import { createClient } from '@supabase/supabase-js';
import type { Database } from '../types/supabase';

// 从环境变量或配置文件中获取Supabase URL和匿名密钥
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://tthwlrwhsjefxovgqlfj.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR0aHdscndoc2plZnhvdmdxbGZqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTgyMDgsImV4cCI6MjA2NDA3NDIwOH0.Ej8rNkGUqQOLHGJmNVSNGBYzJhI7wI8fVhkDZQGqKQE';

// 创建Supabase客户端实例
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// 辅助函数：检查用户是否已登录
export const isAuthenticated = async () => {
  const { data } = await supabase.auth.getSession();
  return !!data.session;
};

// 辅助函数：获取当前用户
export const getCurrentUser = async () => {
  const { data } = await supabase.auth.getUser();
  return data.user;
};

// 辅助函数：登出
export const signOut = async () => {
  return await supabase.auth.signOut();
};