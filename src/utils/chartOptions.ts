import type { EChartsOption } from 'echarts'

// 线性图表配置
export const createLineChartOption = (data: {
  xAxisData: string[]
  series: Array<{
    name: string
    data: number[]
    color?: string
  }>
  title?: string
}): EChartsOption => {
  return {
    title: {
      text: data.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: data.series?.map(s => s.name) || [],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: data.series?.map(item => ({
      name: item.name,
      type: 'line',
      smooth: true,
      data: item.data,
      lineStyle: {
        color: item.color || '#1890ff'
      },
      itemStyle: {
        color: item.color || '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: item.color ? `${item.color}40` : '#1890ff40'
            },
            {
              offset: 1,
              color: item.color ? `${item.color}10` : '#1890ff10'
            }
          ]
        }
      }
    }))
  }
}

// 柱状图配置
export const createBarChartOption = (data: {
  xAxisData: string[]
  series: Array<{
    name: string
    data: number[]
    color?: string
  }>
  title?: string
}): EChartsOption => {
  return {
    title: {
      text: data.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: data.series?.map(s => s.name) || [],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: data.series?.map(item => ({
      name: item.name,
      type: 'bar',
      data: item.data,
      itemStyle: {
        color: item.color || '#1890ff',
        borderRadius: [4, 4, 0, 0]
      }
    }))
  }
}

// 饼图配置
export const createPieChartOption = (data: {
  data: Array<{
    name: string
    value: number
    color?: string
  }>
  title?: string
}): EChartsOption => {
  return {
    title: {
      text: data.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.data?.map(item => item.name) || []
    },
    series: [
      {
        name: '数据分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data.data?.map((item, index) => ({
          ...item,
          itemStyle: {
            color: item.color || `hsl(${index * 45}, 70%, 60%)`
          }
        }))
      }
    ]
  }
}

// 仪表盘配置
export const createGaugeChartOption = (data: {
  value: number
  max?: number
  title?: string
  unit?: string
}): EChartsOption => {
  const { value, max = 100, title = '', unit = '%' } = data
  
  return {
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    series: [
      {
        name: title,
        type: 'gauge',
        progress: {
          show: true,
          width: 18
        },
        axisLine: {
          lineStyle: {
            width: 18
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          length: 15,
          lineStyle: {
            width: 2,
            color: '#999'
          }
        },
        axisLabel: {
          distance: 25,
          color: '#999',
          fontSize: 12
        },
        anchor: {
          show: true,
          showAbove: true,
          size: 25,
          itemStyle: {
            borderWidth: 10
          }
        },
        title: {
          show: false
        },
        detail: {
          valueAnimation: true,
          formatter: `{value}${unit}`,
          color: 'inherit'
        },
        data: [
          {
            value: value,
            name: title
          }
        ],
        max: max
      }
    ]
  }
}

// 雷达图配置
export const createRadarChartOption = (data: {
  indicator: Array<{
    name: string
    max: number
  }>
  series: Array<{
    name: string
    value: number[]
    color?: string
  }>
  title?: string
}): EChartsOption => {
  return {
    title: {
      text: data.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      data: data.series?.map(s => s.name) || [],
      bottom: 0
    },
    radar: {
      indicator: data.indicator,
      radius: '60%',
      splitNumber: 5,
      splitArea: {
        areaStyle: {
          color: ['rgba(114, 172, 209, 0.2)', 'rgba(114, 172, 209, 0.4)', 'rgba(114, 172, 209, 0.6)', 'rgba(114, 172, 209, 0.8)', 'rgba(114, 172, 209, 1)'].reverse()
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(114, 172, 209, 0.6)'
        }
      }
    },
    series: [
      {
        name: '数据对比',
        type: 'radar',
        data: data.series?.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color || '#1890ff'
          },
          areaStyle: {
            color: item.color ? `${item.color}40` : '#1890ff40'
          }
        }))
      }
    ]
  }
}