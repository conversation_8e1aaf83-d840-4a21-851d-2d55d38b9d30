export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          username: string
          email: string
          phone: string | null
          join_date: string
          last_active: string | null
          level: string
          flow_value: number
          status: 'active' | 'inactive' | 'banned'
          is_premium: boolean
          streak: number
          posts_count: number
          checkins_count: number
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          username: string
          email: string
          phone?: string | null
          join_date?: string
          last_active?: string | null
          level?: string
          flow_value?: number
          status?: 'active' | 'inactive' | 'banned'
          is_premium?: boolean
          streak?: number
          posts_count?: number
          checkins_count?: number
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          username?: string
          email?: string
          phone?: string | null
          join_date?: string
          last_active?: string | null
          level?: string
          flow_value?: number
          status?: 'active' | 'inactive' | 'banned'
          is_premium?: boolean
          streak?: number
          posts_count?: number
          checkins_count?: number
          created_at?: string
          updated_at?: string | null
        }
      }
      profiles: {
        Row: {
          id: string
          username: string | null
          email: string
          phone: string | null
          avatar_url: string | null
          role: string
          level: number | null
          days: number | null
          points: number | null
          status: 'active' | 'inactive' | 'banned'
          is_premium: boolean
          streak: number | null
          posts_count: number | null
          checkins_count: number | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          username?: string | null
          email: string
          phone?: string | null
          avatar_url?: string | null
          role?: string
          level?: number | null
          days?: number | null
          points?: number | null
          status?: 'active' | 'inactive' | 'banned'
          is_premium?: boolean
          streak?: number | null
          posts_count?: number | null
          checkins_count?: number | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          username?: string | null
          email?: string
          phone?: string | null
          avatar_url?: string | null
          role?: string
          level?: number | null
          days?: number | null
          points?: number | null
          status?: 'active' | 'inactive' | 'banned'
          is_premium?: boolean
          streak?: number | null
          posts_count?: number | null
          checkins_count?: number | null
          created_at?: string
          updated_at?: string | null
        }
      }
      posts: {
        Row: {
          id: string
          title: string
          content: string
          user_id: string
          status: 'draft' | 'published' | 'archived'
          view_count: number
          like_count: number
          comment_count: number
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          title: string
          content: string
          user_id: string
          status?: 'draft' | 'published' | 'archived'
          view_count?: number
          like_count?: number
          comment_count?: number
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          title?: string
          content?: string
          user_id?: string
          status?: 'draft' | 'published' | 'archived'
          view_count?: number
          like_count?: number
          comment_count?: number
          created_at?: string
          updated_at?: string | null
        }
      }
      checkins: {
        Row: {
          id: string
          user_id: string
          checkin_date: string
          content: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          checkin_date?: string
          content?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          checkin_date?: string
          content?: string | null
          created_at?: string
        }
      }
      achievements: {
        Row: {
          id: string
          name: string
          description: string
          icon: string | null
          points: number
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          icon?: string | null
          points?: number
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          icon?: string | null
          points?: number
          created_at?: string
        }
      }
      user_achievements: {
        Row: {
          id: string
          user_id: string
          achievement_id: string
          achieved_at: string
        }
        Insert: {
          id?: string
          user_id: string
          achievement_id: string
          achieved_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          achievement_id?: string
          achieved_at?: string
        }
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string
          points: number
          status: 'active' | 'inactive'
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          title: string
          description: string
          points?: number
          status?: 'active' | 'inactive'
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          title?: string
          description?: string
          points?: number
          status?: 'active' | 'inactive'
          created_at?: string
          updated_at?: string | null
        }
      }
      user_tasks: {
        Row: {
          id: string
          user_id: string
          task_id: string
          status: 'pending' | 'completed' | 'failed'
          completed_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          task_id: string
          status?: 'pending' | 'completed' | 'failed'
          completed_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          task_id?: string
          status?: 'pending' | 'completed' | 'failed'
          completed_at?: string | null
          created_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          title: string
          content: string
          type: 'system' | 'achievement' | 'task' | 'user'
          is_read: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          content: string
          type?: 'system' | 'achievement' | 'task' | 'user'
          is_read?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          content?: string
          type?: 'system' | 'achievement' | 'task' | 'user'
          is_read?: boolean
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}