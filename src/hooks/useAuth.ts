import { useEffect } from 'react'
import { useUserStore } from '../store/userStore'

export const useAuth = () => {
  const { 
    user,
    isLoggedIn, 
    isLoading, 
    error,
    login, 
    signup,
    logout, 
    refreshUser 
  } = useUserStore()

  useEffect(() => {
    // 检查认证状态并刷新用户信息
    const checkAuthStatus = async () => {
      await refreshUser()
    }

    checkAuthStatus()
  }, [])

  return {
    user,
    isLoading,
    isLoggedIn,
    error,
    login,
    signup,
    logout
  }
}