import { useUserStore } from '../store/userStore'

/**
 * 权限检查 Hook
 * 提供便捷的权限检查方法
 */
export const usePermissions = () => {
  const { hasRole, hasPermission, canAccessArticle, userRoles, userPermissions } = useUserStore()

  return {
    // 角色检查
    hasRole,
    
    // 权限检查
    hasPermission,
    
    // 文章访问权限检查
    canAccessArticle,
    
    // 获取用户角色列表
    userRoles,
    
    // 获取用户权限列表
    userPermissions,
    
    // 便捷的权限检查方法
    canRead: (resource: string) => hasPermission(`${resource}.read`),
    canWrite: (resource: string) => hasPermission(`${resource}.write`),
    canDelete: (resource: string) => hasPermission(`${resource}.delete`),
    canManage: (resource: string) => hasPermission(`${resource}.manage`),
    
    // 角色检查便捷方法
    isAdmin: () => hasRole('ADMIN'),
    isModerator: () => hasRole('MODERATOR'),
    isPremium: () => hasRole('PREMIUM_USER'),
    isVip: () => hasRole('VIP_USER'),
    
    // 组合权限检查
    canAccessAdminPanel: () => hasRole('ADMIN') || hasRole('MODERATOR'),
    canManageUsers: () => hasPermission('users.manage'),
    canManageArticles: () => hasPermission('articles.manage'),
    canManageOrders: () => hasPermission('orders.manage'),
    canViewAnalytics: () => hasPermission('analytics.read'),
  }
}

export default usePermissions