import { useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { usePermissions } from './usePermissions'
import { navigationConfig, type NavigationItem } from '../config/navigation'

/**
 * 导航菜单 Hook
 * 根据用户权限过滤和生成导航菜单
 */
export const useNavigation = () => {
  const { hasRole, hasPermission } = usePermissions()
  const navigate = useNavigate()

  /**
   * 检查用户是否有权限访问某个菜单项
   */
  const hasAccessToItem = (item: NavigationItem): boolean => {
    // 检查角色权限
    if (item.requiredRoles && item.requiredRoles.length > 0) {
      const hasRequiredRoles = item.requireAllRoles
        ? item.requiredRoles.every(role => hasRole(role))
        : item.requiredRoles.some(role => hasRole(role))
      
      if (!hasRequiredRoles) {
        return false
      }
    }

    // 检查操作权限
    if (item.requiredPermissions && item.requiredPermissions.length > 0) {
      const hasRequiredPermissions = item.requireAllPermissions
        ? item.requiredPermissions.every(permission => hasPermission(permission))
        : item.requiredPermissions.some(permission => hasPermission(permission))
      
      if (!hasRequiredPermissions) {
        return false
      }
    }

    return true
  }

  /**
   * 递归过滤导航菜单项
   */
  const filterNavigationItems = (items: NavigationItem[]): NavigationItem[] => {
    return items
      .filter(item => {
        // 过滤隐藏的菜单项
        if (item.hidden) {
          return false
        }
        
        // 检查权限
        if (!hasAccessToItem(item)) {
          return false
        }

        // 如果有子菜单，递归过滤子菜单
        if (item.children && item.children.length > 0) {
          const filteredChildren = filterNavigationItems(item.children)
          // 如果所有子菜单都被过滤掉了，则隐藏父菜单
          if (filteredChildren.length === 0) {
            return false
          }
          // 更新子菜单
          item.children = filteredChildren
        }

        return true
      })
      .sort((a, b) => (a.order || 0) - (b.order || 0)) // 按order排序
  }

  /**
   * 获取过滤后的导航菜单
   */
  const filteredNavigation = useMemo(() => {
    return filterNavigationItems([...navigationConfig])
  }, [hasRole, hasPermission])

  /**
   * 将导航配置转换为Ant Design Menu组件需要的格式
   */
  const getMenuItems = (items: NavigationItem[], navigate?: (path: string) => void): any[] => {
    return items.map(item => ({
      key: item.path || item.key, // 使用path作为key，确保选中状态正确
      icon: item.icon,
      label: item.label,
      onClick: item.path && navigate ? () => navigate(item.path) : undefined,
      children: item.children ? getMenuItems(item.children, navigate) : undefined,
    }))
  }

  /**
   * 获取Ant Design Menu格式的菜单项
   */
  const menuItems = useMemo(() => {
    return getMenuItems(filteredNavigation, navigate)
  }, [filteredNavigation, navigate])

  /**
   * 根据路径查找菜单项
   */
  const findMenuItemByPath = (path: string, items: NavigationItem[] = filteredNavigation): NavigationItem | null => {
    for (const item of items) {
      if (item.path === path) {
        return item
      }
      if (item.children) {
        const found = findMenuItemByPath(path, item.children)
        if (found) {
          return found
        }
      }
    }
    return null
  }

  /**
   * 获取面包屑导航
   */
  const getBreadcrumbs = (currentPath: string): NavigationItem[] => {
    const breadcrumbs: NavigationItem[] = []
    
    const findPath = (path: string, items: NavigationItem[], parents: NavigationItem[] = []): boolean => {
      for (const item of items) {
        const currentParents = [...parents, item]
        
        if (item.path === path) {
          breadcrumbs.push(...currentParents)
          return true
        }
        
        if (item.children && findPath(path, item.children, currentParents)) {
          return true
        }
      }
      return false
    }
    
    findPath(currentPath, filteredNavigation)
    return breadcrumbs
  }

  /**
   * 检查路径是否可访问
   */
  const isPathAccessible = (path: string): boolean => {
    return findMenuItemByPath(path) !== null
  }

  /**
   * 获取分组菜单
   */
  const getGroupedNavigation = () => {
    const grouped: Record<string, NavigationItem[]> = {}
    
    filteredNavigation.forEach(item => {
      const group = item.group || 'default'
      if (!grouped[group]) {
        grouped[group] = []
      }
      grouped[group].push(item)
    })
    
    return grouped
  }

  return {
    // 原始导航配置
    navigationConfig: filteredNavigation,
    // Ant Design Menu格式的菜单项
    menuItems,
    // 工具方法
    hasAccessToItem,
    findMenuItemByPath,
    getBreadcrumbs,
    isPathAccessible,
    getGroupedNavigation,
  }
}

export default useNavigation