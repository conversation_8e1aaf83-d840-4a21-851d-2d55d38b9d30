import { create } from 'zustand'
import { supabase } from '../utils/supabase'
import type { User } from '@supabase/supabase-js'

interface UserProfile {
  id: string
  username: string | null
  avatar_url: string | null
  role: string
  level: string | null
  flow_value: number | null
  streak: number | null
  created_at: string
  updated_at: string | null
  is_premium?: boolean
  membership_type?: string
}

interface UserRole {
  role: string
  expires_at: string | null
}

interface UserPermission {
  permission: string
  resource_pattern: string | null
  role: string
}

interface UserState {
  user: User | null
  profile: UserProfile | null
  name: string
  email: string
  role: string
  userRoles: UserRole[]
  userPermissions: UserPermission[]
  isLoggedIn: boolean
  isLoading: boolean
  error: Error | null
  login: (email: string, password: string) => Promise<{ success: boolean; error?: Error }>
  signup: (email: string, password: string) => Promise<{ success: boolean; error?: Error }>
  logout: () => Promise<{ success: boolean; error?: Error }>
  updateProfile: (data: Partial<{ name: string; email: string }>) => Promise<{ success: boolean; error?: Error }>
  refreshUser: () => Promise<void>
  isAdmin: () => boolean
  hasRole: (role: string) => boolean
  hasPermission: (permission: string, resourcePattern?: string) => boolean
  canAccessArticle: (articleId: string, articleType?: string) => boolean
}

export const useUserStore = create<UserState>((set, get) => ({
  user: null,
  profile: null,
  name: '',
  email: '',
  role: 'USER',
  userRoles: [],
  userPermissions: [],
  isLoggedIn: false,
  isLoading: false,
  error: null,
  
  login: async (email, password) => {
    try {
      set({ isLoading: true, error: null })
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      const user = data.user
      
      // 获取用户信息（profiles 表）
      let profile: UserProfile | null = null
      let userRoles: UserRole[] = []
      let userPermissions: UserPermission[] = []
      
      if (user) {
        const { data: profileRow } = await (supabase as any)
          .from('profiles')
          .select('id, username, avatar_url, role, level, flow_value, streak_days, created_at, updated_at, is_premium, membership_type')
          .eq('id', user.id)
          .single()
        if (profileRow) {
          profile = {
            id: profileRow.id,
            username: profileRow.username ?? null,
            avatar_url: profileRow.avatar_url ?? null,
            role: profileRow.role ?? 'USER',
            level: profileRow.level?.toString() ?? null,
            flow_value: profileRow.flow_value ?? null,
            streak: profileRow.streak_days ?? null,
            created_at: profileRow.created_at,
            updated_at: profileRow.updated_at ?? profileRow.created_at,
            is_premium: profileRow.is_premium ?? false,
            membership_type: profileRow.membership_type ?? 'USER',
          }
        }
        
        // 获取用户角色
        const { data: rolesData } = await (supabase as any)
          .from('user_roles')
          .select('role, expires_at')
          .eq('user_id', user.id)
          .eq('is_active', true)
        
        if (rolesData) {
          userRoles = rolesData.filter((r: any) => !r.expires_at || new Date(r.expires_at) > new Date())
        }
        
        // 获取用户权限
        const { data: permissionsData } = await (supabase as any)
          .rpc('get_user_permissions', { user_id: user.id })
        
        if (permissionsData) {
          userPermissions = permissionsData
        }
      }
      
      set({ 
        user,
        profile,
        email: user?.email || '', 
        isLoggedIn: true,
        name: profile?.username || user?.user_metadata?.name || email.split('@')[0],
        role: profile?.role || 'USER',
        userRoles,
        userPermissions,
        isLoading: false
      })
      return { success: true }
    } catch (error) {
      set({ isLoading: false, error: error as Error })
      return { success: false, error: error as Error }
    }
  },
  
  signup: async (email, password) => {
    try {
      set({ isLoading: true, error: null })
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      })

      if (error) throw error

      const user = data.user
      set({ 
        user,
        email: user?.email || '', 
        isLoggedIn: !!data.session,
        name: user?.user_metadata?.name || email.split('@')[0],
        isLoading: false
      })
      return { success: true }
    } catch (error) {
      set({ isLoading: false, error: error as Error })
      return { success: false, error: error as Error }
    }
  },
  
  logout: async () => {
    try {
      set({ isLoading: true, error: null })
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      
      set({ user: null, profile: null, name: '', email: '', role: 'USER', userRoles: [], userPermissions: [], isLoggedIn: false, isLoading: false })
      return { success: true }
    } catch (error) {
      set({ isLoading: false, error: error as Error })
      return { success: false, error: error as Error }
    }
  },
  
  updateProfile: async (data) => {
    try {
      set({ isLoading: true, error: null })
      
      // 更新Supabase用户元数据
      const { error } = await supabase.auth.updateUser({
        data: { name: data.name }
      })
      
      if (error) throw error
      
      set((state) => ({ 
        ...state, 
        ...data,
        isLoading: false
      }))
      
      return { success: true }
    } catch (error) {
      set({ isLoading: false, error: error as Error })
      return { success: false, error: error as Error }
    }
  },
  
  refreshUser: async () => {
    const { data } = await supabase.auth.getUser()
    const user = data.user
    
    if (user) {
      // 获取用户信息（profiles 表）
      const { data: profileRow } = await (supabase as any)
        .from('profiles')
        .select('id, username, avatar_url, role, level, flow_value, streak_days, created_at, updated_at, is_premium, membership_type')
        .eq('id', user.id)
        .single()
      
      const profile: UserProfile | null = profileRow ? {
        id: profileRow.id,
        username: profileRow.username ?? null,
        avatar_url: profileRow.avatar_url ?? null,
        role: profileRow.role ?? 'USER',
        level: profileRow.level?.toString() ?? null,
        flow_value: profileRow.flow_value ?? null,
        streak: profileRow.streak_days ?? null,
        created_at: profileRow.created_at,
        updated_at: profileRow.updated_at ?? profileRow.created_at,
        is_premium: profileRow.is_premium ?? false,
        membership_type: profileRow.membership_type ?? 'USER',
      } : null
      
      // 获取用户角色
      let userRoles: UserRole[] = []
      const { data: rolesData } = await (supabase as any)
        .from('user_roles')
        .select('role, expires_at')
        .eq('user_id', user.id)
        .eq('is_active', true)
      
      if (rolesData) {
        userRoles = rolesData.filter((r: any) => !r.expires_at || new Date(r.expires_at) > new Date())
      }
      
      // 获取用户权限
      let userPermissions: UserPermission[] = []
      const { data: permissionsData } = await (supabase as any)
        .rpc('get_user_permissions', { user_id: user.id })
      
      if (permissionsData) {
        userPermissions = permissionsData
      }
      
      set({ 
        user,
        profile: profile,
        email: user.email || '', 
        isLoggedIn: true,
        name: profile?.username || user.user_metadata?.name || user.email?.split('@')[0] || '',
        role: profile?.role || 'USER',
        userRoles,
        userPermissions,
      })
    }
  },
  
  isAdmin: () => {
    const state = get()
    return state.role === 'ADMIN' || state.role === 'SUPER_ADMIN'
  },

  hasRole: (role: string) => {
    const state = get()
    return state.userRoles.some(r => r.role === role && (!r.expires_at || new Date(r.expires_at) > new Date()))
  },

  hasPermission: (permission: string, resourcePattern?: string) => {
    const state = get()
    return state.userPermissions.some(p => 
      p.permission === permission && 
      (!resourcePattern || !p.resource_pattern || p.resource_pattern === resourcePattern)
    )
  },

  canAccessArticle: (articleId: string, articleType?: string) => {
    const state = get()
    // 管理员可以访问所有文章
    if (state.hasPermission('articles.read')) {
      return true
    }
    // 免费文章所有人可访问
    if (articleType === 'FREE') {
      return true
    }
    // 付费文章需要会员权限或购买记录
    if (articleType === 'PAID') {
      return state.profile?.is_premium || ['PREMIUM', 'VIP'].includes(state.profile?.membership_type || '')
    }
    return false
  },
}))