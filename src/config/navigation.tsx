import React from 'react'
import {
  DashboardOutlined,
  UserOutlined,
  FileTextOutlined,
  CheckSquareOutlined,
  TrophyOutlined,
  FileDoneOutlined,
  TeamOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  SolutionOutlined,
  BellOutlined,
  GiftOutlined,
  CommentOutlined,
  AuditOutlined,
  HeartOutlined,
  CrownOutlined,
  ExportOutlined,
  PayCircleOutlined,
  PartitionOutlined,
  BookOutlined,
  FlagOutlined,
  AppstoreOutlined,
  ShopOutlined,
  LineChartOutlined,
  SafetyOutlined,
} from '@ant-design/icons'

export interface NavigationItem {
  key: string
  path: string
  icon: React.ReactNode
  label: string
  requiredRoles?: string[]
  requiredPermissions?: string[]
  requireAllRoles?: boolean
  requireAllPermissions?: boolean
  children?: NavigationItem[]
  group?: string
  order?: number
  hidden?: boolean
}

export const navigationConfig: NavigationItem[] = [
  {
    key: 'dashboard',
    path: '/',
    icon: <DashboardOutlined />,
    label: '仪表盘',
    order: 1,
  },
  {
    key: 'users',
    path: '/users',
    icon: <UserOutlined />,
    label: '用户管理',
    group: 'management',
    order: 2,
    children: [
      {
        key: 'user-list',
        path: '/users',
        icon: <UserOutlined />,
        label: '用户列表',
        order: 1,
      },
      {
        key: 'user-create',
        path: '/users/create',
        icon: <UserOutlined />,
        label: '创建用户',
        order: 2,
        hidden: true,
      },
    ],
  },

  {
    key: 'content',
    path: '',
    icon: <FileTextOutlined />,
    label: '内容管理',
    group: 'management',
    order: 3,
    children: [
      {
        key: 'articles',
        path: '/articles',
        icon: <BookOutlined />,
        label: '文章管理',
        order: 1,
      },
      {
        key: 'content-posts',
        path: '/content',
        icon: <FileTextOutlined />,
        label: '内容发布',
        order: 2,
      },
      {
        key: 'content-library',
        path: '/content-library',
        icon: <AppstoreOutlined />,
        label: '内容库管理',
        order: 3,
      },
    ],
  },

  {
    key: 'community',
    path: '/community',
    icon: <TeamOutlined />,
    label: '社区管理',
    group: 'management',
    order: 4,
  },

  {
    key: 'engagement',
    path: '',
    icon: <TrophyOutlined />,
    label: '用户参与',
    group: 'engagement',
    order: 5,
    children: [
      {
        key: 'checkin',
        path: '/checkin',
        icon: <CheckSquareOutlined />,
        label: '打卡系统',
        order: 1,
      },
      {
        key: 'tasks',
        path: '/tasks',
        icon: <FileDoneOutlined />,
        label: '任务系统',
        order: 2,
      },
      {
        key: 'challenges',
        path: '/challenges',
        icon: <SolutionOutlined />,
        label: '挑战管理',
        order: 3,
      },
      {
        key: 'achievements',
        path: '/achievements',
        icon: <TrophyOutlined />,
        label: '成就系统',
        order: 4,
      },
      {
        key: 'milestones',
        path: '/milestones',
        icon: <FlagOutlined />,
        label: '里程碑',
        order: 5,
      },
    ],
  },

  {
    key: 'analytics',
    path: '',
    icon: <BarChartOutlined />,
    label: '数据分析',
    group: 'analytics',
    order: 6,
    children: [
      {
        key: 'analytics-overview',
        path: '/analytics',
        icon: <LineChartOutlined />,
        label: '数据统计',
        order: 1,
      },
      {
        key: 'export',
        path: '/export',
        icon: <ExportOutlined />,
        label: '数据导出',
        order: 2,
      },
    ],
  },

  {
    key: 'operations',
    path: '',
    icon: <BellOutlined />,
    label: '运营工具',
    group: 'operations',
    order: 7,
    children: [
      {
        key: 'notifications',
        path: '/notifications',
        icon: <BellOutlined />,
        label: '通知管理',
        order: 1,
      },
      {
        key: 'invites',
        path: '/invites',
        icon: <GiftOutlined />,
        label: '邀请管理',
        order: 2,
      },
      {
        key: 'flow',
        path: '/flow',
        icon: <PartitionOutlined />,
        label: '心流值管理',
        order: 3,
      },
    ],
  },

  {
    key: 'business',
    path: '',
    icon: <ShopOutlined />,
    label: '商业功能',
    group: 'business',
    order: 8,
    children: [
      {
        key: 'appreciation',
        path: '/appreciation',
        icon: <HeartOutlined />,
        label: '赞赏管理',
        order: 1,
      },
      {
        key: 'subscription',
        path: '/subscription',
        icon: <CrownOutlined />,
        label: '订阅管理',
        order: 2,
      },
      {
        key: 'partner',
        path: '/partner',
        icon: <TeamOutlined />,
        label: '合伙人管理',
        order: 3,
      },
      {
        key: 'payment',
        path: '/payment',
        icon: <PayCircleOutlined />,
        label: '支付统计',
        order: 4,
      },
    ],
  },

  {
    key: 'admin',
    path: '',
    icon: <SafetyOutlined />,
    label: '系统管理',
    group: 'admin',
    order: 9,
    children: [
      {
        key: 'admin-panel',
        path: '/admin',
        icon: <AuditOutlined />,
        label: '权限管理',
        order: 1,
      },
      {
        key: 'logs',
        path: '/logs',
        icon: <CommentOutlined />,
        label: '操作日志',
        order: 2,
      },
      {
        key: 'settings',
        path: '/settings',
        icon: <SettingOutlined />,
        label: '系统设置',
        order: 3,
      },
    ],
  },
]

export const menuGroups = {
  core: {
    title: '核心功能',
    order: 1,
  },
  management: {
    title: '管理功能',
    order: 2,
  },
  engagement: {
    title: '用户参与',
    order: 3,
  },
  analytics: {
    title: '数据分析',
    order: 4,
  },
  operations: {
    title: '运营工具',
    order: 5,
  },
  business: {
    title: '商业功能',
    order: 6,
  },
  admin: {
    title: '系统管理',
    order: 7,
  },
}

export default navigationConfig