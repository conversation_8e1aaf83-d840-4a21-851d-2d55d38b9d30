import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  message,
  Popconfirm,
  Tag,
  Tabs,
  Statistic,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Tooltip
} from 'antd'
import {
  DeleteOutlined,
  ReloadOutlined,
  SearchOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  TrophyOutlined,
  CalendarOutlined
} from '@ant-design/icons'
import { flowService } from '../../services/flowService'
import type { FlowBehaviorRecord, FlowDailyRecord } from '../../services/flowService'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { TabPane } = Tabs
const { RangePicker } = DatePicker
const { Option } = Select
const { Search } = Input

const FlowRecords: React.FC = () => {
  const [behaviorRecords, setBehaviorRecords] = useState<FlowBehaviorRecord[]>([])
  const [dailyRecords, setDailyRecords] = useState<FlowDailyRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [overview, setOverview] = useState<any>({})
  const [activeTab, setActiveTab] = useState('behavior')
  const [searchText, setSearchText] = useState('')
  const [behaviorTypeFilter, setBehaviorTypeFilter] = useState<string | undefined>()
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null)

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      const [behaviorResult, dailyResult, overviewResult] = await Promise.all([
        flowService.getFlowBehaviorRecords(),
        flowService.getFlowDailyRecords(),
        flowService.getFlowOverview()
      ])

      if (behaviorResult.success) {
        setBehaviorRecords(behaviorResult.data || [])
      } else {
        message.error(behaviorResult.error || '加载心流行为记录失败')
      }

      if (dailyResult.success) {
        setDailyRecords(dailyResult.data || [])
      } else {
        message.error(dailyResult.error || '加载每日心流记录失败')
      }

      if (overviewResult.success) {
        setOverview(overviewResult.data || {})
      }
    } catch (error) {
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  // 删除心流行为记录
  const handleDeleteBehaviorRecord = async (id: string) => {
    try {
      const result = await flowService.deleteFlowBehaviorRecord(id)
      if (result.success) {
        message.success('删除成功')
        loadData()
      } else {
        message.error(result.error || '删除失败')
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 删除每日心流记录
  const handleDeleteDailyRecord = async (id: string) => {
    try {
      const result = await flowService.deleteFlowDailyRecord(id)
      if (result.success) {
        message.success('删除成功')
        loadData()
      } else {
        message.error(result.error || '删除失败')
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 获取行为类型颜色
  const getBehaviorTypeColor = (type: string) => {
    const colors: { [key: string]: string } = {
      'focus': 'blue',
      'meditation': 'green',
      'exercise': 'orange',
      'learning': 'purple',
      'creative': 'cyan',
      'social': 'magenta',
      'rest': 'gray'
    }
    return colors[type] || 'default'
  }

  // 获取心流影响等级
  const getFlowImpactLevel = (impact: number) => {
    if (impact >= 80) return { text: '极高', color: 'red' }
    if (impact >= 60) return { text: '高', color: 'orange' }
    if (impact >= 40) return { text: '中等', color: 'blue' }
    if (impact >= 20) return { text: '低', color: 'green' }
    return { text: '极低', color: 'gray' }
  }

  // 心流行为记录表格列
  const behaviorColumns: ColumnsType<FlowBehaviorRecord> = [
    {
      title: '用户',
      dataIndex: ['user', 'username'],
      key: 'username',
      width: 120,
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value, record) => {
        const username = record.user?.username || ''
        return username.toLowerCase().includes((value as string).toLowerCase())
      }
    },
    {
      title: '行为类型',
      dataIndex: 'behavior_type',
      key: 'behavior_type',
      width: 120,
      filteredValue: behaviorTypeFilter ? [behaviorTypeFilter] : null,
      onFilter: (value, record) => record.behavior_type === value,
      render: (type: string) => (
        <Tag color={getBehaviorTypeColor(type)}>
          {type}
        </Tag>
      )
    },
    {
      title: '行为值',
      dataIndex: 'behavior_value',
      key: 'behavior_value',
      width: 100,
      sorter: (a, b) => a.behavior_value - b.behavior_value
    },
    {
      title: '心流影响',
      dataIndex: 'flow_impact',
      key: 'flow_impact',
      width: 120,
      sorter: (a, b) => a.flow_impact - b.flow_impact,
      render: (impact: number) => {
        const level = getFlowImpactLevel(impact)
        return (
          <Tooltip title={`影响值: ${impact}`}>
            <Tag color={level.color}>{level.text}</Tag>
          </Tooltip>
        )
      }
    },
    {
      title: '记录时间',
      dataIndex: 'recorded_at',
      key: 'recorded_at',
      width: 180,
      sorter: (a, b) => dayjs(a.recorded_at).unix() - dayjs(b.recorded_at).unix(),
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDeleteBehaviorRecord(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              size="small"
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 每日心流记录表格列
  const dailyColumns: ColumnsType<FlowDailyRecord> = [
    {
      title: '用户',
      dataIndex: ['user', 'username'],
      key: 'username',
      width: 120,
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value, record) => {
        const username = record.user?.username || ''
        return username.toLowerCase().includes((value as string).toLowerCase())
      }
    },
    {
      title: '记录日期',
      dataIndex: 'record_date',
      key: 'record_date',
      width: 120,
      sorter: (a, b) => dayjs(a.record_date).unix() - dayjs(b.record_date).unix(),
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '上午能量',
      dataIndex: 'morning_energy',
      key: 'morning_energy',
      width: 100,
      sorter: (a, b) => a.morning_energy - b.morning_energy,
      render: (energy: number) => (
        <Tag color={energy >= 80 ? 'red' : energy >= 60 ? 'orange' : energy >= 40 ? 'blue' : 'green'}>
          {energy}
        </Tag>
      )
    },
    {
      title: '下午能量',
      dataIndex: 'afternoon_energy',
      key: 'afternoon_energy',
      width: 100,
      sorter: (a, b) => a.afternoon_energy - b.afternoon_energy,
      render: (energy: number) => (
        <Tag color={energy >= 80 ? 'red' : energy >= 60 ? 'orange' : energy >= 40 ? 'blue' : 'green'}>
          {energy}
        </Tag>
      )
    },
    {
      title: '晚上能量',
      dataIndex: 'evening_energy',
      key: 'evening_energy',
      width: 100,
      sorter: (a, b) => a.evening_energy - b.evening_energy,
      render: (energy: number) => (
        <Tag color={energy >= 80 ? 'red' : energy >= 60 ? 'orange' : energy >= 40 ? 'blue' : 'green'}>
          {energy}
        </Tag>
      )
    },
    {
      title: '每日心流分数',
      dataIndex: 'daily_flow_score',
      key: 'daily_flow_score',
      width: 130,
      sorter: (a, b) => a.daily_flow_score - b.daily_flow_score,
      render: (score: number) => {
        const level = getFlowImpactLevel(score)
        return (
          <Tooltip title={`心流分数: ${score}`}>
            <Tag color={level.color}>{score}</Tag>
          </Tooltip>
        )
      }
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      width: 200,
      ellipsis: true,
      render: (notes: string) => notes || '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDeleteDailyRecord(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              size="small"
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 获取唯一的行为类型列表
  const behaviorTypes = Array.from(new Set(behaviorRecords.map(record => record.behavior_type)))

  return (
    <div className="flow-records">
      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总行为记录数"
              value={overview.totalBehaviorRecords || 0}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总每日记录数"
              value={overview.totalDailyRecords || 0}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日新增行为记录"
              value={overview.todayBehaviorRecords || 0}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均心流分数"
              value={overview.avgFlowScore || 0}
              precision={2}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={loadData}
              loading={loading}
            >
              刷新
            </Button>
            <Search
              placeholder="搜索用户名"
              allowClear
              style={{ width: 200 }}
              onSearch={setSearchText}
              onChange={(e) => !e.target.value && setSearchText('')}
            />
            {activeTab === 'behavior' && (
              <Select
                placeholder="筛选行为类型"
                allowClear
                style={{ width: 150 }}
                value={behaviorTypeFilter}
                onChange={setBehaviorTypeFilter}
              >
                {behaviorTypes.map(type => (
                  <Option key={type} value={type}>
                    <Tag color={getBehaviorTypeColor(type)} style={{ margin: 0 }}>
                      {type}
                    </Tag>
                  </Option>
                ))}
              </Select>
            )}
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="心流行为记录" key="behavior">
            <Table
              columns={behaviorColumns}
              dataSource={behaviorRecords}
              rowKey="id"
              loading={loading}
              pagination={{
                total: behaviorRecords.length,
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
              scroll={{ x: 1000 }}
            />
          </TabPane>
          <TabPane tab="每日心流记录" key="daily">
            <Table
              columns={dailyColumns}
              dataSource={dailyRecords}
              rowKey="id"
              loading={loading}
              pagination={{
                total: dailyRecords.length,
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
              scroll={{ x: 1200 }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default FlowRecords