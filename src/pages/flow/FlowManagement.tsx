import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Statistic,
  Progress,
  Alert,
  Typography,
  Divider,
  Tooltip,
  Badge,
  message,
  Popconfirm,
  Switch,
  Slider,
  Timeline,
  Tabs
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  SettingOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  LineC<PERSON>Outlined,
  UserOutlined,
  CalendarOutlined,
  ThunderboltOutlined,
  HeartOutlined,
  StarOutlined,
  FireOutlined,
  BulbOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import EChartsComponent from '../../components/EChartsComponent'
import { createLineChartOption, createBarChartOption } from '../../utils/chartOptions'
import { flowService, type FlowBehaviorRecord } from '../../services/flowService'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker
const { TextArea } = Input
const { TabPane } = Tabs

interface FlowUser {
  id: string
  username: string
  email: string
  currentFlowScore: number
  totalFlowTime: number
  flowLevel: string
  lastFlowSession: string
  flowStreak: number
  averageFlowDuration: number
  flowActivities: string[]
  createdAt: string
}

interface FlowAdjustment {
  id: string
  userId: string
  username: string
  oldScore: number
  newScore: number
  adjustment: number
  reason: string
  adminId: string
  adminName: string
  createdAt: string
}

interface FlowSettings {
  baseScorePerMinute: number
  qualityMultiplier: number
  streakBonus: number
  maxDailyScore: number
  decayRate: number
  levelThresholds: Record<string, number>
  activityWeights: Record<string, number>
}

const FlowManagement: React.FC = () => {
  const [form] = Form.useForm()
  const [adjustForm] = Form.useForm()
  const [settingsForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [flowUsers, setFlowUsers] = useState<FlowUser[]>([])
  const [flowRecords, setFlowRecords] = useState<FlowBehaviorRecord[]>([])
  const [flowAdjustments, setFlowAdjustments] = useState<FlowAdjustment[]>([])
  const [flowSettings, setFlowSettings] = useState<FlowSettings | null>(null)
  const [adjustModalVisible, setAdjustModalVisible] = useState(false)
  const [settingsModalVisible, setSettingsModalVisible] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [selectedUser, setSelectedUser] = useState<FlowUser | null>(null)
  const [selectedUserId, setSelectedUserId] = useState<string>('')

  // 心流等级配置
  const flowLevels = [
    { level: 'beginner', name: '初学者', color: '#87d068', threshold: 0 },
    { level: 'intermediate', name: '进阶者', color: '#108ee9', threshold: 1000 },
    { level: 'advanced', name: '高手', color: '#f50', threshold: 5000 },
    { level: 'expert', name: '专家', color: '#722ed1', threshold: 15000 },
    { level: 'master', name: '大师', color: '#eb2f96', threshold: 50000 }
  ]

  // 心流活动类型
  const activityTypes = [
    { value: 'reading', label: '阅读', icon: '📚', weight: 1.0 },
    { value: 'writing', label: '写作', icon: '✍️', weight: 1.2 },
    { value: 'coding', label: '编程', icon: '💻', weight: 1.3 },
    { value: 'learning', label: '学习', icon: '🎓', weight: 1.1 },
    { value: 'meditation', label: '冥想', icon: '🧘', weight: 0.9 },
    { value: 'exercise', label: '运动', icon: '🏃', weight: 0.8 },
    { value: 'creative', label: '创作', icon: '🎨', weight: 1.4 },
    { value: 'music', label: '音乐', icon: '🎵', weight: 1.0 }
  ]

  // 加载心流用户数据
  const loadFlowUsers = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      const mockUsers: FlowUser[] = [
        {
          id: '1',
          username: 'flowmaster',
          email: '<EMAIL>',
          currentFlowScore: 15680,
          totalFlowTime: 2340,
          flowLevel: 'expert',
          lastFlowSession: '2024-12-01 14:30:00',
          flowStreak: 15,
          averageFlowDuration: 45,
          flowActivities: ['coding', 'writing', 'learning'],
          createdAt: '2024-01-15'
        },
        {
          id: '2',
          username: 'zenwriter',
          email: '<EMAIL>',
          currentFlowScore: 8920,
          totalFlowTime: 1560,
          flowLevel: 'advanced',
          lastFlowSession: '2024-12-01 10:15:00',
          flowStreak: 8,
          averageFlowDuration: 38,
          flowActivities: ['writing', 'reading', 'meditation'],
          createdAt: '2024-02-20'
        },
        {
          id: '3',
          username: 'codeflow',
          email: '<EMAIL>',
          currentFlowScore: 3450,
          totalFlowTime: 890,
          flowLevel: 'intermediate',
          lastFlowSession: '2024-11-30 16:45:00',
          flowStreak: 3,
          averageFlowDuration: 32,
          flowActivities: ['coding', 'learning'],
          createdAt: '2024-06-10'
        }
      ]
      setFlowUsers(mockUsers)
    } catch (error) {
      message.error('加载心流用户数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载心流记录
  const loadFlowRecords = async (userId?: string) => {
    try {
      const result = await flowService.getFlowBehaviorRecords()
      if (result.success && result.data) {
        // 如果指定了用户ID，过滤记录
        const filteredRecords = userId ? result.data.filter(record => record.user_id === userId) : result.data
        setFlowRecords(filteredRecords)
      }
    } catch (error) {
      console.error('加载心流记录失败:', error)
    }
  }

  // 加载调整历史
  const loadFlowAdjustments = async () => {
    try {
      // 模拟API调用
      const mockAdjustments: FlowAdjustment[] = [
        {
          id: '1',
          userId: '1',
          username: 'flowmaster',
          oldScore: 15500,
          newScore: 15680,
          adjustment: 180,
          reason: '参与社区活动奖励',
          adminId: 'admin1',
          adminName: '管理员',
          createdAt: '2024-12-01 09:00:00'
        },
        {
          id: '2',
          userId: '2',
          username: 'zenwriter',
          oldScore: 9000,
          newScore: 8920,
          adjustment: -80,
          reason: '违规行为扣分',
          adminId: 'admin1',
          adminName: '管理员',
          createdAt: '2024-11-30 15:30:00'
        }
      ]
      setFlowAdjustments(mockAdjustments)
    } catch (error) {
      message.error('加载调整历史失败')
    }
  }

  // 加载心流设置
  const loadFlowSettings = async () => {
    try {
      // 模拟API调用
      const mockSettings: FlowSettings = {
        baseScorePerMinute: 2,
        qualityMultiplier: 1.5,
        streakBonus: 0.1,
        maxDailyScore: 500,
        decayRate: 0.02,
        levelThresholds: {
          beginner: 0,
          intermediate: 1000,
          advanced: 5000,
          expert: 15000,
          master: 50000
        },
        activityWeights: {
          reading: 1.0,
          writing: 1.2,
          coding: 1.3,
          learning: 1.1,
          meditation: 0.9,
          exercise: 0.8,
          creative: 1.4,
          music: 1.0
        }
      }
      setFlowSettings(mockSettings)
      settingsForm.setFieldsValue(mockSettings)
    } catch (error) {
      message.error('加载心流设置失败')
    }
  }

  // 调整心流值
  const adjustFlowScore = async (values: any) => {
    try {
      const user = flowUsers.find(u => u.id === selectedUserId)
      if (!user) return

      const adjustment = values.adjustment
      const newScore = Math.max(0, user.currentFlowScore + adjustment)
      
      // 模拟API调用
      const newAdjustment: FlowAdjustment = {
        id: Date.now().toString(),
        userId: user.id,
        username: user.username,
        oldScore: user.currentFlowScore,
        newScore,
        adjustment,
        reason: values.reason,
        adminId: 'current_admin',
        adminName: '当前管理员',
        createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }

      // 更新用户心流值
      setFlowUsers(prev => prev.map(u => 
        u.id === user.id ? { ...u, currentFlowScore: newScore } : u
      ))

      // 添加调整记录
      setFlowAdjustments(prev => [newAdjustment, ...prev])

      message.success('心流值调整成功')
      setAdjustModalVisible(false)
      adjustForm.resetFields()
    } catch (error) {
      message.error('调整心流值失败')
    }
  }

  // 更新心流设置
  const updateFlowSettings = async (values: any) => {
    try {
      setFlowSettings(values)
      message.success('心流设置更新成功')
      setSettingsModalVisible(false)
    } catch (error) {
      message.error('更新心流设置失败')
    }
  }

  // 查看用户详情
  const viewUserDetail = (user: FlowUser) => {
    setSelectedUser(user)
    loadFlowRecords(user.id)
    setDetailModalVisible(true)
  }

  // 获取心流等级信息
  const getFlowLevelInfo = (level: string) => {
    return flowLevels.find(l => l.level === level) || flowLevels[0]
  }

  useEffect(() => {
    loadFlowUsers()
    loadFlowAdjustments()
    loadFlowSettings()
  }, [])

  // 用户表格列定义
  const userColumns: ColumnsType<FlowUser> = [
    {
      title: '用户',
      key: 'user',
      render: (_, record) => (
        <Space>
          <UserOutlined />
          <div>
            <div>{record.username}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.email}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '心流值',
      dataIndex: 'currentFlowScore',
      key: 'currentFlowScore',
      render: (score: number) => (
        <Space>
          <ThunderboltOutlined style={{ color: '#faad14' }} />
          <Text strong>{score.toLocaleString()}</Text>
        </Space>
      ),
      sorter: (a, b) => a.currentFlowScore - b.currentFlowScore
    },
    {
      title: '等级',
      dataIndex: 'flowLevel',
      key: 'flowLevel',
      render: (level: string) => {
        const levelInfo = getFlowLevelInfo(level)
        return (
          <Tag color={levelInfo.color}>
            <TrophyOutlined /> {levelInfo.name}
          </Tag>
        )
      }
    },
    {
      title: '总时长',
      dataIndex: 'totalFlowTime',
      key: 'totalFlowTime',
      render: (time: number) => (
        <Space>
          <ClockCircleOutlined />
          <span>{Math.floor(time / 60)}h {time % 60}m</span>
        </Space>
      )
    },
    {
      title: '连续天数',
      dataIndex: 'flowStreak',
      key: 'flowStreak',
      render: (streak: number) => (
        <Badge count={streak} style={{ backgroundColor: '#52c41a' }}>
          <FireOutlined style={{ fontSize: '16px', color: '#ff4d4f' }} />
        </Badge>
      )
    },
    {
      title: '平均时长',
      dataIndex: 'averageFlowDuration',
      key: 'averageFlowDuration',
      render: (duration: number) => `${duration}分钟`
    },
    {
      title: '最后活动',
      dataIndex: 'lastFlowSession',
      key: 'lastFlowSession',
      render: (time: string) => dayjs(time).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => viewUserDetail(record)}
            />
          </Tooltip>
          <Tooltip title="调整心流值">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedUserId(record.id)
                adjustForm.setFieldsValue({
                  username: record.username,
                  currentScore: record.currentFlowScore
                })
                setAdjustModalVisible(true)
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  // 调整历史表格列定义
  const adjustmentColumns: ColumnsType<FlowAdjustment> = [
    {
      title: '用户',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: '调整前',
      dataIndex: 'oldScore',
      key: 'oldScore',
      render: (score: number) => score.toLocaleString()
    },
    {
      title: '调整后',
      dataIndex: 'newScore',
      key: 'newScore',
      render: (score: number) => score.toLocaleString()
    },
    {
      title: '调整值',
      dataIndex: 'adjustment',
      key: 'adjustment',
      render: (adjustment: number) => (
        <Tag color={adjustment > 0 ? 'green' : 'red'}>
          {adjustment > 0 ? '+' : ''}{adjustment}
        </Tag>
      )
    },
    {
      title: '原因',
      dataIndex: 'reason',
      key: 'reason',
      ellipsis: true
    },
    {
      title: '操作员',
      dataIndex: 'adminName',
      key: 'adminName'
    },
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time: string) => dayjs(time).format('MM-DD HH:mm')
    }
  ]

  // 生成统计数据
  const generateStats = () => {
    const totalUsers = flowUsers.length
    const totalFlowScore = flowUsers.reduce((sum, user) => sum + user.currentFlowScore, 0)
    const avgFlowScore = totalUsers > 0 ? totalFlowScore / totalUsers : 0
    const activeUsers = flowUsers.filter(user => 
      dayjs().diff(dayjs(user.lastFlowSession), 'day') <= 7
    ).length

    return { totalUsers, totalFlowScore, avgFlowScore, activeUsers }
  }

  const stats = generateStats()

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>心流值管理</Title>
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setSettingsModalVisible(true)}
          >
            心流设置
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadFlowUsers}
            loading={loading}
          >
            刷新数据
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              suffix="人"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总心流值"
              value={stats.totalFlowScore}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均心流值"
              value={stats.avgFlowScore}
              precision={0}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={stats.activeUsers}
              prefix={<HeartOutlined />}
              suffix="人"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="users">
        {/* 用户管理 */}
        <TabPane tab="用户管理" key="users">
          <Card>
            <Table
              columns={userColumns}
              dataSource={flowUsers}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个用户`
              }}
            />
          </Card>
        </TabPane>

        {/* 调整历史 */}
        <TabPane tab="调整历史" key="adjustments">
          <Card>
            <Table
              columns={adjustmentColumns}
              dataSource={flowAdjustments}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 调整心流值模态框 */}
      <Modal
        title="调整心流值"
        open={adjustModalVisible}
        onCancel={() => {
          setAdjustModalVisible(false)
          adjustForm.resetFields()
        }}
        footer={null}
      >
        <Form
          form={adjustForm}
          layout="vertical"
          onFinish={adjustFlowScore}
        >
          <Form.Item name="username" label="用户">
            <Input disabled />
          </Form.Item>
          
          <Form.Item name="currentScore" label="当前心流值">
            <InputNumber disabled style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="adjustment"
            label="调整值"
            rules={[{ required: true, message: '请输入调整值' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="正数为增加，负数为减少"
              formatter={value => value ? (Number(value) > 0 ? `+${value}` : `${value}`) : ''}
            />
          </Form.Item>

          <Form.Item
            name="reason"
            label="调整原因"
            rules={[{ required: true, message: '请输入调整原因' }]}
          >
            <TextArea rows={3} placeholder="请说明调整原因" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确认调整
              </Button>
              <Button onClick={() => {
                setAdjustModalVisible(false)
                adjustForm.resetFields()
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 心流设置模态框 */}
      <Modal
        title="心流设置"
        open={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={settingsForm}
          layout="vertical"
          onFinish={updateFlowSettings}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="baseScorePerMinute"
                label="基础分数/分钟"
                rules={[{ required: true }]}
              >
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="qualityMultiplier"
                label="质量倍数"
                rules={[{ required: true }]}
              >
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="streakBonus"
                label="连续奖励"
                rules={[{ required: true }]}
              >
                <InputNumber min={0} step={0.01} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxDailyScore"
                label="每日上限"
                rules={[{ required: true }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="decayRate"
            label="衰减率"
            rules={[{ required: true }]}
          >
            <Slider
              min={0}
              max={0.1}
              step={0.001}
              marks={{
                0: '0%',
                0.05: '5%',
                0.1: '10%'
              }}
            />
          </Form.Item>

          <Divider>等级阈值</Divider>
          {flowLevels.map(level => (
            <Form.Item
              key={level.level}
              name={['levelThresholds', level.level]}
              label={level.name}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          ))}

          <Divider>活动权重</Divider>
          <Row gutter={16}>
            {activityTypes.map(activity => (
              <Col span={12} key={activity.value}>
                <Form.Item
                  name={['activityWeights', activity.value]}
                  label={`${activity.icon} ${activity.label}`}
                >
                  <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            ))}
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存设置
              </Button>
              <Button onClick={() => setSettingsModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 用户详情模态框 */}
      <Modal
        title={selectedUser ? `${selectedUser.username} 的心流详情` : '用户详情'}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedUser && (
          <div>
            <Row gutter={16} style={{ marginBottom: '24px' }}>
              <Col span={8}>
                <Statistic
                  title="当前心流值"
                  value={selectedUser.currentFlowScore}
                  prefix={<ThunderboltOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="总时长"
                  value={`${Math.floor(selectedUser.totalFlowTime / 60)}h ${selectedUser.totalFlowTime % 60}m`}
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="连续天数"
                  value={selectedUser.flowStreak}
                  prefix={<FireOutlined />}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>

            <Divider>心流活动</Divider>
            <Space wrap>
              {selectedUser.flowActivities.map(activity => {
                const activityInfo = activityTypes.find(a => a.value === activity)
                return activityInfo ? (
                  <Tag key={activity} color="blue">
                    {activityInfo.icon} {activityInfo.label}
                  </Tag>
                ) : null
              })}
            </Space>

            <Divider>最近记录</Divider>
            <Timeline>
              {flowRecords.slice(0, 5).map(record => (
                <Timeline.Item
                  key={record.id}
                  dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                >
                  <div>
                    <Text strong>{record.activity_type}</Text>
                    <br />
                    <Text type="secondary">
                      {dayjs(record.created_at).format('YYYY-MM-DD HH:mm')} · 
                      时长: {record.duration}分钟 · 
                      状态: {record.flow_state}
                    </Text>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default FlowManagement