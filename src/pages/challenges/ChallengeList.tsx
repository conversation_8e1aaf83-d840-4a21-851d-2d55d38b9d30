import React, { useState, useContext } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Modal,
  Form,
  DatePicker,
  Switch,
  Dropdown,
  Menu,
  Tabs,
  Progress,
  Popconfirm,
  Spin,
  Alert,
  Statistic
} from 'antd'
import { 
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  UserOutlined,
  Bar<PERSON><PERSON>Outlined,
  FlagOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import RichTextEditor from '../../components/RichTextEditor'
import { GlobalMessageContext } from '../../components/GlobalMessage'

const { Title } = Typography
const { Option } = Select
const { RangePicker } = DatePicker
const { TabPane } = Tabs

interface ChallengeDataType {
  id: number
  title: string
  creator: string
  participants: number
  startDate: string
  endDate: string
  status: 'active' | 'upcoming' | 'completed' | 'cancelled'
  category: string
  isPublic: boolean
  createdAt: string
}

interface ParticipantDataType {
  id: number
  userId: number
  username: string
  challengeTitle: string
  progress: number
  joinDate: string
  status: 'active' | 'completed' | 'dropped'
}

const challengeData: ChallengeDataType[] = [
  { 
    id: 1, 
    title: '21天戒色挑战', 
    creator: 'test09', 
    participants: 124,
    startDate: '2023-06-01',
    endDate: '2023-06-21',
    status: 'active',
    category: '戒色',
    isPublic: true,
    createdAt: '2023-05-30'
  },
  { 
    id: 2, 
    title: '30天早起挑战', 
    creator: '测试用户1', 
    participants: 86,
    startDate: '2023-06-10',
    endDate: '2023-07-09',
    status: 'active',
    category: '生活习惯',
    isPublic: true,
    createdAt: '2023-06-05'
  },
  { 
    id: 3, 
    title: '7天冥想挑战', 
    creator: '活跃用户2', 
    participants: 42,
    startDate: '2023-06-15',
    endDate: '2023-06-21',
    status: 'upcoming',
    category: '心理健康',
    isPublic: false,
    createdAt: '2023-06-10'
  },
  { 
    id: 4, 
    title: '100天阅读挑战', 
    creator: '高级用户3', 
    participants: 68,
    startDate: '2023-05-01',
    endDate: '2023-08-08',
    status: 'completed',
    category: '学习成长',
    isPublic: true,
    createdAt: '2023-04-28'
  },
]

const participantData: ParticipantDataType[] = [
  { 
    id: 1, 
    userId: 1,
    username: 'test09', 
    challengeTitle: '21天戒色挑战',
    progress: 70,
    joinDate: '2023-06-01',
    status: 'active'
  },
  { 
    id: 2, 
    userId: 2,
    username: '测试用户1', 
    challengeTitle: '30天早起挑战',
    progress: 40,
    joinDate: '2023-06-10',
    status: 'active'
  },
  { 
    id: 3, 
    userId: 3,
    username: '活跃用户2', 
    challengeTitle: '7天冥想挑战',
    progress: 0,
    joinDate: '2023-06-15',
    status: 'active'
  },
  { 
    id: 4, 
    userId: 4,
    username: '高级用户3', 
    challengeTitle: '100天阅读挑战',
    progress: 100,
    joinDate: '2023-05-01',
    status: 'completed'
  },
]

const challengeColumns: TableProps<ChallengeDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '挑战标题',
    dataIndex: 'title',
    key: 'title',
    render: (text: string) => (
      <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {text}
      </div>
    )
  },
  {
    title: '创建者',
    dataIndex: 'creator',
    key: 'creator',
  },
  {
    title: '参与者',
    dataIndex: 'participants',
    key: 'participants',
  },
  {
    title: '开始日期',
    dataIndex: 'startDate',
    key: 'startDate',
  },
  {
    title: '结束日期',
    dataIndex: 'endDate',
    key: 'endDate',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => {
      const statusMap: Record<string, { text: string, color: 'success' | 'processing' | 'default' | 'error' }> = {
        'active': { text: '进行中', color: 'processing' },
        'upcoming': { text: '即将开始', color: 'default' },
        'completed': { text: '已完成', color: 'success' },
        'cancelled': { text: '已取消', color: 'error' }
      }
      
      const stat = statusMap[status] || { text: status, color: 'default' }
      return <Tag color={stat.color}>{stat.text}</Tag>
    }
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    render: (category: string) => {
      const categoryMap: Record<string, { text: string, color: string }> = {
        '戒色': { text: '戒色', color: 'red' },
        '生活习惯': { text: '生活习惯', color: 'green' },
        '心理健康': { text: '心理健康', color: 'blue' },
        '学习成长': { text: '学习成长', color: 'purple' }
      }
      
      const cat = categoryMap[category] || { text: category, color: 'default' }
      return <Tag color={cat.color}>{cat.text}</Tag>
    }
  },
  {
    title: '公开',
    dataIndex: 'isPublic',
    key: 'isPublic',
    render: (isPublic: boolean) => (
      <Tag color={isPublic ? 'success' : 'default'}>
        {isPublic ? '是' : '否'}
      </Tag>
    )
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    render: (_, record) => (
      <Space size="middle">
        <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewChallenge(record)}>查看</Button>
        <Button type="link" icon={<EditOutlined />} size="small" onClick={() => handleEditChallenge(record)}>编辑</Button>
        <Dropdown 
          overlay={
            <Menu>
              <Menu.Item key="1" icon={<EyeOutlined />} onClick={() => handleViewChallenge(record)}>查看详情</Menu.Item>
              <Menu.Item key="2" icon={<EditOutlined />} onClick={() => handleEditChallenge(record)}>编辑挑战</Menu.Item>
              <Menu.Item key="3" icon={<UserOutlined />}>参与者管理</Menu.Item>
              <Menu.Item key="4" icon={<BarChartOutlined />}>查看统计</Menu.Item>
              <Menu.Divider />
              <Popconfirm
                title="确定删除此挑战吗？"
                description="删除后将无法恢复，请确认操作"
                onConfirm={() => {
                  // 在实际应用中，这里会调用删除API
                  message.success(`挑战 "${record.title}" 删除成功`)
                }}
                okText="确定"
                cancelText="取消"
              >
                <Menu.Item key="5" danger icon={<DeleteOutlined />}>删除挑战</Menu.Item>
              </Popconfirm>
            </Menu>
          }
        >
          <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
        </Dropdown>
      </Space>
    ),
  },
]

const participantColumns: TableProps<ParticipantDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '用户ID',
    dataIndex: 'userId',
    key: 'userId',
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '挑战名称',
    dataIndex: 'challengeTitle',
    key: 'challengeTitle',
    render: (text: string) => (
      <div style={{ maxWidth: 150, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {text}
      </div>
    )
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    render: (progress: number) => (
      <Progress percent={progress} size="small" />
    )
  },
  {
    title: '加入时间',
    dataIndex: 'joinDate',
    key: 'joinDate',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => {
      const statusMap: Record<string, { text: string, color: 'success' | 'processing' | 'default' | 'error' }> = {
        'active': { text: '进行中', color: 'processing' },
        'completed': { text: '已完成', color: 'success' },
        'dropped': { text: '已退出', color: 'error' }
      }
      
      const stat = statusMap[status] || { text: status, color: 'default' }
      return <Tag color={stat.color}>{stat.text}</Tag>
    }
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    render: (_, record) => (
      <Space size="middle">
        <Button type="link" icon={<EyeOutlined />} size="small">查看</Button>
        <Button type="link" icon={<EditOutlined />} size="small">编辑</Button>
      </Space>
    ),
  },
]

const userChallengeColumns: TableProps<ChallengeDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '挑战标题',
    dataIndex: 'title',
    key: 'title',
    render: (text: string) => (
      <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {text}
      </div>
    )
  },
  {
    title: '创建者',
    dataIndex: 'creator',
    key: 'creator',
  },
  {
    title: '参与者',
    dataIndex: 'participants',
    key: 'participants',
  },
  {
    title: '开始日期',
    dataIndex: 'startDate',
    key: 'startDate',
  },
  {
    title: '结束日期',
    dataIndex: 'endDate',
    key: 'endDate',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => {
      const statusMap: Record<string, { text: string, color: 'success' | 'processing' | 'default' | 'error' }> = {
        'active': { text: '进行中', color: 'processing' },
        'upcoming': { text: '即将开始', color: 'default' },
        'completed': { text: '已完成', color: 'success' },
        'cancelled': { text: '已取消', color: 'error' }
      }
      
      const stat = statusMap[status] || { text: status, color: 'default' }
      return <Tag color={stat.color}>{stat.text}</Tag>
    }
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    render: (category: string) => {
      const categoryMap: Record<string, { text: string, color: string }> = {
        '戒色': { text: '戒色', color: 'red' },
        '生活习惯': { text: '生活习惯', color: 'green' },
        '心理健康': { text: '心理健康', color: 'blue' },
        '学习成长': { text: '学习成长', color: 'purple' }
      }
      
      const cat = categoryMap[category] || { text: category, color: 'default' }
      return <Tag color={cat.color}>{cat.text}</Tag>
    }
  },
  {
    title: '公开',
    dataIndex: 'isPublic',
    key: 'isPublic',
    render: (isPublic: boolean) => (
      <Tag color={isPublic ? 'success' : 'default'}>
        {isPublic ? '是' : '否'}
      </Tag>
    )
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
  },
]

const userChallengeData: ChallengeDataType[] = [
  { 
    id: 1, 
    title: '21天戒色挑战', 
    creator: 'test09', 
    participants: 124,
    startDate: '2023-06-01',
    endDate: '2023-06-21',
    status: 'active',
    category: '戒色',
    isPublic: true,
    createdAt: '2023-05-30'
  },
  { 
    id: 2, 
    title: '30天早起挑战', 
    creator: '测试用户1', 
    participants: 86,
    startDate: '2023-06-10',
    endDate: '2023-07-09',
    status: 'active',
    category: '生活习惯',
    isPublic: true,
    createdAt: '2023-06-05'
  },
  { 
    id: 3, 
    title: '7天冥想挑战', 
    creator: '活跃用户2', 
    participants: 42,
    startDate: '2023-06-15',
    endDate: '2023-06-21',
    status: 'upcoming',
    category: '心理健康',
    isPublic: false,
    createdAt: '2023-06-10'
  },
  { 
    id: 4, 
    title: '100天阅读挑战', 
    creator: '高级用户3', 
    participants: 68,
    startDate: '2023-05-01',
    endDate: '2023-08-08',
    status: 'completed',
    category: '学习成长',
    isPublic: true,
    createdAt: '2023-04-28'
  },
]

const ChallengeList: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('challenges')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()
  const { message } = useContext(GlobalMessageContext)
  
  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('Received values of form: ', values)
      message.success('挑战创建成功')
      setIsModalVisible(false)
      form.resetFields()
    }).catch(info => {
      console.log('Validate Failed:', info)
      message.error('表单验证失败')
    })
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    setLoading(true)
    setError(null)
    // 模拟API调用
    setTimeout(() => {
      // 模拟随机错误
      if (Math.random() > 0.8) {
        setError('数据加载失败，请稍后重试')
        message.error('数据加载失败，请稍后重试')
      } else {
        message.success('数据刷新成功')
      }
      setLoading(false)
    }, 1000)
  }
  
  const handleAddChallenge = () => {
    showModal()
  }

  // 查看挑战详情
  const handleViewChallenge = (record: ChallengeDataType) => {
    Modal.info({
      title: '挑战详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <strong>挑战标题：</strong>{record.title}
            </Col>
            <Col span={12}>
              <strong>创建者：</strong>{record.creator}
            </Col>
            <Col span={12}>
              <strong>参与者：</strong>{record.participants}人
            </Col>
            <Col span={12}>
              <strong>开始日期：</strong>{record.startDate}
            </Col>
            <Col span={12}>
              <strong>结束日期：</strong>{record.endDate}
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={record.status === 'active' ? 'processing' : record.status === 'upcoming' ? 'default' : record.status === 'completed' ? 'success' : 'error'}>
                {record.status === 'active' ? '进行中' : record.status === 'upcoming' ? '即将开始' : record.status === 'completed' ? '已完成' : '已取消'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>分类：</strong>
              <Tag color={record.category === '戒色' ? 'red' : record.category === '生活习惯' ? 'green' : record.category === '心理健康' ? 'blue' : 'purple'}>
                {record.category}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>公开：</strong>
              <Tag color={record.isPublic ? 'success' : 'default'}>
                {record.isPublic ? '是' : '否'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{record.createdAt}
            </Col>
          </Row>
        </div>
      ),
    });
  };

  // 编辑挑战
  const handleEditChallenge = (record: ChallengeDataType) => {
    form.setFieldsValue({
      title: record.title,
      category: record.category,
      startDate: record.startDate,
      endDate: record.endDate,
      isPublic: record.isPublic
    });
    setIsModalVisible(true);
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>挑战管理</Title>
      
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<Space><FlagOutlined />挑战管理</Space>} key="challenges">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  onAdd={handleAddChallenge}
                  showRefresh
                  showAdd
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索挑战标题/创建者"
              filters={[
                {
                  key: 'status',
                  label: '挑战状态',
                  type: 'select',
                  options: [
                    { value: 'active', label: '进行中' },
                    { value: 'upcoming', label: '即将开始' },
                    { value: 'completed', label: '已完成' },
                    { value: 'cancelled', label: '已取消' }
                  ]
                },
                {
                  key: 'category',
                  label: '挑战分类',
                  type: 'select',
                  options: [
                    { value: '戒色', label: '戒色' },
                    { value: '生活习惯', label: '生活习惯' },
                    { value: '心理健康', label: '心理健康' },
                    { value: '学习成长', label: '学习成长' }
                  ]
                },
                {
                  key: 'dateRange',
                  label: '日期范围',
                  type: 'dateRange'
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={challengeColumns} 
                dataSource={challengeData} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  defaultPageSize: 20
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
          
          <TabPane tab={<Space><UserOutlined />参与者管理</Space>} key="participants">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索用户名/挑战名称"
              filters={[
                {
                  key: 'status',
                  label: '参与状态',
                  type: 'select',
                  options: [
                    { value: 'active', label: '进行中' },
                    { value: 'completed', label: '已完成' },
                    { value: 'dropped', label: '已退出' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={participantColumns} 
                dataSource={participantData} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  defaultPageSize: 20
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
          
          <TabPane tab={<Space><BarChartOutlined />统计分析</Space>} key="statistics">
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="总挑战数" value={12} prefix={<FlagOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="进行中挑战" value={3} prefix={<FlagOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="总参与者" value={456} prefix={<UserOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="完成率" value="68%" prefix={<BarChartOutlined />} />
                </Card>
              </Col>
            </Row>
            
            <Card title="挑战参与趋势">
              <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <img src="https://placehold.co/600x300" alt="挑战参与趋势图表" />
              </div>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
      
      <Modal
        title="创建挑战"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="title" 
            label="挑战标题" 
            rules={[{ required: true, message: '请输入挑战标题' }]}
          >
            <Input placeholder="请输入挑战标题" />
          </Form.Item>
          <Form.Item 
            name="creator" 
            label="创建者" 
            rules={[{ required: true, message: '请输入创建者' }]}
          >
            <Input placeholder="请输入创建者" />
          </Form.Item>
          <Form.Item 
            name="category" 
            label="分类" 
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              <Option value="戒色">戒色</Option>
              <Option value="生活习惯">生活习惯</Option>
              <Option value="心理健康">心理健康</Option>
              <Option value="学习成长">学习成长</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="startDate" 
            label="开始日期" 
            rules={[{ required: true, message: '请选择开始日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item 
            name="endDate" 
            label="结束日期" 
            rules={[{ required: true, message: '请选择结束日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item 
            name="description" 
            label="挑战描述" 
            rules={[{ required: true, message: '请输入挑战描述' }]}
          >
            <RichTextEditor 
              placeholder="请输入挑战描述" 
              style={{ height: '200px' }}
            />
          </Form.Item>
          <Form.Item name="isPublic" label="是否公开" valuePropName="checked">
            <Switch defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default ChallengeList