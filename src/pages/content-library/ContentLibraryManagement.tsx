import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Upload,
  message,
  Popconfirm,
  Row,
  Col,
  Statistic,
  DatePicker,
  InputNumber,
  Divider,
  Avatar,
  Rate,
  Progress,
  Tabs,
  List,
  Typography,
  TreeSelect,
  Collapse,
  Badge,
  Tooltip,
  Image
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  BookOutlined,
  QuestionCircleOutlined,
  TagOutlined,
  FolderOutlined,
  FileTextOutlined,
  UserOutlined,
  StarOutlined,
  LikeOutlined,
  MessageOutlined,
  SearchOutlined,
  ExportOutlined,
  ImportOutlined,
  PlayCircleOutlined,
  SoundOutlined,
  PictureOutlined,
  FileOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import RichTextEditor from '../../components/RichTextEditor';
import { contentLibraryService } from '../../services/contentLibraryService';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;
const { TreeNode } = TreeSelect;

interface ContentItem {
  id: string;
  title: string;
  content: string;
  type: 'article' | 'video' | 'audio' | 'image' | 'document';
  category: string;
  tags: string[];
  author: {
    id: string;
    name: string;
    avatar?: string;
    isExpert: boolean;
  };
  status: 'draft' | 'published' | 'archived' | 'rejected';
  visibility: 'public' | 'private' | 'premium';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadTime: number;
  views: number;
  likes: number;
  comments: number;
  rating: number;
  ratingCount: number;
  thumbnail?: string;
  attachments?: string[];
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  featured: boolean;
  seoKeywords?: string[];
  summary: string;
}

interface ContentCategory {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  icon?: string;
  color?: string;
  order: number;
  isActive: boolean;
  contentCount: number;
  children?: ContentCategory[];
}

interface ExpertQA {
  id: string;
  question: string;
  answer: string;
  expert: {
    id: string;
    name: string;
    avatar?: string;
    title: string;
    expertise: string[];
    rating: number;
  };
  category: string;
  tags: string[];
  status: 'pending' | 'answered' | 'published' | 'archived';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  askedBy: {
    id: string;
    name: string;
    avatar?: string;
  };
  views: number;
  likes: number;
  helpful: number;
  createdAt: string;
  answeredAt?: string;
  publishedAt?: string;
  isPublic: boolean;
  relatedContent?: string[];
}

interface ContentStats {
  totalContent: number;
  publishedContent: number;
  draftContent: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  expertQACount: number;
  pendingQACount: number;
  topCategories: { name: string; count: number }[];
  topTags: { name: string; count: number }[];
}

const ContentLibraryManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('content');
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [categories, setCategories] = useState<ContentCategory[]>([]);
  const [expertQAs, setExpertQAs] = useState<ExpertQA[]>([]);
  const [stats, setStats] = useState<ContentStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [qaModalVisible, setQaModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<ContentItem | null>(null);
  const [editingCategory, setEditingCategory] = useState<ContentCategory | null>(null);
  const [editingQA, setEditingQA] = useState<ExpertQA | null>(null);
  const [form] = Form.useForm();
  const [categoryForm] = Form.useForm();
  const [qaForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [contentData, categoryData, qaData, statsData] = await Promise.all([
        contentLibraryService.getAllContent(),
        contentLibraryService.getCategories(),
        contentLibraryService.getExpertQAs(),
        contentLibraryService.getStats()
      ]);
      setContentItems(contentData);
      setCategories(categoryData);
      setExpertQAs(qaData);
      setStats(statsData);
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 内容管理相关函数
  const handleAddContent = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditContent = (item: ContentItem) => {
    setEditingItem(item);
    form.setFieldsValue(item);
    setModalVisible(true);
  };

  const handleDeleteContent = async (id: string) => {
    try {
      await contentLibraryService.deleteContent(id);
      message.success('删除成功');
      fetchData();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmitContent = async (values: any) => {
    try {
      if (editingItem) {
        await contentLibraryService.updateContent(editingItem.id, values);
        message.success('更新成功');
      } else {
        await contentLibraryService.createContent(values);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchData();
    } catch (error) {
      message.error(editingItem ? '更新失败' : '创建失败');
    }
  };

  // 分类管理相关函数
  const handleAddCategory = () => {
    setEditingCategory(null);
    categoryForm.resetFields();
    setCategoryModalVisible(true);
  };

  const handleEditCategory = (category: ContentCategory) => {
    setEditingCategory(category);
    categoryForm.setFieldsValue(category);
    setCategoryModalVisible(true);
  };

  const handleDeleteCategory = async (id: string) => {
    try {
      await contentLibraryService.deleteCategory(id);
      message.success('删除成功');
      fetchData();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmitCategory = async (values: any) => {
    try {
      if (editingCategory) {
        await contentLibraryService.updateCategory(editingCategory.id, values);
        message.success('更新成功');
      } else {
        await contentLibraryService.createCategory(values);
        message.success('创建成功');
      }
      setCategoryModalVisible(false);
      fetchData();
    } catch (error) {
      message.error(editingCategory ? '更新失败' : '创建失败');
    }
  };

  // 专家问答相关函数
  const handleAddQA = () => {
    setEditingQA(null);
    qaForm.resetFields();
    setQaModalVisible(true);
  };

  const handleEditQA = (qa: ExpertQA) => {
    setEditingQA(qa);
    qaForm.setFieldsValue(qa);
    setQaModalVisible(true);
  };

  const handleDeleteQA = async (id: string) => {
    try {
      await contentLibraryService.deleteExpertQA(id);
      message.success('删除成功');
      fetchData();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmitQA = async (values: any) => {
    try {
      if (editingQA) {
        await contentLibraryService.updateExpertQA(editingQA.id, values);
        message.success('更新成功');
      } else {
        await contentLibraryService.createExpertQA(values);
        message.success('创建成功');
      }
      setQaModalVisible(false);
      fetchData();
    } catch (error) {
      message.error(editingQA ? '更新失败' : '创建失败');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      draft: 'orange',
      published: 'green',
      archived: 'gray',
      rejected: 'red',
      pending: 'blue',
      answered: 'cyan'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      article: <FileTextOutlined />,
      video: <PlayCircleOutlined />,
      audio: <SoundOutlined />,
      image: <PictureOutlined />,
      document: <FileOutlined />
    };
    return icons[type as keyof typeof icons] || <FileTextOutlined />;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
      urgent: 'magenta'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  // 内容列表列定义
  const contentColumns: ColumnsType<ContentItem> = [
    {
      title: '内容信息',
      key: 'info',
      render: (_, record) => (
        <Space>
          {record.thumbnail && (
            <Image
              width={60}
              height={40}
              src={record.thumbnail}
              style={{ borderRadius: 4 }}
            />
          )}
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
              {getTypeIcon(record.type)} {record.title}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.summary.substring(0, 50)}...
            </div>
            <Space size="small" style={{ marginTop: 4 }}>
              {record.featured && <Badge status="success" text="精选" />}
              <Text type="secondary">{record.estimatedReadTime}分钟阅读</Text>
            </Space>
          </div>
        </Space>
      ),
    },
    {
      title: '作者',
      key: 'author',
      render: (_, record) => (
        <Space>
          <Avatar src={record.author.avatar} icon={<UserOutlined />} />
          <div>
            <div>{record.author.name}</div>
            {record.author.isExpert && (
              <Tag color="gold">专家</Tag>
            )}
          </div>
        </Space>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category) => <Tag>{category}</Tag>,
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags) => (
        <Space wrap>
          {tags.slice(0, 2).map((tag: string) => (
            <Tag key={tag}>{tag}</Tag>
          ))}
          {tags.length > 2 && <Tag>+{tags.length - 2}</Tag>}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status === 'draft' ? '草稿' : 
           status === 'published' ? '已发布' :
           status === 'archived' ? '已归档' : '已拒绝'}
        </Tag>
      ),
    },
    {
      title: '统计',
      key: 'stats',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Space size="small">
            <EyeOutlined /> {record.views}
            <LikeOutlined /> {record.likes}
            <MessageOutlined /> {record.comments}
          </Space>
          <Rate disabled defaultValue={record.rating} />
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewContent(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditContent(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个内容吗？"
            onConfirm={() => handleDeleteContent(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分类列表列定义
  const categoryColumns: ColumnsType<ContentCategory> = [
    {
      title: '分类名称',
      key: 'name',
      render: (_, record) => (
        <Space>
          {record.icon && <span style={{ color: record.color }}>{record.icon}</span>}
          <span>{record.name}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '内容数量',
      dataIndex: 'contentCount',
      key: 'contentCount',
      sorter: (a, b) => a.contentCount - b.contentCount,
    },
    {
      title: '排序',
      dataIndex: 'order',
      key: 'order',
      sorter: (a, b) => a.order - b.order,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditCategory(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分类吗？"
            onConfirm={() => handleDeleteCategory(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 专家问答列表列定义
  const qaColumns: ColumnsType<ExpertQA> = [
    {
      title: '问题',
      key: 'question',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            {record.question}
          </div>
          <Space size="small">
            <Tag color={getPriorityColor(record.priority)}>
              {record.priority === 'low' ? '低' :
               record.priority === 'medium' ? '中' :
               record.priority === 'high' ? '高' : '紧急'}
            </Tag>
            <Text type="secondary">分类: {record.category}</Text>
          </Space>
        </div>
      ),
    },
    {
      title: '提问者',
      key: 'askedBy',
      render: (_, record) => (
        <Space>
          <Avatar src={record.askedBy.avatar} icon={<UserOutlined />} />
          <span>{record.askedBy.name}</span>
        </Space>
      ),
    },
    {
      title: '专家',
      key: 'expert',
      render: (_, record) => (
        <Space>
          <Avatar src={record.expert.avatar} icon={<UserOutlined />} />
          <div>
            <div>{record.expert.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.expert.title}
            </div>
            <Rate disabled defaultValue={record.expert.rating} />
          </div>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status === 'pending' ? '待回答' :
           status === 'answered' ? '已回答' :
           status === 'published' ? '已发布' : '已归档'}
        </Tag>
      ),
    },
    {
      title: '统计',
      key: 'stats',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Space size="small">
            <EyeOutlined /> {record.views}
            <LikeOutlined /> {record.likes}
            <StarOutlined /> {record.helpful}
          </Space>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewQA(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditQA(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个问答吗？"
            onConfirm={() => handleDeleteQA(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleViewContent = (content: ContentItem) => {
    Modal.info({
      title: '内容详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <strong>标题：</strong>{content.title}
            </Col>
            <Col span={12}>
              <strong>类型：</strong>
              <Tag color={content.type === 'article' ? 'blue' : content.type === 'video' ? 'red' : content.type === 'audio' ? 'green' : content.type === 'image' ? 'orange' : 'purple'}>
                {content.type === 'article' ? '文章' : content.type === 'video' ? '视频' : content.type === 'audio' ? '音频' : content.type === 'image' ? '图片' : '文档'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>分类：</strong>{content.category}
            </Col>
            <Col span={12}>
              <strong>作者：</strong>{content.author.name}
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={content.status === 'published' ? 'success' : content.status === 'draft' ? 'default' : content.status === 'archived' ? 'warning' : 'error'}>
                {content.status === 'published' ? '已发布' : content.status === 'draft' ? '草稿' : content.status === 'archived' ? '已归档' : '已拒绝'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>可见性：</strong>
              <Tag color={content.visibility === 'public' ? 'green' : content.visibility === 'private' ? 'red' : 'gold'}>
                {content.visibility === 'public' ? '公开' : content.visibility === 'private' ? '私有' : '会员专享'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>难度：</strong>
              <Tag color={content.difficulty === 'beginner' ? 'green' : content.difficulty === 'intermediate' ? 'orange' : 'red'}>
                {content.difficulty === 'beginner' ? '初级' : content.difficulty === 'intermediate' ? '中级' : '高级'}
              </Tag>
            </Col>
            <Col span={8}>
              <strong>浏览量：</strong>{content.views}
            </Col>
            <Col span={8}>
              <strong>点赞数：</strong>{content.likes}
            </Col>
            <Col span={8}>
              <strong>评论数：</strong>{content.comments}
            </Col>
            <Col span={12}>
              <strong>评分：</strong>
              <Rate disabled value={content.rating} /> ({content.ratingCount}人评价)
            </Col>
            <Col span={12}>
              <strong>预计阅读时间：</strong>{content.estimatedReadTime}分钟
            </Col>
            <Col span={24}>
              <strong>标签：</strong>
              <Space wrap>
                {content.tags.map(tag => <Tag key={tag}>{tag}</Tag>)}
              </Space>
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{new Date(content.createdAt).toLocaleString()}
            </Col>
            <Col span={12}>
              <strong>更新时间：</strong>{new Date(content.updatedAt).toLocaleString()}
            </Col>
            <Col span={24}>
              <strong>摘要：</strong>
              <div style={{ 
                border: '1px solid #d9d9d9', 
                borderRadius: '6px', 
                padding: '12px', 
                backgroundColor: '#fafafa',
                marginTop: '8px'
              }}>
                {content.summary}
              </div>
            </Col>
            <Col span={24}>
              <strong>内容：</strong>
              <div style={{ 
                border: '1px solid #d9d9d9', 
                borderRadius: '6px', 
                padding: '12px', 
                backgroundColor: '#fafafa',
                maxHeight: '300px',
                overflow: 'auto',
                marginTop: '8px'
              }}>
                {content.content}
              </div>
            </Col>
          </Row>
        </div>
      )
    });
  };

  const handleViewQA = (qa: ExpertQA) => {
    Modal.info({
      title: '专家问答详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <strong>问题：</strong>
              <div style={{ 
                border: '1px solid #d9d9d9', 
                borderRadius: '6px', 
                padding: '12px', 
                backgroundColor: '#fafafa',
                marginTop: '8px'
              }}>
                {qa.question}
              </div>
            </Col>
            <Col span={12}>
              <strong>提问者：</strong>{qa.askedBy.name}
            </Col>
            <Col span={12}>
              <strong>专家：</strong>{qa.expert.name} ({qa.expert.title})
            </Col>
            <Col span={12}>
              <strong>分类：</strong>{qa.category}
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={qa.status === 'published' ? 'success' : qa.status === 'answered' ? 'blue' : qa.status === 'pending' ? 'warning' : 'default'}>
                {qa.status === 'published' ? '已发布' : qa.status === 'answered' ? '已回答' : qa.status === 'pending' ? '待回答' : '已归档'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>优先级：</strong>
              <Tag color={qa.priority === 'urgent' ? 'red' : qa.priority === 'high' ? 'orange' : qa.priority === 'medium' ? 'blue' : 'default'}>
                {qa.priority === 'urgent' ? '紧急' : qa.priority === 'high' ? '高' : qa.priority === 'medium' ? '中' : '低'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>可见性：</strong>
              <Tag color={qa.isPublic ? 'green' : 'red'}>
                {qa.isPublic ? '公开' : '私有'}
              </Tag>
            </Col>
            <Col span={8}>
              <strong>浏览量：</strong>{qa.views}
            </Col>
            <Col span={8}>
              <strong>点赞数：</strong>{qa.likes}
            </Col>
            <Col span={8}>
              <strong>有用数：</strong>{qa.helpful}
            </Col>
            <Col span={24}>
              <strong>专家领域：</strong>
              <Space wrap>
                {qa.expert.expertise.map(exp => <Tag key={exp} color="blue">{exp}</Tag>)}
              </Space>
            </Col>
            <Col span={24}>
              <strong>标签：</strong>
              <Space wrap>
                {qa.tags.map(tag => <Tag key={tag}>{tag}</Tag>)}
              </Space>
            </Col>
            <Col span={12}>
              <strong>提问时间：</strong>{new Date(qa.createdAt).toLocaleString()}
            </Col>
            <Col span={12}>
              <strong>回答时间：</strong>{qa.answeredAt ? new Date(qa.answeredAt).toLocaleString() : '未回答'}
            </Col>
            {qa.answer && (
              <Col span={24}>
                <strong>回答：</strong>
                <div style={{ 
                  border: '1px solid #d9d9d9', 
                  borderRadius: '6px', 
                  padding: '12px', 
                  backgroundColor: '#fafafa',
                  maxHeight: '300px',
                  overflow: 'auto',
                  marginTop: '8px'
                }}>
                  {qa.answer}
                </div>
              </Col>
            )}
          </Row>
        </div>
      )
    });
  };

  const filteredContent = contentItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchText.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchText.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus;
    const matchesType = selectedType === 'all' || item.type === selectedType;
    return matchesSearch && matchesCategory && matchesStatus && matchesType;
  });

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>内容库管理</Title>
      
      {/* 统计卡片 */}
      {stats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总内容数"
                value={stats.totalContent}
                prefix={<BookOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已发布内容"
                value={stats.publishedContent}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="专家问答"
                value={stats.expertQACount}
                prefix={<QuestionCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="待处理问答"
                value={stats.pendingQACount}
                prefix={<QuestionCircleOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 内容管理标签页 */}
        <TabPane tab={<Space><BookOutlined />内容管理</Space>} key="content">
          {/* 搜索和筛选 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col span={6}>
                <Input.Search
                  placeholder="搜索内容标题或内容"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={4}>
                <Select
                  placeholder="分类筛选"
                  value={selectedCategory}
                  onChange={setSelectedCategory}
                  style={{ width: '100%' }}
                >
                  <Option value="all">全部分类</Option>
                  {categories.map(cat => (
                    <Option key={cat.id} value={cat.name}>{cat.name}</Option>
                  ))}
                </Select>
              </Col>
              <Col span={4}>
                <Select
                  placeholder="状态筛选"
                  value={selectedStatus}
                  onChange={setSelectedStatus}
                  style={{ width: '100%' }}
                >
                  <Option value="all">全部状态</Option>
                  <Option value="draft">草稿</Option>
                  <Option value="published">已发布</Option>
                  <Option value="archived">已归档</Option>
                  <Option value="rejected">已拒绝</Option>
                </Select>
              </Col>
              <Col span={4}>
                <Select
                  placeholder="类型筛选"
                  value={selectedType}
                  onChange={setSelectedType}
                  style={{ width: '100%' }}
                >
                  <Option value="all">全部类型</Option>
                  <Option value="article">文章</Option>
                  <Option value="video">视频</Option>
                  <Option value="audio">音频</Option>
                  <Option value="image">图片</Option>
                  <Option value="document">文档</Option>
                </Select>
              </Col>
              <Col span={6} style={{ textAlign: 'right' }}>
                <Space>
                  <Button icon={<ImportOutlined />}>导入</Button>
                  <Button icon={<ExportOutlined />}>导出</Button>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddContent}
                  >
                    添加内容
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 内容列表 */}
          <Card>
            <Table
              columns={contentColumns}
              dataSource={filteredContent}
              rowKey="id"
              loading={loading}
              pagination={{
                total: filteredContent.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </Card>
        </TabPane>

        {/* 分类管理标签页 */}
        <TabPane tab={<Space><FolderOutlined />分类管理</Space>} key="categories">
          <Card>
            <div style={{ marginBottom: 16, textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddCategory}
              >
                添加分类
              </Button>
            </div>
            <Table
              columns={categoryColumns}
              dataSource={categories}
              rowKey="id"
              loading={loading}
              pagination={false}
            />
          </Card>
        </TabPane>

        {/* 专家问答标签页 */}
        <TabPane tab={<Space><QuestionCircleOutlined />专家问答</Space>} key="qa">
          <Card>
            <div style={{ marginBottom: 16, textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddQA}
              >
                添加问答
              </Button>
            </div>
            <Table
              columns={qaColumns}
              dataSource={expertQAs}
              rowKey="id"
              loading={loading}
              pagination={{
                total: expertQAs.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </Card>
        </TabPane>

        {/* 标签管理标签页 */}
        <TabPane tab={<Space><TagOutlined />标签管理</Space>} key="tags">
          <Card>
            <Title level={4}>热门标签</Title>
            {stats && (
              <Space wrap style={{ marginBottom: 16 }}>
                {stats.topTags.map(tag => (
                  <Tag key={tag.name} color="blue">
                    {tag.name} ({tag.count})
                  </Tag>
                ))}
              </Space>
            )}
            <Divider />
            <Title level={4}>标签管理功能</Title>
            <Space>
              <Button icon={<PlusOutlined />}>添加标签</Button>
              <Button icon={<EditOutlined />}>批量编辑</Button>
              <Button icon={<DeleteOutlined />}>批量删除</Button>
            </Space>
          </Card>
        </TabPane>
      </Tabs>

      {/* 内容添加/编辑模态框 */}
      <Modal
        title={editingItem ? '编辑内容' : '添加内容'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={1000}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitContent}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="标题"
                rules={[{ required: true, message: '请输入标题' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="内容类型"
                rules={[{ required: true, message: '请选择内容类型' }]}
              >
                <Select>
                  <Option value="article">文章</Option>
                  <Option value="video">视频</Option>
                  <Option value="audio">音频</Option>
                  <Option value="image">图片</Option>
                  <Option value="document">文档</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select>
                  {categories.map(cat => (
                    <Option key={cat.id} value={cat.name}>{cat.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tags"
                label="标签"
                rules={[{ required: true, message: '请选择标签' }]}
              >
                <Select mode="tags" placeholder="输入或选择标签">
                  {stats?.topTags.map(tag => (
                    <Option key={tag.name} value={tag.name}>{tag.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="summary"
            label="摘要"
            rules={[{ required: true, message: '请输入摘要' }]}
          >
            <RichTextEditor style={{ minHeight: '100px' }} />
          </Form.Item>

          <Form.Item
            name="content"
            label="内容"
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <RichTextEditor 
              placeholder="请输入内容..."
              style={{ height: '300px' }}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="difficulty"
                label="难度等级"
                rules={[{ required: true, message: '请选择难度等级' }]}
              >
                <Select>
                  <Option value="beginner">初级</Option>
                  <Option value="intermediate">中级</Option>
                  <Option value="advanced">高级</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="estimatedReadTime"
                label="预计阅读时间（分钟）"
                rules={[{ required: true, message: '请输入预计阅读时间' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="visibility"
                label="可见性"
                rules={[{ required: true, message: '请选择可见性' }]}
              >
                <Select>
                  <Option value="public">公开</Option>
                  <Option value="private">私有</Option>
                  <Option value="premium">会员专享</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="featured"
                label="设为精选"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="draft">草稿</Option>
                  <Option value="published">发布</Option>
                  <Option value="archived">归档</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingItem ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分类添加/编辑模态框 */}
      <Modal
        title={editingCategory ? '编辑分类' : '添加分类'}
        open={categoryModalVisible}
        onCancel={() => setCategoryModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={categoryForm}
          layout="vertical"
          onFinish={handleSubmitCategory}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={3} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="icon"
                label="图标"
              >
                <Input placeholder="如: 📚" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="color"
                label="颜色"
              >
                <Input placeholder="如: #1890ff" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="order"
                label="排序"
                rules={[{ required: true, message: '请输入排序' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="isActive"
                label="启用状态"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setCategoryModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCategory ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 专家问答添加/编辑模态框 */}
      <Modal
        title={editingQA ? '编辑问答' : '添加问答'}
        open={qaModalVisible}
        onCancel={() => setQaModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={qaForm}
          layout="vertical"
          onFinish={handleSubmitQA}
        >
          <Form.Item
            name="question"
            label="问题"
            rules={[{ required: true, message: '请输入问题' }]}
          >
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="answer"
            label="答案"
            rules={[{ required: true, message: '请输入答案' }]}
          >
            <RichTextEditor style={{ minHeight: '150px' }} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select>
                  {categories.map(cat => (
                    <Option key={cat.id} value={cat.name}>{cat.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select>
                  <Option value="low">低</Option>
                  <Option value="medium">中</Option>
                  <Option value="high">高</Option>
                  <Option value="urgent">紧急</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="tags"
            label="标签"
            rules={[{ required: true, message: '请选择标签' }]}
          >
            <Select mode="tags" placeholder="输入或选择标签">
              {stats?.topTags.map(tag => (
                <Option key={tag.name} value={tag.name}>{tag.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="isPublic"
                label="公开显示"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="pending">待回答</Option>
                  <Option value="answered">已回答</Option>
                  <Option value="published">已发布</Option>
                  <Option value="archived">已归档</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setQaModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingQA ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ContentLibraryManagement;