import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Select,
  DatePicker,
  Form,
  Space,
  Table,
  Tag,
  Progress,
  Alert,
  Typography,
  Divider,
  Checkbox,
  Radio,
  Input,
  message,
  Modal,
  Tooltip,
  Badge
} from 'antd'
import {
  DownloadOutlined,
  ExportOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  CloudDownloadOutlined,
  HistoryOutlined,
  SettingOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select
const { TextArea } = Input

interface ExportTask {
  id: string
  name: string
  type: string
  format: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  createdAt: string
  completedAt?: string
  fileSize?: number
  downloadUrl?: string
  error?: string
}

interface ExportConfig {
  name: string
  type: string
  format: string
  dateRange: [string, string] | null
  filters: Record<string, any>
  fields: string[]
  includeDeleted: boolean
  compression: boolean
}

const DataExport: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [exportTasks, setExportTasks] = useState<ExportTask[]>([])
  const [selectedDataType, setSelectedDataType] = useState<string>('users')
  const [exportFormat, setExportFormat] = useState<string>('xlsx')
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewData, setPreviewData] = useState<any[]>([])

  // 数据类型配置
  const dataTypes = [
    { value: 'users', label: '用户数据', description: '用户基本信息、注册时间、活跃度等' },
    { value: 'posts', label: '文章数据', description: '文章内容、发布时间、互动数据等' },
    { value: 'comments', label: '评论数据', description: '评论内容、回复关系、点赞数等' },
    { value: 'flow_records', label: '心流记录', description: '用户心流状态记录和分析数据' },
    { value: 'milestones', label: '里程碑数据', description: '用户里程碑达成情况和进度' },
    { value: 'analytics', label: '分析数据', description: '用户行为分析和统计数据' },
    { value: 'system_logs', label: '系统日志', description: '系统操作日志和错误记录' },
    { value: 'backup', label: '完整备份', description: '所有数据的完整备份' }
  ]

  // 导出格式配置
  const exportFormats = [
    { value: 'xlsx', label: 'Excel (.xlsx)', icon: <FileExcelOutlined />, description: '适合数据分析和查看' },
    { value: 'csv', label: 'CSV (.csv)', icon: <FileTextOutlined />, description: '通用格式，易于导入其他系统' },
    { value: 'json', label: 'JSON (.json)', icon: <FileTextOutlined />, description: '程序化处理的标准格式' },
    { value: 'pdf', label: 'PDF (.pdf)', icon: <FilePdfOutlined />, description: '适合报告和文档' }
  ]

  // 字段配置
  const getFieldOptions = (dataType: string) => {
    const fieldConfigs: Record<string, string[]> = {
      users: ['id', 'username', 'email', 'created_at', 'last_login', 'profile_data', 'flow_score'],
      posts: ['id', 'title', 'content', 'author_id', 'created_at', 'updated_at', 'likes_count', 'comments_count'],
      comments: ['id', 'content', 'post_id', 'author_id', 'parent_id', 'created_at', 'likes_count'],
      flow_records: ['id', 'user_id', 'flow_state', 'duration', 'activity_type', 'created_at', 'metadata'],
      milestones: ['id', 'user_id', 'milestone_type', 'target_value', 'current_value', 'achieved_at', 'created_at'],
      analytics: ['date', 'metric_name', 'metric_value', 'user_segment', 'device_type'],
      system_logs: ['id', 'level', 'message', 'user_id', 'ip_address', 'created_at', 'metadata']
    }
    return fieldConfigs[dataType] || []
  }

  // 加载导出任务历史
  const loadExportTasks = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      const mockTasks: ExportTask[] = [
        {
          id: '1',
          name: '用户数据导出_20241201',
          type: 'users',
          format: 'xlsx',
          status: 'completed',
          progress: 100,
          createdAt: '2024-12-01 10:30:00',
          completedAt: '2024-12-01 10:32:15',
          fileSize: 2048576,
          downloadUrl: '/exports/users_20241201.xlsx'
        },
        {
          id: '2',
          name: '文章数据导出_20241201',
          type: 'posts',
          format: 'csv',
          status: 'processing',
          progress: 65,
          createdAt: '2024-12-01 11:00:00'
        },
        {
          id: '3',
          name: '完整备份_20241130',
          type: 'backup',
          format: 'json',
          status: 'failed',
          progress: 0,
          createdAt: '2024-11-30 23:00:00',
          error: '磁盘空间不足'
        }
      ]
      setExportTasks(mockTasks)
    } catch (error) {
      message.error('加载导出任务失败')
    } finally {
      setLoading(false)
    }
  }

  // 预览数据
  const previewExportData = async () => {
    setLoading(true)
    try {
      // 模拟数据预览
      const mockPreviewData = [
        { id: 1, username: 'user1', email: '<EMAIL>', created_at: '2024-01-01' },
        { id: 2, username: 'user2', email: '<EMAIL>', created_at: '2024-01-02' },
        { id: 3, username: 'user3', email: '<EMAIL>', created_at: '2024-01-03' }
      ]
      setPreviewData(mockPreviewData)
      setPreviewVisible(true)
    } catch (error) {
      message.error('预览数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 开始导出
  const startExport = async (values: any) => {
    setLoading(true)
    try {
      const exportConfig: ExportConfig = {
        name: values.exportName || `${selectedDataType}_${dayjs().format('YYYYMMDD_HHmmss')}`,
        type: selectedDataType,
        format: exportFormat,
        dateRange: values.dateRange ? [values.dateRange[0].format('YYYY-MM-DD'), values.dateRange[1].format('YYYY-MM-DD')] : null,
        filters: values.filters || {},
        fields: values.fields || getFieldOptions(selectedDataType),
        includeDeleted: values.includeDeleted || false,
        compression: values.compression || false
      }

      // 模拟创建导出任务
      const newTask: ExportTask = {
        id: Date.now().toString(),
        name: exportConfig.name,
        type: exportConfig.type,
        format: exportConfig.format,
        status: 'pending',
        progress: 0,
        createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }

      setExportTasks(prev => [newTask, ...prev])
      message.success('导出任务已创建，正在处理中...')
      form.resetFields()

      // 模拟进度更新
      setTimeout(() => {
        setExportTasks(prev => prev.map(task => 
          task.id === newTask.id ? { ...task, status: 'processing', progress: 30 } : task
        ))
      }, 1000)

      setTimeout(() => {
        setExportTasks(prev => prev.map(task => 
          task.id === newTask.id ? { 
            ...task, 
            status: 'completed', 
            progress: 100,
            completedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            fileSize: Math.floor(Math.random() * 10000000),
            downloadUrl: `/exports/${newTask.name}.${exportConfig.format}`
          } : task
        ))
      }, 5000)

    } catch (error) {
      message.error('创建导出任务失败')
    } finally {
      setLoading(false)
    }
  }

  // 下载文件
  const downloadFile = (task: ExportTask) => {
    if (task.downloadUrl) {
      // 模拟下载
      message.success(`开始下载 ${task.name}`)
    }
  }

  // 删除任务
  const deleteTask = (taskId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个导出任务吗？',
      onOk: () => {
        setExportTasks(prev => prev.filter(task => task.id !== taskId))
        message.success('任务已删除')
      }
    })
  }

  useEffect(() => {
    loadExportTasks()
  }, [])

  // 任务表格列定义
  const taskColumns: ColumnsType<ExportTask> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true
    },
    {
      title: '数据类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const config = dataTypes.find(dt => dt.value === type)
        return config ? config.label : type
      }
    },
    {
      title: '格式',
      dataIndex: 'format',
      key: 'format',
      render: (format: string) => {
        const config = exportFormats.find(ef => ef.value === format)
        return config ? (
          <Space>
            {config.icon}
            {format.toUpperCase()}
          </Space>
        ) : format
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: ExportTask) => {
        const statusConfig = {
          pending: { color: 'default', icon: <ClockCircleOutlined />, text: '等待中' },
          processing: { color: 'processing', icon: <ReloadOutlined spin />, text: '处理中' },
          completed: { color: 'success', icon: <CheckCircleOutlined />, text: '已完成' },
          failed: { color: 'error', icon: <ExclamationCircleOutlined />, text: '失败' }
        }
        const config = statusConfig[status as keyof typeof statusConfig]
        return (
          <Space>
            <Badge status={config.color as any} />
            {config.icon}
            {config.text}
            {status === 'processing' && (
              <Progress percent={record.progress} size="small" style={{ width: 60 }} />
            )}
          </Space>
        )
      }
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size?: number) => {
        if (!size) return '-'
        const units = ['B', 'KB', 'MB', 'GB']
        let unitIndex = 0
        let fileSize = size
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
          fileSize /= 1024
          unitIndex++
        }
        return `${fileSize.toFixed(1)} ${units[unitIndex]}`
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: ExportTask) => (
        <Space>
          {record.status === 'completed' && record.downloadUrl && (
            <Tooltip title="下载文件">
              <Button
                type="link"
                icon={<DownloadOutlined />}
                onClick={() => downloadFile(record)}
              />
            </Tooltip>
          )}
          {record.error && (
            <Tooltip title={record.error}>
              <Button type="link" icon={<EyeOutlined />} />
            </Tooltip>
          )}
          <Tooltip title="删除任务">
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => deleteTask(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>数据导出</Title>
      
      <Row gutter={24}>
        {/* 导出配置 */}
        <Col xs={24} lg={10}>
          <Card title="创建导出任务" style={{ marginBottom: '24px' }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={startExport}
            >
              <Form.Item label="数据类型" required>
                <Select
                  value={selectedDataType}
                  onChange={setSelectedDataType}
                  placeholder="选择要导出的数据类型"
                >
                  {dataTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      <div>
                        <div>{type.label}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {type.description}
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label="导出格式" required>
                <Radio.Group
                  value={exportFormat}
                  onChange={e => setExportFormat(e.target.value)}
                >
                  {exportFormats.map(format => (
                    <Radio.Button key={format.value} value={format.value}>
                      <Space>
                        {format.icon}
                        {format.label}
                      </Space>
                    </Radio.Button>
                  ))}
                </Radio.Group>
                <div style={{ marginTop: '8px' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {exportFormats.find(f => f.value === exportFormat)?.description}
                  </Text>
                </div>
              </Form.Item>

              <Form.Item name="exportName" label="任务名称">
                <Input placeholder="留空将自动生成" />
              </Form.Item>

              <Form.Item name="dateRange" label="时间范围">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item name="fields" label="导出字段">
                <Checkbox.Group
                  options={getFieldOptions(selectedDataType).map(field => ({
                    label: field,
                    value: field
                  }))}
                />
              </Form.Item>

              <Form.Item name="includeDeleted" valuePropName="checked">
                <Checkbox>包含已删除的数据</Checkbox>
              </Form.Item>

              <Form.Item name="compression" valuePropName="checked">
                <Checkbox>启用压缩（推荐大文件使用）</Checkbox>
              </Form.Item>

              <Form.Item name="description" label="备注">
                <TextArea rows={3} placeholder="可选的导出说明" />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<ExportOutlined />}
                    loading={loading}
                  >
                    开始导出
                  </Button>
                  <Button
                    icon={<EyeOutlined />}
                    onClick={previewExportData}
                    loading={loading}
                  >
                    预览数据
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 导出任务列表 */}
        <Col xs={24} lg={14}>
          <Card
            title={
              <Space>
                <HistoryOutlined />
                <span>导出任务历史</span>
              </Space>
            }
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={loadExportTasks}
                loading={loading}
              >
                刷新
              </Button>
            }
          >
            <Table
              columns={taskColumns}
              dataSource={exportTasks}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个任务`
              }}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据预览模态框 */}
      <Modal
        title="数据预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <Alert
          message="预览说明"
          description="这里显示前几行数据作为预览，实际导出将包含所有符合条件的数据。"
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />
        <Table
          dataSource={previewData}
          columns={Object.keys(previewData[0] || {}).map(key => ({
            title: key,
            dataIndex: key,
            key
          }))}
          pagination={false}
          size="small"
          scroll={{ x: true }}
        />
      </Modal>
    </div>
  )
}

export default DataExport