import React, { useState, useContext } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Modal,
  Form,
  DatePicker,
  Progress,
  Dropdown,
  Menu,
  Popconfirm,
  Spin,
  Alert,
  notification,
  Tooltip
} from 'antd'
import { 
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  ReloadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'

const { Title } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface TaskDataType {
  id: number
  title: string
  user: string
  category: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  dueDate: string
  completionRate: number
  createdAt: string
}

const taskData: TaskDataType[] = [
  { 
    id: 1, 
    title: '每日冥想10分钟', 
    user: 'test09', 
    category: 'health',
    priority: 'medium',
    status: 'completed',
    dueDate: '2023-06-10',
    completionRate: 100,
    createdAt: '2023-06-01'
  },
  { 
    id: 2, 
    title: '阅读正能量文章', 
    user: '测试用户1', 
    category: 'habit',
    priority: 'high',
    status: 'in_progress',
    dueDate: '2023-06-11',
    completionRate: 60,
    createdAt: '2023-06-02'
  },
  { 
    id: 3, 
    title: '写一篇戒色心得', 
    user: '活跃用户2', 
    category: 'personal',
    priority: 'urgent',
    status: 'pending',
    dueDate: '2023-06-12',
    completionRate: 0,
    createdAt: '2023-06-03'
  },
  { 
    id: 4, 
    title: '运动30分钟', 
    user: '高级用户3', 
    category: 'health',
    priority: 'medium',
    status: 'cancelled',
    dueDate: '2023-06-09',
    completionRate: 0,
    createdAt: '2023-06-04'
  },
]

const taskColumns: TableProps<TaskDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '任务标题',
    dataIndex: 'title',
    key: 'title',
    render: (text: string) => (
      <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {text}
      </div>
    )
  },
  {
    title: '用户',
    dataIndex: 'user',
    key: 'user',
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    render: (category: string) => {
      const categoryMap: Record<string, { text: string, color: string }> = {
        'health': { text: '健康', color: 'green' },
        'work': { text: '工作', color: 'blue' },
        'personal': { text: '个人', color: 'purple' },
        'habit': { text: '习惯', color: 'orange' }
      }
      
      const cat = categoryMap[category] || { text: category, color: 'default' }
      return <Tag color={cat.color}>{cat.text}</Tag>
    }
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    render: (priority: string) => {
      const priorityMap: Record<string, { text: string, color: string }> = {
        'low': { text: '低', color: 'default' },
        'medium': { text: '中', color: 'blue' },
        'high': { text: '高', color: 'orange' },
        'urgent': { text: '紧急', color: 'red' }
      }
      
      const pri = priorityMap[priority] || { text: priority, color: 'default' }
      return <Tag color={pri.color}>{pri.text}</Tag>
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    onFilter: (value: string | number | boolean, record: TaskDataType) => record.status === value,
    filters: [
      { text: '待处理', value: 'pending' },
      { text: '进行中', value: 'in_progress' },
      { text: '已完成', value: 'completed' },
      { text: '已取消', value: 'cancelled' }
    ],
    render: (status: string) => {
      const statusMap: Record<string, { text: string, color: 'success' | 'processing' | 'default' | 'error', icon: React.ReactNode }> = {
        'pending': { text: '待处理', color: 'default', icon: <MoreOutlined /> },
        'in_progress': { text: '进行中', color: 'processing', icon: <ReloadOutlined spin /> },
        'completed': { text: '已完成', color: 'success', icon: <CheckCircleOutlined /> },
        'cancelled': { text: '已取消', color: 'error', icon: <CloseCircleOutlined /> }
      }
      
      const stat = statusMap[status] || { text: status, color: 'default', icon: null }
      return (
        <Tag color={stat.color} icon={stat.icon} style={{ cursor: 'pointer' }}>
          {stat.text}
        </Tag>
      )
    }
  },
  {
    title: '完成度',
    dataIndex: 'completionRate',
    key: 'completionRate',
    render: (completionRate: number) => (
      <Progress 
        percent={completionRate} 
        size="small" 
        status={completionRate === 100 ? 'success' : 'normal'} 
        format={() => `${completionRate}%`}
      />
    )
  },
  {
    title: '截止日期',
    dataIndex: 'dueDate',
    key: 'dueDate',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    render: (_, record) => (
      <Space size="middle">
        <Tooltip title="查看任务详情 (Ctrl/Cmd + Enter)">
          <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewTask(record)}>查看</Button>
        </Tooltip>
        <Tooltip title="编辑任务信息">
          <Button type="link" icon={<EditOutlined />} size="small" onClick={() => handleEditTask(record)}>编辑</Button>
        </Tooltip>
        <Dropdown 
          overlay={
            <Menu>
              <Menu.Item key="1" icon={<EyeOutlined />} onClick={() => handleViewTask(record)}>查看详情</Menu.Item>
              <Menu.Item key="2" icon={<EditOutlined />} onClick={() => handleEditTask(record)}>编辑任务</Menu.Item>
              <Menu.Divider />
              <Popconfirm
                title="确定删除此任务吗？"
                description="删除后将无法恢复，请确认操作"
                onConfirm={() => {
                  // 在实际应用中，这里会调用删除API
                  message.success(`任务 "${record.title}" 删除成功`)
                  notificationApi.success({
                    message: '删除成功',
                    description: `任务 "${record.title}" 已成功删除`
                  })
                }}
                okText="确定"
                cancelText="取消"
                placement="left"
              >
                <Menu.Item key="3" danger icon={<DeleteOutlined />}>删除任务</Menu.Item>
              </Popconfirm>
            </Menu>
          }
        >
          <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
        </Dropdown>
      </Space>
    ),
  },
]

const TaskList: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()
  const { message } = useContext(GlobalMessageContext)
  const [notificationApi, contextHolder] = notification.useNotification()
  
  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('Received values of form: ', values)
      message.success('任务创建成功')
      setIsModalVisible(false)
      form.resetFields()
    }).catch(info => {
      console.log('Validate Failed:', info)
      message.error('表单验证失败')
    })
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    setLoading(true)
    setError(null)
    // 模拟API调用
    setTimeout(() => {
      // 模拟随机错误
      if (Math.random() > 0.8) {
        setError('数据加载失败，请稍后重试')
        message.error('数据加载失败，请稍后重试')
      } else {
        message.success('数据刷新成功')
      }
      setLoading(false)
    }, 1000)
  }
  
  const handleAddTask = () => {
    showModal()
  }

  // 查看任务详情
  const handleViewTask = (record: TaskDataType) => {
    Modal.info({
      title: '任务详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <strong>任务标题：</strong>{record.title}
            </Col>
            <Col span={12}>
              <strong>用户：</strong>{record.user}
            </Col>
            <Col span={12}>
              <strong>分类：</strong>
              <Tag color={record.category === 'health' ? 'green' : record.category === 'work' ? 'blue' : record.category === 'personal' ? 'purple' : 'orange'}>
                {record.category === 'health' ? '健康' : record.category === 'work' ? '工作' : record.category === 'personal' ? '个人' : '习惯'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>优先级：</strong>
              <Tag color={record.priority === 'urgent' ? 'red' : record.priority === 'high' ? 'orange' : record.priority === 'medium' ? 'blue' : 'default'}>
                {record.priority === 'urgent' ? '紧急' : record.priority === 'high' ? '高' : record.priority === 'medium' ? '中' : '低'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={record.status === 'completed' ? 'success' : record.status === 'in_progress' ? 'processing' : record.status === 'pending' ? 'warning' : 'error'}>
                {record.status === 'completed' ? '已完成' : record.status === 'in_progress' ? '进行中' : record.status === 'pending' ? '待处理' : '已取消'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>完成度：</strong>
              <Progress percent={record.completionRate} size="small" />
            </Col>
            <Col span={12}>
              <strong>截止日期：</strong>{record.dueDate}
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{record.createdAt}
            </Col>
          </Row>
        </div>
      ),
    });
  };

  // 编辑任务
  const handleEditTask = (record: TaskDataType) => {
    form.setFieldsValue({
      title: record.title,
      category: record.category,
      priority: record.priority,
      status: record.status,
      dueDate: record.dueDate
    });
    setIsModalVisible(true);
  };

  return (
    <>
      {contextHolder}
      <Space direction="vertical" style={{ width: '100%' }}>
        <Title level={3}>任务管理</Title>
      
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <QuickActions 
              onRefresh={handleRefresh}
              onAdd={handleAddTask}
              showRefresh
              showAdd
              refreshLoading={loading}
            />
          </Col>
        </Row>
        
        <SearchFilter 
          onSearch={handleSearch}
          onRefresh={handleRefresh}
          placeholder="搜索任务标题/用户"
          filters={[
            {
              key: 'status',
              label: '任务状态',
              type: 'select',
              multiple: true,
              options: [
                { value: 'pending', label: '待处理' },
                { value: 'in_progress', label: '进行中' },
                { value: 'completed', label: '已完成' },
                { value: 'cancelled', label: '已取消' }
              ]
            },
            {
              key: 'category',
              label: '任务分类',
              type: 'select',
              multiple: true,
              options: [
                { value: 'health', label: '健康' },
                { value: 'work', label: '工作' },
                { value: 'personal', label: '个人' },
                { value: 'habit', label: '习惯' }
              ]
            },
            {
              key: 'priority',
              label: '优先级',
              type: 'select',
              multiple: true,
              options: [
                { value: 'low', label: '低' },
                { value: 'medium', label: '中' },
                { value: 'high', label: '高' },
                { value: 'urgent', label: '紧急' }
              ]
            },
            {
              key: 'dateRange',
              label: '日期范围',
              type: 'dateRange'
            }
          ]}
          loading={loading}
        />
        
        {error && (
          <Alert 
            message="错误" 
            description={error} 
            type="error" 
            showIcon 
            style={{ marginTop: 16 }}
          />
        )}
        
        <Spin spinning={loading}>
          <Table 
            columns={taskColumns} 
            dataSource={taskData} 
            pagination={{ 
              showSizeChanger: true, 
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 条记录`,
              defaultPageSize: 20
            }}
            scroll={{ x: 1500 }}
            style={{ marginTop: 16 }}
          />
        </Spin>
      </Card>
      
      <Modal
        title="新增任务"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
        maskClosable={false}
        keyboard={false}
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="title" 
            label="任务标题" 
            rules={[{ required: true, message: '请输入任务标题' }]}
            tooltip="任务标题应简洁明了地描述任务内容"
          >
            <Input placeholder="请输入任务标题" autoFocus />
          </Form.Item>
          <Form.Item 
            name="user" 
            label="用户" 
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item 
            name="category" 
            label="分类" 
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              <Option value="health">健康</Option>
              <Option value="work">工作</Option>
              <Option value="personal">个人</Option>
              <Option value="habit">习惯</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="priority" 
            label="优先级" 
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="请选择优先级">
              <Option value="low">低</Option>
              <Option value="medium">中</Option>
              <Option value="high">高</Option>
              <Option value="urgent">紧急</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="dueDate" 
            label="截止日期" 
            rules={[{ required: true, message: '请选择截止日期' }]}
            tooltip="选择截止日期后，系统会自动计算任务剩余天数并在列表中显示"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
    </>
  )
}

export default TaskList