import React, { useState, useContext, useEffect } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Dropdown,
  Menu,
  Switch,
  Tabs,
  Statistic,
  Popconfirm,
  Spin,
  Alert
} from 'antd'
import message from 'antd/lib/message'
import { 
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  SendOutlined,
  FileOutlined,
  Bar<PERSON><PERSON>Outlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { notificationService, type NotificationWithProfile } from '../../services/notificationService'

const { Title } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface NotificationDataType {
  id: string
  title: string
  content: string
  type: 'system' | 'achievement' | 'task' | 'user'
  user_id: string
  is_read: boolean
  created_at: string
}

interface NotificationStats {
  total: number
  unread: number
  read: number
}

// 模拟数据已移除，将使用真实的Supabase数据

// 简化版本，只保留通知管理功能

const NotificationList: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('notifications')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()
  
  // 状态管理
  const [notifications, setNotifications] = useState<NotificationDataType[]>([])
  const [stats, setStats] = useState<NotificationStats>({
    total: 0,
    unread: 0,
    read: 0
  })
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  
  // 数据加载函数
  const loadNotifications = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await notificationService.getAllNotifications()
      if (result.success && result.data) {
        // 转换数据格式以匹配NotificationDataType
        const formattedData: NotificationDataType[] = result.data.map(item => ({
          id: item.id,
          title: item.title,
          content: item.content,
          type: item.type,
          user_id: item.user_id,
          is_read: item.is_read,
          created_at: item.created_at
        }))
        setNotifications(formattedData)
      } else {
        setError(result.error || '加载通知失败')
      }
    } catch (error) {
      console.error('加载通知失败:', error)
      setError('加载通知失败')
    } finally {
      setLoading(false)
    }
  }
  
  const loadStats = async () => {
    try {
      const result = await notificationService.getNotificationStats()
      if (result.success && result.data) {
        setStats({
          total: result.data.total || 0,
          unread: result.data.unread || 0,
          read: result.data.read || 0
        })
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }
  
  // 组件加载时获取数据
  useEffect(() => {
    loadNotifications()
    loadStats()
  }, [])
  
  // 定义通知表格列
  const notificationColumns: TableProps<NotificationDataType>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      render: (content: string) => (
        <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {content}
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap: Record<string, { text: string, color: string }> = {
          'system': { text: '系统通知', color: 'red' },
          'achievement': { text: '成就通知', color: 'purple' },
          'task': { text: '任务通知', color: 'blue' },
          'user': { text: '用户通知', color: 'green' }
        }
        
        const t = typeMap[type] || { text: type, color: 'default' }
        return <Tag color={t.color}>{t.text}</Tag>
      }
    },
    {
      title: '状态',
      dataIndex: 'is_read',
      key: 'is_read',
      render: (is_read: boolean) => (
        <Tag color={is_read ? 'green' : 'orange'}>
          {is_read ? '已读' : '未读'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString('zh-CN')
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<EyeOutlined />} size="small">查看</Button>
          <Button type="link" icon={<EditOutlined />} size="small">编辑</Button>
          <Dropdown 
            overlay={
              <Menu>
                <Menu.Item key="1" icon={<EyeOutlined />}>查看详情</Menu.Item>
                <Menu.Item key="2" icon={<EditOutlined />}>编辑通知</Menu.Item>
                {!record.is_read && (
                  <Menu.Item 
                    key="3" 
                    icon={<SendOutlined />} 
                    onClick={async () => {
                      try {
                        setLoading(true);
                        const result = await notificationService.markAsRead(record.id);
                        if (result.success) {
                          message.success('标记已读成功');
                          await loadNotifications();
                          await loadStats();
                        } else {
                          message.error(result.error || '标记已读失败');
                        }
                      } catch (error) {
                        console.error('标记已读失败:', error);
                        message.error('标记已读失败');
                      } finally {
                        setLoading(false);
                      }
                    }}
                  >
                    标记已读
                  </Menu.Item>
                )}
                <Menu.Divider />
                <Popconfirm
                  title="确定删除此通知吗？"
                  description="删除后将无法恢复，请确认操作"
                  onConfirm={async () => {
                    try {
                      setLoading(true);
                      const result = await notificationService.deleteNotification(record.id)
                      if (result.success) {
                        message.success('删除成功')
                        await loadNotifications()
                        await loadStats()
                      } else {
                        message.error(result.error || '删除失败')
                      }
                    } catch (error) {
                      console.error('删除失败:', error)
                      message.error('删除失败')
                    } finally {
                      setLoading(false);
                    }
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Menu.Item key="4" danger icon={<DeleteOutlined />}>删除通知</Menu.Item>
                </Popconfirm>
              </Menu>
            }
          >
            <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
          </Dropdown>
        </Space>
      ),
    },
  ]

  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true);
      const result = await notificationService.createNotification({
        title: values.title,
        content: values.content,
        type: values.type,
        user_id: values.user_id,
        is_read: false
      })
      
      if (result.success) {
        message.success('通知创建成功')
        setIsModalVisible(false)
        form.resetFields()
        await loadNotifications() // 重新加载通知列表
        await loadStats() // 重新加载统计数据
      } else {
        message.error(result.error || '创建通知失败')
      }
    } catch (error) {
      console.error('创建通知失败:', error)
      message.error('表单验证失败或创建通知失败')
    } finally {
      setLoading(false);
    }
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = async (searchText?: string, filters?: Record<string, any>) => {
    try {
      setLoading(true)
      setError(null)
      
      // 如果有搜索文本，使用搜索功能
      if (searchText) {
        const result = await notificationService.searchNotifications(searchText)
        if (result.success && result.data) {
          // 转换数据格式以匹配NotificationDataType
          const formattedData: NotificationDataType[] = result.data.map(item => ({
            id: item.id,
            title: item.title,
            content: item.content,
            type: item.type,
            user_id: item.user_id,
            is_read: item.is_read,
            created_at: item.created_at
          }))
          setNotifications(formattedData)
        }
      } 
      // 如果有过滤器，根据过滤器获取数据
      else if (filters && (filters.type || filters.is_read !== undefined)) {
        // 这里可以添加根据类型或阅读状态过滤的逻辑
        // 目前先重新加载所有通知
        await loadNotifications()
      }
      // 否则重新加载所有通知
      else {
        await loadNotifications()
      }
      
      message.success('搜索完成')
    } catch (error) {
      console.error('搜索失败:', error)
      message.error('搜索失败')
    } finally {
      setLoading(false)
    }
  }
  
  const handleRefresh = async () => {
    await loadNotifications()
    await loadStats()
    message.success('数据刷新成功')
  }
  
  const handleAddNotification = () => {
    showModal()
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>通知管理</Title>
      
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<Space><SendOutlined />通知管理</Space>} key="notifications">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  onAdd={handleAddNotification}
                  showRefresh
                  showAdd
                  refreshLoading={loading}
                />
              </Col>
              <Col>
                {selectedRowKeys.length > 0 && (
                  <Space>
                    <Button 
                      type="primary" 
                      onClick={async () => {
                        try {
                          setLoading(true);
                          const result = await notificationService.markMultipleAsRead(selectedRowKeys as string[]);
                          if (result.success) {
                            message.success('批量标记已读成功');
                            setSelectedRowKeys([]);
                            await loadNotifications();
                            await loadStats();
                          } else {
                            message.error(result.error || '批量标记已读失败');
                          }
                        } catch (error) {
                          console.error('批量标记已读失败:', error);
                          message.error('批量标记已读失败');
                        } finally {
                          setLoading(false);
                        }
                      }}
                    >
                      批量标记已读
                    </Button>
                    <Popconfirm
                      title={`确定删除选中的 ${selectedRowKeys.length} 条通知吗？`}
                      description="删除后将无法恢复，请确认操作"
                      onConfirm={async () => {
                        try {
                          setLoading(true);
                          const result = await notificationService.deleteMultipleNotifications(selectedRowKeys as string[]);
                          if (result.success) {
                            message.success('批量删除成功');
                            setSelectedRowKeys([]);
                            await loadNotifications();
                            await loadStats();
                          } else {
                            message.error(result.error || '批量删除失败');
                          }
                        } catch (error) {
                          console.error('批量删除失败:', error);
                          message.error('批量删除失败');
                        } finally {
                          setLoading(false);
                        }
                      }}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button danger>批量删除</Button>
                    </Popconfirm>

                  </Space>
                )}
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索通知标题/内容"
              filters={[
                {
                  key: 'type',
                  label: '通知类型',
                  type: 'select',
                  options: [
                    { value: 'system', label: '系统通知' },
                    { value: 'achievement', label: '成就通知' },
                    { value: 'task', label: '任务通知' },
                    { value: 'user', label: '用户通知' }
                  ]
                },
                {
                  key: 'is_read',
                  label: '阅读状态',
                  type: 'select',
                  options: [
                    { value: 'true', label: '已读' },
                    { value: 'false', label: '未读' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                rowSelection={{
                  selectedRowKeys,
                  onChange: setSelectedRowKeys,
                }}
                columns={notificationColumns} 
                dataSource={notifications} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  defaultPageSize: 20
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
          
          <TabPane tab={<Space><FileOutlined />模板管理</Space>} key="templates">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索模板名称/内容"
              filters={[
                {
                  key: 'type',
                  label: '通知类型',
                  type: 'select',
                  options: [
                    { value: 'system', label: '系统通知' },
                    { value: 'activity', label: '活动通知' },
                    { value: 'reminder', label: '提醒通知' },
                    { value: 'achievement', label: '成就通知' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              模板管理功能开发中...
            </div>
          </TabPane>
          
          <TabPane tab={<Space><BarChartOutlined />统计分析</Space>} key="statistics">
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="总通知数" value={stats.total} prefix={<SendOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="未读通知" value={stats.unread} valueStyle={{ color: '#cf1322' }} prefix={<SendOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="已读通知" value={stats.read} valueStyle={{ color: '#3f8600' }} prefix={<FileOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="送达率" value="92%" prefix={<BarChartOutlined />} />
                </Card>
              </Col>
            </Row>
            
            <Card title="通知发送趋势">
              <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <img src="https://placehold.co/600x300" alt="通知发送趋势图表" />
              </div>
            </Card>
          </TabPane>
          
          <TabPane tab={<Space><ReloadOutlined />推送记录</Space>} key="push-records">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索推送记录"
              filters={[
                {
                  key: 'status',
                  label: '推送状态',
                  type: 'select',
                  options: [
                    { value: 'sent', label: '已发送' },
                    { value: 'failed', label: '发送失败' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              推送记录功能开发中...
            </div>
          </TabPane>
        </Tabs>
      </Card>
      
      <Modal
        title="发送通知"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="title" 
            label="通知标题" 
            rules={[{ required: true, message: '请输入通知标题' }]}
          >
            <Input placeholder="请输入通知标题" />
          </Form.Item>
          <Form.Item 
            name="content" 
            label="通知内容" 
            rules={[{ required: true, message: '请输入通知内容' }]}
          >
            <Input.TextArea placeholder="请输入通知内容" rows={4} />
          </Form.Item>
          <Form.Item 
            name="type" 
            label="通知类型" 
            rules={[{ required: true, message: '请选择通知类型' }]}
          >
            <Select placeholder="请选择通知类型">
              <Option value="system">系统通知</Option>
              <Option value="achievement">成就通知</Option>
              <Option value="task">任务通知</Option>
              <Option value="user">用户通知</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="user_id" 
            label="接收用户ID" 
            rules={[{ required: true, message: '请输入接收用户ID' }]}
          >
            <Input placeholder="请输入接收用户的ID" />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default NotificationList