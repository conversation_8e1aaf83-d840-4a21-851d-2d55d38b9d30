import React, { useState, useEffect } from 'react';
import { Card, Button, Form, Input, Select, List, Badge, message, Space, Typography, Divider } from 'antd';
import { BellOutlined, SendOutlined, ReloadOutlined } from '@ant-design/icons';
import { notificationService } from '../../services/notificationService';
import type { NotificationWithProfile } from '../../services/notificationService';
import { supabase } from '../../utils/supabase';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface NotificationDemoProps {}

const NotificationDemo: React.FC<NotificationDemoProps> = () => {
  const [notifications, setNotifications] = useState<NotificationWithProfile[]>([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [form] = Form.useForm();

  // 加载通知列表
  const loadNotifications = async () => {
    setLoading(true);
    try {
      const result = await notificationService.getAllNotifications();
      if (result.success && result.data) {
        setNotifications(result.data);
      } else {
        throw new Error(result.error || '获取通知失败');
      }
    } catch (error) {
      console.error('加载通知失败:', error);
      message.error('加载通知失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建通知
  const handleCreateNotification = async (values: any) => {
    setSending(true);
    try {
      await notificationService.createNotification({
        title: values.title,
        content: values.content,
        type: values.type,
        user_id: values.user_id || null,
        is_read: false
      });
      message.success('通知创建成功');
      form.resetFields();
      loadNotifications();
    } catch (error) {
      console.error('创建通知失败:', error);
      message.error('创建通知失败');
    } finally {
      setSending(false);
    }
  };

  // 标记通知为已读
  const handleMarkAsRead = async (id: string) => {
    try {
      await notificationService.markAsRead(id);
      message.success('已标记为已读');
      loadNotifications();
    } catch (error) {
      console.error('标记已读失败:', error);
      message.error('标记已读失败');
    }
  };

  // 设置实时订阅
  useEffect(() => {
    loadNotifications();

    // 订阅通知表的变化
    const subscription = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications'
        },
        (payload: any) => {
          console.log('通知实时更新:', payload);
          loadNotifications(); // 重新加载通知列表
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <BellOutlined /> 实时通知演示
      </Title>
      <Text type="secondary">
        这个页面演示了实时通知功能，包括创建通知、接收实时更新和标记已读。
      </Text>

      <Divider />

      <div style={{ display: 'flex', gap: '24px', flexWrap: 'wrap' }}>
        {/* 创建通知表单 */}
        <Card title="创建通知" style={{ flex: '1', minWidth: '400px' }}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCreateNotification}
          >
            <Form.Item
              name="title"
              label="通知标题"
              rules={[{ required: true, message: '请输入通知标题' }]}
            >
              <Input placeholder="请输入通知标题" />
            </Form.Item>

            <Form.Item
              name="content"
              label="通知内容"
              rules={[{ required: true, message: '请输入通知内容' }]}
            >
              <TextArea rows={3} placeholder="请输入通知内容" />
            </Form.Item>

            <Form.Item
              name="type"
              label="通知类型"
              rules={[{ required: true, message: '请选择通知类型' }]}
            >
              <Select placeholder="请选择通知类型">
                <Option value="system">系统通知</Option>
                <Option value="achievement">成就通知</Option>
                <Option value="task">任务通知</Option>
                <Option value="user">用户通知</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="user_id"
              label="接收用户ID"
              help="留空则为全局通知"
            >
              <Input placeholder="请输入用户ID（可选）" />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SendOutlined />}
                loading={sending}
                block
              >
                发送通知
              </Button>
            </Form.Item>
          </Form>
        </Card>

        {/* 通知列表 */}
        <Card 
          title="通知列表" 
          style={{ flex: '1', minWidth: '400px' }}
          extra={
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadNotifications}
              loading={loading}
            >
              刷新
            </Button>
          }
        >
          <List
            loading={loading}
            dataSource={notifications}
            renderItem={(item) => (
              <List.Item
                actions={[
                  !item.is_read && (
                    <Button
                      type="link"
                      size="small"
                      onClick={() => handleMarkAsRead(item.id)}
                    >
                      标记已读
                    </Button>
                  )
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  title={
                    <Space>
                      <Badge 
                        status={item.is_read ? 'default' : 'processing'} 
                      />
                      {item.title}
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        [{item.type}]
                      </Text>
                    </Space>
                  }
                  description={
                    <div>
                      <div>{item.content}</div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {new Date(item.created_at).toLocaleString()}
                        {item.profiles && ` • 发送给: ${item.profiles.username}`}
                      </Text>
                    </div>
                  }
                />
              </List.Item>
            )}
            locale={{ emptyText: '暂无通知' }}
          />
        </Card>
      </div>
    </div>
  );
};

export default NotificationDemo;