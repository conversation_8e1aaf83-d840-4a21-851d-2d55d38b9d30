import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Avatar,
  Divider,
  Typography,
  Row,
  Col,
  Statistic,
  Timeline,
  List,

  Spin,
  Alert,
  message,
  Modal,
  Form,
  Input,
  Select
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  LikeOutlined,
  CommentOutlined,
  ShareAltOutlined,
  UserOutlined,
  CalendarOutlined,
  TagOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StarOutlined
} from '@ant-design/icons';
import { contentService } from '../../services/contentService';
import type { Post, Comment } from '../../services/contentService';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const PostDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(false);
  const [post, setPost] = useState<Post | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (id) {
      loadPost(id);
    }
  }, [id]);

  const loadPost = async (postId: string) => {
    setLoading(true);
    setError(null);
    try {
      const { success, data, error } = await contentService.getPostById(postId);
      if (!success || error) throw error;
      if (data) {
        setPost(data);
      }
    } catch (err) {
      setError(err as Error);
      message.error('加载帖子详情失败');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (values: any) => {
    if (!post) return;
    
    try {
      const { success, error } = await contentService.updatePostStatus(post.id, values.status);
      if (!success || error) throw error;
      
      message.success('状态更新成功');
      setStatusModalVisible(false);
      loadPost(post.id);
    } catch (err) {
      message.error('状态更新失败');
    }
  };

  const handleToggleFeatured = async () => {
    if (!post) return;
    
    try {
      const { success, error } = await contentService.toggleFeatured(post.id, !post.is_featured);
      if (!success || error) throw error;
      
      message.success(post.is_featured ? '已取消精选' : '已设为精选');
      loadPost(post.id);
    } catch (err) {
      message.error('操作失败');
    }
  };

  const handleDelete = async () => {
    if (!post) return;
    
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这篇帖子吗？删除后无法恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const { success, error } = await contentService.deletePost(post.id);
          if (!success || error) throw error;
          
          message.success('删除成功');
          navigate('/content');
        } catch (err) {
          message.error('删除失败');
        }
      }
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'green';
      case 'draft': return 'orange';
      case 'pending': return 'blue';
      case 'rejected': return 'red';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'published': return '已发布';
      case 'draft': return '草稿';
      case 'pending': return '待审核';
      case 'rejected': return '已拒绝';
      default: return status;
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !post) {
    return (
      <Alert
        message="加载失败"
        description={error?.message || '帖子不存在或已被删除'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => navigate('/content')}>
            返回列表
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 头部操作栏 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/content')}
          >
            返回列表
          </Button>
        </Col>
        <Col>
          <Space>
            <Button 
              icon={<EditOutlined />} 
              type="primary"
              onClick={() => navigate(`/content/edit/${post.id}`)}
            >
              编辑
            </Button>
            <Button 
              icon={<StarOutlined />}
              type={post.is_featured ? 'default' : 'primary'}
              onClick={handleToggleFeatured}
            >
              {post.is_featured ? '取消精选' : '设为精选'}
            </Button>
            <Button 
              onClick={() => setStatusModalVisible(true)}
            >
              更改状态
            </Button>
            <Button 
              icon={<DeleteOutlined />}
              danger
              onClick={handleDelete}
            >
              删除
            </Button>
          </Space>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        {/* 左侧主要内容 */}
        <Col xs={24} lg={16}>
          <Card>
            <div style={{ marginBottom: '24px' }}>
              <Space align="start">
                <Avatar 
                  size={64} 
                  src={post.profiles?.avatar_url} 
                  icon={<UserOutlined />}
                />
                <div>
                  <Title level={2} style={{ margin: 0 }}>
                    {post.title}
                    {post.is_featured && (
                      <StarOutlined style={{ color: '#faad14', marginLeft: '8px' }} />
                    )}
                  </Title>
                  <Space style={{ marginTop: '8px' }}>
                    <Text type="secondary">
                      <UserOutlined /> {post.profiles?.username || '未知用户'}
                    </Text>
                    <Text type="secondary">
                      <CalendarOutlined /> {new Date(post.created_at).toLocaleString()}
                    </Text>
                    {post.categories && (
                      <Tag color={post.categories.color} icon={<TagOutlined />}>
                        {post.categories.name}
                      </Tag>
                    )}
                    <Tag color={getStatusColor(post.status)}>
                      {getStatusText(post.status)}
                    </Tag>
                  </Space>
                </div>
              </Space>
            </div>
            
            <Divider />
            
            <div style={{ marginBottom: '24px' }}>
              <Paragraph>
                {post.content}
              </Paragraph>
            </div>
            
            <Divider />
            
            {/* 统计信息 */}
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="浏览量"
                  value={post.views}
                  prefix={<EyeOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="点赞数"
                  value={post.likes_count}
                  prefix={<LikeOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="评论数"
                  value={post.comments_count}
                  prefix={<CommentOutlined />}
                />
              </Col>
            </Row>
          </Card>
          
          {/* 评论列表 */}
          {post.comments && post.comments.length > 0 && (
            <Card title="评论列表" style={{ marginTop: '24px' }}>
              <List
                itemLayout="horizontal"
                dataSource={post.comments}
                renderItem={(comment) => (
                  <List.Item
                    actions={[
                      <Button type="link" size="small">
                        {comment.status === 'approved' ? '已审核' : '待审核'}
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar 
                          src={comment.profiles?.avatar_url} 
                          icon={<UserOutlined />} 
                        />
                      }
                      title={
                        <Space>
                          <span>{comment.profiles?.username || '未知用户'}</span>
                          <Tag color={comment.status === 'approved' ? 'green' : 'orange'}>
                            {comment.status === 'approved' ? '已审核' : '待审核'}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <div>{comment.content}</div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {new Date(comment.created_at).toLocaleString()}
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          )}
        </Col>
        
        {/* 右侧信息面板 */}
        <Col xs={24} lg={8}>
          <Card title="帖子信息">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="ID">
                {post.id}
              </Descriptions.Item>
              <Descriptions.Item label="作者">
                {post.profiles?.username || '未知用户'}
              </Descriptions.Item>
              <Descriptions.Item label="分类">
                {post.categories ? (
                  <Tag color={post.categories.color}>{post.categories.name}</Tag>
                ) : (
                  '未分类'
                )}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(post.status)}>
                  {getStatusText(post.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="精选">
                {post.is_featured ? (
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                ) : (
                  <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                )}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(post.created_at).toLocaleString()}
              </Descriptions.Item>
              {post.updated_at && (
                <Descriptions.Item label="更新时间">
                  {new Date(post.updated_at).toLocaleString()}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
          
          <Card title="操作历史" style={{ marginTop: '16px' }}>
            <Timeline>
              <Timeline.Item>
                帖子创建 - {new Date(post.created_at).toLocaleString()}
              </Timeline.Item>
              {post.updated_at && (
                <Timeline.Item color="blue">
                  最后更新 - {new Date(post.updated_at).toLocaleString()}
                </Timeline.Item>
              )}
              <Timeline.Item color={getStatusColor(post.status)}>
                当前状态: {getStatusText(post.status)}
              </Timeline.Item>
            </Timeline>
          </Card>
        </Col>
      </Row>
      
      {/* 状态更新弹窗 */}
      <Modal
        title="更改帖子状态"
        open={statusModalVisible}
        onCancel={() => setStatusModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleStatusUpdate}
          initialValues={{ status: post.status }}
        >
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value="published">已发布</Option>
              <Option value="draft">草稿</Option>
              <Option value="archived">已归档</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
              <Button onClick={() => setStatusModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PostDetail;