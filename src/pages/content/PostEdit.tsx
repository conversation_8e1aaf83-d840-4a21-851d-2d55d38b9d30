import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Select,
  Switch,
  Upload,
  Row,
  Col,
  Divider,
  Typography,
  message,
  Spin,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  UploadOutlined,
  PlusOutlined
} from '@ant-design/icons';
import RichTextEditor from '../../components/RichTextEditor';
import { contentService } from '../../services/contentService';
import type { Post, Category } from '../../services/contentService';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface PostFormData {
  title: string;
  content: string;
  status: 'published' | 'draft' | 'archived';
  category_id?: string;
  is_featured: boolean;
}

interface PostEditProps {
  mode?: 'create' | 'edit';
}

const PostEdit: React.FC<PostEditProps> = ({ mode = 'edit' }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [post, setPost] = useState<any>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    loadCategories();
    if (mode === 'edit' && id) {
      loadPost(id);
    }
  }, [mode, id]);

  const loadPost = async (postId: string) => {
    setLoading(true);
    setError(null);
    try {
      const { success, data, error } = await contentService.getPostById(postId);
      if (!success || error) throw error;
      setPost(data);
      
      // 设置表单初始值
      form.setFieldsValue({
        title: data.title || '',
        content: data.content || '',
        status: data.status || 'draft',
        category_id: data.category_id,
        is_featured: data.is_featured || false
      });
    } catch (err) {
      setError(err as Error);
      message.error('加载帖子失败');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const { success, data, error } = await contentService.getAllCategories();
      if (success && data) {
        setCategories(data);
      }
    } catch (err) {
      console.error('加载分类失败:', err);
    }
  };

  const handleSubmit = async (values: PostFormData) => {
    setSaving(true);
    try {
      const formData = {
        ...values,
        user_id: 'current-user-id' // 这里应该从认证状态获取当前用户ID
      };

      let result;
      if (mode === 'create') {
        result = await contentService.createPost(formData);
      } else {
        result = await contentService.updatePost(id!, formData);
      }

      if (!result.success || result.error) {
        throw result.error;
      }

      message.success(mode === 'create' ? '帖子创建成功' : '帖子更新成功');
      navigate('/content');
    } catch (err) {
      message.error(mode === 'create' ? '创建失败' : '更新失败');
    } finally {
      setSaving(false);
    }
  };

  const handleImageUpload = (info: any) => {
    // 这里可以实现图片上传逻辑
    if (info.file.status === 'done') {
      message.success('图片上传成功');
    } else if (info.file.status === 'error') {
      message.error('图片上传失败');
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error && mode === 'edit') {
    return (
      <Alert
        message="加载失败"
        description={error.message}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => navigate('/content')}>
            返回列表
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Space align="center">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/content')}
            >
              返回列表
            </Button>
            <Title level={3} style={{ margin: 0 }}>
              {mode === 'create' ? '新建帖子' : '编辑帖子'}
            </Title>
          </Space>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        {/* 主要编辑区域 */}
        <Col xs={24} lg={16}>
          <Card>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                status: 'draft',
                is_featured: false
              }}
            >
              <Form.Item
                name="title"
                label="标题"
                rules={[
                  { required: true, message: '请输入帖子标题' },
                  { max: 100, message: '标题不能超过100个字符' }
                ]}
              >
                <Input 
                  placeholder="请输入帖子标题"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="content"
                label="内容"
                rules={[
                  { required: true, message: '请输入帖子内容' },
                  { min: 10, message: '内容至少需要10个字符' }
                ]}
              >
                <RichTextEditor 
                  placeholder="请输入帖子内容"
                  style={{ height: '300px' }}
                />
              </Form.Item>

              {/* 图片上传 */}
              <Form.Item label="图片">
                <Upload
                  name="images"
                  listType="picture-card"
                  className="avatar-uploader"
                  showUploadList={true}
                  onChange={handleImageUpload}
                  beforeUpload={() => false} // 阻止自动上传
                >
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传图片</div>
                  </div>
                </Upload>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="category_id"
                    label="分类"
                  >
                    <Select 
                      placeholder="选择分类"
                      allowClear
                    >
                      {categories.map(category => (
                        <Option key={category.id} value={category.id}>
                          <span style={{ 
                            display: 'inline-block',
                            width: '8px',
                            height: '8px',
                            borderRadius: '50%',
                            backgroundColor: category.color,
                            marginRight: '8px'
                          }} />
                          {category.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="status"
                    label="状态"
                    rules={[{ required: true, message: '请选择状态' }]}
                  >
                    <Select>
                      <Option value="draft">草稿</Option>
                      <Option value="published">已发布</Option>
                      <Option value="archived">已归档</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="is_featured"
                label="精选帖子"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Divider />

              <Form.Item>
                <Space>
                  <Button 
                    type="primary" 
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                    size="large"
                  >
                    {mode === 'create' ? '创建帖子' : '保存更改'}
                  </Button>
                  <Button 
                    onClick={() => navigate('/content')}
                    size="large"
                  >
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 右侧预览和帮助 */}
        <Col xs={24} lg={8}>
          <Card title="发布设置" style={{ marginBottom: '16px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <strong>状态说明：</strong>
                <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                  <li>草稿：仅自己可见</li>
                  <li>已发布：所有用户可见</li>
                  <li>已归档：不再显示</li>
                </ul>
              </div>
              <div>
                <strong>精选帖子：</strong>
                <p style={{ margin: '8px 0 0 0', color: '#666' }}>
                  精选帖子将在首页显著位置展示
                </p>
              </div>
            </Space>
          </Card>

          <Card title="编辑提示">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <strong>标题建议：</strong>
                <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                  <li>简洁明了，突出重点</li>
                  <li>不超过100个字符</li>
                  <li>避免使用特殊符号</li>
                </ul>
              </div>
              <div>
                <strong>内容建议：</strong>
                <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                  <li>内容丰富，逻辑清晰</li>
                  <li>适当使用段落分隔</li>
                  <li>可以添加相关图片</li>
                </ul>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PostEdit;