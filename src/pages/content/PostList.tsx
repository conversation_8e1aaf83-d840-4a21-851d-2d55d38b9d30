import React, { useState, useContext, useEffect } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Switch,
  Modal,
  Form,
  Dropdown,
  Menu,
  Divider,
  Popconfirm,
  Tabs,
  Spin,
  Alert,
  message
} from 'antd'
import { 
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  CommentOutlined,
  LikeOutlined,
  DislikeOutlined,
  ReloadOutlined,
  FileTextOutlined,
  UnlockOutlined,
  LockOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'
import { contentService } from '../../services/contentService';
import type { Post, Comment } from '../../services/contentService';

const { Title } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface PostDataType {
  id: string
  title: string
  content: string
  author: string
  category: string
  status: 'published' | 'draft' | 'pending' | 'rejected'
  views: number
  likes: number
  comments_count: number
  created_at: string
  is_featured: boolean
  profiles?: {
    username: string
    avatar_url?: string
  }
  categories?: {
    name: string
    color: string
  }
}

interface CommentDataType {
  id: string
  post_id: string
  content: string
  status: 'approved' | 'pending' | 'rejected'
  created_at: string
  profiles?: {
    username: string
    avatar_url?: string
  }
  posts?: {
    title: string
  }
}

// 数据转换函数
const mapPostData = (post: any): PostDataType => ({
  id: post.id,
  title: post.title,
  content: post.content,
  author: post.profiles?.username || '未知用户',
  category: post.categories?.name || '未分类',
  status: post.status,
  views: post.views || 0,
  likes: post.likes_count || 0,
  comments_count: post.comments_count || 0,
  created_at: post.created_at,
  is_featured: post.is_featured || false,
  profiles: post.profiles,
  categories: post.categories
});

const mapCommentData = (comment: any): CommentDataType => ({
  id: comment.id,
  post_id: comment.post_id,
  content: comment.content,
  status: comment.status,
  created_at: comment.created_at,
  profiles: comment.profiles,
  posts: comment.posts
});

const PostList: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('posts')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()
  const { message } = useContext(GlobalMessageContext)
  
  // 数据状态
  const [postData, setPostData] = useState<PostDataType[]>([])
  const [commentData, setCommentData] = useState<CommentDataType[]>([])
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string | undefined>()
  const [categoryFilter, setCategoryFilter] = useState<string | undefined>()
  
  // 详情查看状态
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<PostDataType | CommentDataType | null>(null)
  const [detailType, setDetailType] = useState<'post' | 'comment'>('post')
  
  // 查看帖子详情
  const handleViewPost = (record: PostDataType) => {
    setSelectedRecord(record)
    setDetailType('post')
    setIsDetailModalVisible(true)
  }

  // 查看评论详情
  const handleViewComment = (record: CommentDataType) => {
    setSelectedRecord(record)
    setDetailType('comment')
    setIsDetailModalVisible(true)
  }

  // 编辑帖子
  const handleEditPost = (record: PostDataType) => {
    form.setFieldsValue({
      title: record.title,
      content: record.content,
      category: record.category,
      status: record.status
    })
    setIsModalVisible(true)
  }

  // 关闭详情模态框
  const handleDetailModalClose = () => {
    setIsDetailModalVisible(false)
    setSelectedRecord(null)
  }

  // 帖子表格列定义
  const postColumns: TableProps<PostDataType>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: string) => (
        <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {text}
        </div>
      )
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => {
        const categoryColors: Record<string, string> = {
          'confession': '#f50',
          'inspiration': '#2db7f5',
          'story': '#87d068',
          'improvement': '#108ee9',
          'warning': '#f50'
        }
        return <Tag color={categoryColors[category] || 'default'}>{category}</Tag>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap: Record<string, { text: string, color: 'success' | 'processing' | 'default' | 'error' }> = {
          'published': { text: '已发布', color: 'success' },
          'draft': { text: '草稿', color: 'default' },
          'pending': { text: '待审核', color: 'processing' },
          'rejected': { text: '已拒绝', color: 'error' }
        }
        
        const stat = statusMap[status] || { text: status, color: 'default' }
        return <Tag color={stat.color}>{stat.text}</Tag>
      }
    },
    {
      title: '精选',
      dataIndex: 'is_featured',
      key: 'is_featured',
      render: (is_featured: boolean) => (
        is_featured ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      )
    },
    {
      title: '浏览',
      dataIndex: 'views',
      key: 'views',
    },
    {
      title: '点赞',
      dataIndex: 'likes',
      key: 'likes',
    },
    {
      title: '评论',
      dataIndex: 'comments_count',
      key: 'comments_count',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewPost(record)}>查看</Button>
          <Button type="link" icon={<EditOutlined />} size="small" onClick={() => handleEditPost(record)}>编辑</Button>
          <Dropdown 
            overlay={
              <Menu>
                <Menu.Item key="1" icon={<EyeOutlined />} onClick={() => handleViewPost(record)}>查看详情</Menu.Item>
                <Menu.Item key="2" icon={<EditOutlined />} onClick={() => handleEditPost(record)}>编辑内容</Menu.Item>
                {record.status === 'pending' && (
                  <>
                    <Menu.Item key="3" icon={<CheckCircleOutlined />}>审核通过</Menu.Item>
                    <Menu.Item key="4" icon={<CloseCircleOutlined />}>拒绝内容</Menu.Item>
                  </>
                )}
                <Menu.Item key="5" icon={<LikeOutlined />}>
                  {record.is_featured ? '取消精选' : '设为精选'}
                </Menu.Item>
                <Menu.Divider />
                <Popconfirm
                  title="确定删除此内容吗？"
                  description="删除后将无法恢复，请确认操作"
                  onConfirm={() => {
                    // 在实际应用中，这里会调用删除API
                    message.success(`内容 "${record.title}" 删除成功`)
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Menu.Item key="6" danger icon={<DeleteOutlined />}>删除内容</Menu.Item>
                </Popconfirm>
              </Menu>
            }
          >
            <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
          </Dropdown>
        </Space>
      ),
    },
  ]

  // 评论表格列定义
  const commentColumns: TableProps<CommentDataType>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '帖子标题',
      dataIndex: 'posts',
      key: 'posts',
      render: (posts: any) => (
        <div style={{ maxWidth: 150, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {posts?.title || '未知帖子'}
        </div>
      )
    },
    {
      title: '作者',
      dataIndex: 'profiles',
      key: 'profiles',
      render: (profiles: any) => profiles?.username || '未知用户'
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      render: (text: string) => (
        <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {text}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap: Record<string, { text: string, color: 'success' | 'processing' | 'default' | 'error' }> = {
          'approved': { text: '已批准', color: 'success' },
          'pending': { text: '待审核', color: 'processing' },
          'rejected': { text: '已拒绝', color: 'error' }
        }
        
        const stat = statusMap[status] || { text: status, color: 'default' }
        return <Tag color={stat.color}>{stat.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewComment(record)}>查看</Button>
          <Dropdown 
            overlay={
              <Menu>
                <Menu.Item key="1" icon={<EyeOutlined />} onClick={() => handleViewComment(record)}>查看详情</Menu.Item>
                {record.status === 'pending' && (
                  <>
                    <Menu.Item key="2" icon={<CheckCircleOutlined />}>审核通过</Menu.Item>
                    <Menu.Item key="3" icon={<CloseCircleOutlined />}>拒绝评论</Menu.Item>
                  </>
                )}
                <Menu.Divider />
                <Popconfirm
                  title="确定删除此评论吗？"
                  description="删除后将无法恢复，请确认操作"
                  onConfirm={() => {
                    // 在实际应用中，这里会调用删除API
                    message.success(`评论删除成功`)
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Menu.Item key="4" danger icon={<DeleteOutlined />}>删除评论</Menu.Item>
                </Popconfirm>
              </Menu>
            }
          >
            <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
          </Dropdown>
        </Space>
      ),
    },
  ]
  
  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('Received values of form: ', values)
      message.success('内容创建成功')
      setIsModalVisible(false)
      form.resetFields()
    }).catch(info => {
      console.log('Validate Failed:', info)
      message.error('表单验证失败')
    })
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = async (searchValue?: string) => {
    const searchTerm = searchValue || searchText
    setSearchText(searchTerm)
    if (activeTab === 'posts') {
      try {
        setLoading(true)
        const result = await contentService.searchPosts(searchTerm)
        if (result.success && result.data) {
          setPostData(result.data.map(mapPostData))
          message.success('搜索完成')
        } else {
          throw new Error('搜索失败')
        }
      } catch (err) {
        message.error('搜索失败')
      } finally {
        setLoading(false)
      }
    }
  }
  
  // 加载帖子数据
  const loadPosts = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await contentService.getAllPosts()
      if (result.success && result.data) {
        setPostData(result.data.map(mapPostData))
        message.success('帖子数据加载成功')
      } else {
        throw new Error('获取数据失败')
      }
    } catch (err) {
      const errorMsg = '加载帖子数据失败'
      setError(errorMsg)
      message.error(errorMsg)
    } finally {
      setLoading(false)
    }
  }
  
  // 加载评论数据
  const loadComments = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await contentService.getAllComments()
      if (result.success && result.data) {
        setCommentData(result.data.map(mapCommentData))
        message.success('评论数据加载成功')
      } else {
        throw new Error('获取数据失败')
      }
    } catch (err) {
      const errorMsg = '加载评论数据失败'
      setError(errorMsg)
      message.error(errorMsg)
    } finally {
      setLoading(false)
    }
  }
  
  const handleRefresh = () => {
    if (activeTab === 'posts') {
      loadPosts()
    } else {
      loadComments()
    }
  }
  
  const handleAddContent = () => {
    showModal()
  }
  
  // 组件加载时获取数据
  useEffect(() => {
    if (activeTab === 'posts') {
      loadPosts()
    } else {
      loadComments()
    }
  }, [activeTab])

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>内容管理</Title>
      
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<Space><FileTextOutlined />帖子管理</Space>} key="posts">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  onAdd={handleAddContent}
                  showRefresh
                  showAdd
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索标题/作者"
              filters={[
                {
                  key: 'status',
                  label: '内容状态',
                  type: 'select',
                  options: [
                    { value: 'published', label: '已发布' },
                    { value: 'draft', label: '草稿' },
                    { value: 'pending', label: '待审核' },
                    { value: 'rejected', label: '已拒绝' }
                  ]
                },
                {
                  key: 'category',
                  label: '内容分类',
                  type: 'select',
                  options: [
                    { value: 'confession', label: '忏悔录' },
                    { value: 'inspiration', label: '励志文' },
                    { value: 'story', label: '故事汇' },
                    { value: 'improvement', label: '改过篇' },
                    { value: 'warning', label: '警钟长鸣' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={postColumns} 
                dataSource={postData} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  defaultPageSize: 20
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
                rowKey="id"
              />
            </Spin>
          </TabPane>
          
          <TabPane tab={<Space><CommentOutlined />评论管理</Space>} key="comments">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索评论内容/作者"
              filters={[
                {
                  key: 'status',
                  label: '评论状态',
                  type: 'select',
                  options: [
                    { value: 'approved', label: '已批准' },
                    { value: 'pending', label: '待审核' },
                    { value: 'rejected', label: '已拒绝' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={commentColumns} 
                dataSource={commentData} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  defaultPageSize: 20
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
                rowKey="id"
              />
            </Spin>
          </TabPane>
        </Tabs>
      </Card>
      
      <Modal
        title="发布内容"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="title" 
            label="标题" 
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请输入标题" />
          </Form.Item>
          <Form.Item 
            name="author" 
            label="作者" 
            rules={[{ required: true, message: '请输入作者' }]}
          >
            <Input placeholder="请输入作者" />
          </Form.Item>
          <Form.Item 
            name="category" 
            label="分类" 
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              <Option value="confession">忏悔录</Option>
              <Option value="inspiration">励志文</Option>
              <Option value="story">故事汇</Option>
              <Option value="improvement">改过篇</Option>
              <Option value="warning">警钟长鸣</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="content" 
            label="内容" 
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <Input.TextArea placeholder="请输入内容" rows={6} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 详情查看模态框 */}
      <Modal
        title={detailType === 'post' ? '帖子详情' : '评论详情'}
        open={isDetailModalVisible}
        onCancel={handleDetailModalClose}
        footer={[
          <Button key="close" onClick={handleDetailModalClose}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedRecord && (
          <div>
            {detailType === 'post' ? (
              <div>
                <p><strong>ID:</strong> {(selectedRecord as PostDataType).id}</p>
                <p><strong>标题:</strong> {(selectedRecord as PostDataType).title}</p>
                <p><strong>作者:</strong> {(selectedRecord as PostDataType).author}</p>
                <p><strong>分类:</strong> {(selectedRecord as PostDataType).category}</p>
                <p><strong>状态:</strong> {(selectedRecord as PostDataType).status}</p>
                <p><strong>浏览量:</strong> {(selectedRecord as PostDataType).views}</p>
                <p><strong>点赞数:</strong> {(selectedRecord as PostDataType).likes}</p>
                <p><strong>评论数:</strong> {(selectedRecord as PostDataType).comments_count}</p>
                <p><strong>创建时间:</strong> {new Date((selectedRecord as PostDataType).created_at).toLocaleString()}</p>
                <p><strong>内容:</strong></p>
                <div style={{ 
                  border: '1px solid #d9d9d9', 
                  borderRadius: '6px', 
                  padding: '12px', 
                  backgroundColor: '#fafafa',
                  maxHeight: '300px',
                  overflow: 'auto'
                }}>
                  {(selectedRecord as PostDataType).content}
                </div>
              </div>
            ) : (
              <div>
                <p><strong>ID:</strong> {(selectedRecord as CommentDataType).id}</p>
                <p><strong>帖子ID:</strong> {(selectedRecord as CommentDataType).post_id}</p>
                <p><strong>帖子标题:</strong> {(selectedRecord as CommentDataType).posts?.title || '未知帖子'}</p>
                <p><strong>作者:</strong> {(selectedRecord as CommentDataType).profiles?.username || '未知用户'}</p>
                <p><strong>状态:</strong> {(selectedRecord as CommentDataType).status}</p>
                <p><strong>创建时间:</strong> {new Date((selectedRecord as CommentDataType).created_at).toLocaleString()}</p>
                <p><strong>内容:</strong></p>
                <div style={{ 
                  border: '1px solid #d9d9d9', 
                  borderRadius: '6px', 
                  padding: '12px', 
                  backgroundColor: '#fafafa',
                  maxHeight: '300px',
                  overflow: 'auto'
                }}>
                  {(selectedRecord as CommentDataType).content}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </Space>
  )
}

export default PostList