import React, { useState, useContext } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Select,
  Upload,
  Divider,
  Tabs,
  Collapse,
  ColorPicker,
  TimePicker,
  Spin,
  Alert
} from 'antd'
import { 
  SaveOutlined,
  UploadOutlined,
  SettingOutlined,
  UserOutlined,
  FileTextOutlined,
  TrophyOutlined,
  BellOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import moment from 'moment'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs
const { Panel } = Collapse

const Settings: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { message } = useContext(GlobalMessageContext)
  
  const onFinish = (values: any) => {
    console.log('Received values of form: ', values)
    setSaving(true)
    
    // 模拟保存操作
    setTimeout(() => {
      setSaving(false)
      message.success('设置保存成功')
    }, 1000)
  }
  
  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo)
    message.error('表单验证失败，请检查输入')
  }
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    setLoading(true)
    setError(null)
    // 模拟API调用
    setTimeout(() => {
      // 模拟随机错误
      if (Math.random() > 0.8) {
        setError('数据加载失败，请稍后重试')
        message.error('数据加载失败，请稍后重试')
      } else {
        message.success('数据刷新成功')
      }
      setLoading(false)
    }, 1000)
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>系统设置</Title>
      
      <SearchFilter 
        onSearch={handleSearch}
        onRefresh={handleRefresh}
        placeholder="搜索设置项"
        loading={loading}
      />
      
      {error && (
        <Alert 
          message="错误" 
          description={error} 
          type="error" 
          showIcon 
          style={{ marginTop: 16 }}
        />
      )}
      
      <Spin spinning={loading}>
        <Tabs defaultActiveKey="1">
          <TabPane tab={<Space><SettingOutlined />基本设置</Space>} key="1">
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              initialValues={{
                siteName: 'ReBase 正能量社区',
                siteDescription: '专注于传播正能量的移动优先应用',
                allowRegistration: true,
                emailVerification: true,
                maxPostsPerDay: 5,
                maxCommentsPerDay: 20,
                postReview: true,
                minLevelToPost: 'L1',
                inviteOnly: false,
                defaultUserLevel: 'L1'
              }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={24} md={16} lg={16} xl={16}>
                  <Card title="基本设置">
                    <Form.Item
                      name="siteName"
                      label="站点名称"
                      rules={[{ required: true, message: '请输入站点名称' }]}
                    >
                      <Input placeholder="请输入站点名称" />
                    </Form.Item>
                    
                    <Form.Item
                      name="siteDescription"
                      label="站点描述"
                      rules={[{ required: true, message: '请输入站点描述' }]}
                    >
                      <TextArea placeholder="请输入站点描述" rows={3} />
                    </Form.Item>
                    
                    <Form.Item
                      name="siteLogo"
                      label="站点Logo"
                      valuePropName="fileList"
                    >
                      <Upload beforeUpload={() => false}>
                        <Button icon={<UploadOutlined />}>点击上传</Button>
                      </Upload>
                    </Form.Item>
                  </Card>
                  
                  <Card title="用户注册设置" style={{ marginTop: 16 }}>
                    <Form.Item
                      name="allowRegistration"
                      label="允许用户注册"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    
                    <Form.Item
                      name="emailVerification"
                      label="邮箱验证"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    
                    <Form.Item
                      name="inviteOnly"
                      label="仅限邀请注册"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Card>
                  
                  <Card title="内容管理设置" style={{ marginTop: 16 }}>
                    <Form.Item
                      name="maxPostsPerDay"
                      label="每日最大发帖数"
                      rules={[{ required: true, message: '请输入每日最大发帖数' }]}
                    >
                      <InputNumber min={0} max={100} style={{ width: '100%' }} />
                    </Form.Item>
                    
                    <Form.Item
                      name="maxCommentsPerDay"
                      label="每日最大评论数"
                      rules={[{ required: true, message: '请输入每日最大评论数' }]}
                    >
                      <InputNumber min={0} max={1000} style={{ width: '100%' }} />
                    </Form.Item>
                    
                    <Form.Item
                      name="postReview"
                      label="新帖子需要审核"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    
                    <Form.Item
                      name="minLevelToPost"
                      label="发帖最低等级要求"
                      rules={[{ required: true, message: '请选择发帖最低等级要求' }]}
                    >
                      <Select placeholder="请选择发帖最低等级要求">
                        <Option value="L1">L1 - 自我救赎</Option>
                        <Option value="L2">L2 - 习惯养成</Option>
                        <Option value="L3">L3 - 百日筑基</Option>
                        <Option value="L4">L4 - 年复一年</Option>
                        <Option value="L5">L5 - 千日还童</Option>
                        <Option value="L6">L6 - 逆天改命</Option>
                        <Option value="L7">L7 - 破茧成蝶</Option>
                      </Select>
                    </Form.Item>
                  </Card>
                </Col>
                
                <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                  <Card title="系统状态">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Text strong>系统版本</Text>
                      <Text type="secondary">v1.0.0</Text>
                      
                      <Divider />
                      
                      <Text strong>数据库状态</Text>
                      <Text type="success">正常运行</Text>
                      
                      <Divider />
                      
                      <Text strong>存储空间</Text>
                      <Text type="secondary">使用 2.4GB / 10GB</Text>
                      
                      <Divider />
                      
                      <Text strong>在线用户</Text>
                      <Text type="secondary">142 人</Text>
                      
                      <Divider />
                      
                      <Text strong>系统负载</Text>
                      <Text type="secondary">正常</Text>
                    </Space>
                  </Card>
                  
                  <Card title="用户等级设置" style={{ marginTop: 16 }}>
                    <Form.Item
                      name="defaultUserLevel"
                      label="新用户默认等级"
                      rules={[{ required: true, message: '请选择新用户默认等级' }]}
                    >
                      <Select placeholder="请选择新用户默认等级">
                        <Option value="L1">L1 - 自我救赎</Option>
                        <Option value="L2">L2 - 习惯养成</Option>
                        <Option value="L3">L3 - 百日筑基</Option>
                        <Option value="L4">L4 - 年复一年</Option>
                        <Option value="L5">L5 - 千日还童</Option>
                        <Option value="L6">L6 - 逆天改命</Option>
                        <Option value="L7">L7 - 破茧成蝶</Option>
                      </Select>
                    </Form.Item>
                  </Card>
                </Col>
              </Row>
              
              <Card style={{ marginTop: 16 }}>
                <Space>
                  <Button 
                    type="primary" 
                    icon={<SaveOutlined />} 
                    htmlType="submit" 
                    loading={saving}
                  >
                    保存设置
                  </Button>
                  <Button onClick={() => form.resetFields()}>重置</Button>
                </Space>
              </Card>
            </Form>
          </TabPane>
          
          <TabPane tab={<Space><UserOutlined />用户设置</Space>} key="2">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card title="用户权限管理">
                  <Form.Item
                    name="userRoles"
                    label="用户角色配置"
                  >
                    <Collapse>
                      <Panel header="管理员 (Admin)" key="admin">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Form.Item name="adminCanDeletePosts" valuePropName="checked">
                            <Switch /> 可以删除任何帖子
                          </Form.Item>
                          <Form.Item name="adminCanBanUsers" valuePropName="checked">
                            <Switch /> 可以封禁用户
                          </Form.Item>
                          <Form.Item name="adminCanModifySettings" valuePropName="checked">
                            <Switch /> 可以修改系统设置
                          </Form.Item>
                        </Space>
                      </Panel>
                      <Panel header="版主 (Moderator)" key="moderator">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Form.Item name="modCanReviewPosts" valuePropName="checked">
                            <Switch /> 可以审核帖子
                          </Form.Item>
                          <Form.Item name="modCanWarnUsers" valuePropName="checked">
                            <Switch /> 可以警告用户
                          </Form.Item>
                          <Form.Item name="modCanPinPosts" valuePropName="checked">
                            <Switch /> 可以置顶帖子
                          </Form.Item>
                        </Space>
                      </Panel>
                      <Panel header="普通用户 (User)" key="user">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Form.Item name="userCanCreatePosts" valuePropName="checked">
                            <Switch /> 可以创建帖子
                          </Form.Item>
                          <Form.Item name="userCanComment" valuePropName="checked">
                            <Switch /> 可以评论
                          </Form.Item>
                          <Form.Item name="userCanLike" valuePropName="checked">
                            <Switch /> 可以点赞
                          </Form.Item>
                        </Space>
                      </Panel>
                    </Collapse>
                  </Form.Item>
                </Card>
                
                <Card title="用户行为限制" style={{ marginTop: 16 }}>
                  <Form.Item
                    name="maxLoginAttempts"
                    label="最大登录尝试次数"
                    rules={[{ required: true, message: '请输入最大登录尝试次数' }]}
                  >
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="loginLockoutDuration"
                    label="登录锁定时长（分钟）"
                    rules={[{ required: true, message: '请输入登录锁定时长' }]}
                  >
                    <InputNumber min={5} max={1440} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="sessionTimeout"
                    label="会话超时时间（小时）"
                    rules={[{ required: true, message: '请输入会话超时时间' }]}
                  >
                    <InputNumber min={1} max={168} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="passwordMinLength"
                    label="密码最小长度"
                    rules={[{ required: true, message: '请输入密码最小长度' }]}
                  >
                    <InputNumber min={6} max={20} style={{ width: '100%' }} />
                  </Form.Item>
                </Card>
              </Col>
              
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card title="用户等级系统">
                  <Form.Item
                    name="levelUpRequirements"
                    label="等级提升要求"
                  >
                    <Collapse>
                      <Panel header="L1 → L2 (自我救赎 → 习惯养成)" key="l1-l2">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Form.Item name="l1ToL2Days" label="连续签到天数">
                            <InputNumber min={1} max={365} style={{ width: '100%' }} />
                          </Form.Item>
                          <Form.Item name="l1ToL2Posts" label="发帖数量">
                            <InputNumber min={0} max={100} style={{ width: '100%' }} />
                          </Form.Item>
                          <Form.Item name="l1ToL2Points" label="积分要求">
                            <InputNumber min={0} max={10000} style={{ width: '100%' }} />
                          </Form.Item>
                        </Space>
                      </Panel>
                      <Panel header="L2 → L3 (习惯养成 → 百日筑基)" key="l2-l3">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Form.Item name="l2ToL3Days" label="连续签到天数">
                            <InputNumber min={1} max={365} style={{ width: '100%' }} />
                          </Form.Item>
                          <Form.Item name="l2ToL3Posts" label="发帖数量">
                            <InputNumber min={0} max={100} style={{ width: '100%' }} />
                          </Form.Item>
                          <Form.Item name="l2ToL3Points" label="积分要求">
                            <InputNumber min={0} max={10000} style={{ width: '100%' }} />
                          </Form.Item>
                        </Space>
                      </Panel>
                      <Panel header="L3 → L4 (百日筑基 → 年复一年)" key="l3-l4">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Form.Item name="l3ToL4Days" label="连续签到天数">
                            <InputNumber min={1} max={365} style={{ width: '100%' }} />
                          </Form.Item>
                          <Form.Item name="l3ToL4Posts" label="发帖数量">
                            <InputNumber min={0} max={100} style={{ width: '100%' }} />
                          </Form.Item>
                          <Form.Item name="l3ToL4Points" label="积分要求">
                            <InputNumber min={0} max={10000} style={{ width: '100%' }} />
                          </Form.Item>
                        </Space>
                      </Panel>
                    </Collapse>
                  </Form.Item>
                </Card>
                
                <Card title="用户数据管理" style={{ marginTop: 16 }}>
                  <Form.Item
                    name="dataRetentionDays"
                    label="用户数据保留天数"
                    rules={[{ required: true, message: '请输入用户数据保留天数' }]}
                  >
                    <InputNumber min={30} max={3650} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="allowDataExport"
                    label="允许用户导出数据"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="allowAccountDeletion"
                    label="允许用户删除账户"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="anonymizeDeletedUsers"
                    label="删除用户时匿名化数据"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane tab={<Space><FileTextOutlined />内容设置</Space>} key="3">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card title="内容审核设置">
                  <Form.Item
                    name="autoModeration"
                    label="启用自动审核"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="sensitiveWordFilter"
                    label="敏感词过滤"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="imageModeration"
                    label="图片内容审核"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="linkModeration"
                    label="外链审核"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="moderationKeywords"
                    label="审核关键词"
                  >
                    <TextArea 
                      placeholder="请输入需要审核的关键词，用逗号分隔" 
                      rows={4} 
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="autoRejectThreshold"
                    label="自动拒绝阈值（0-100）"
                    rules={[{ required: true, message: '请输入自动拒绝阈值' }]}
                  >
                    <InputNumber min={0} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                </Card>
                
                <Card title="内容分类管理" style={{ marginTop: 16 }}>
                  <Form.Item
                    name="defaultCategory"
                    label="默认分类"
                    rules={[{ required: true, message: '请选择默认分类' }]}
                  >
                    <Select placeholder="请选择默认分类">
                      <Option value="general">综合讨论</Option>
                      <Option value="motivation">励志分享</Option>
                      <Option value="experience">经验交流</Option>
                      <Option value="question">问题求助</Option>
                      <Option value="achievement">成就展示</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="allowUserCreateCategory"
                    label="允许用户创建分类"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="categoryRequiresApproval"
                    label="新分类需要审批"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="maxCategoriesPerPost"
                    label="每篇帖子最大分类数"
                    rules={[{ required: true, message: '请输入每篇帖子最大分类数' }]}
                  >
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Card>
              </Col>
              
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card title="内容质量控制">
                  <Form.Item
                    name="minPostLength"
                    label="帖子最小字数"
                    rules={[{ required: true, message: '请输入帖子最小字数' }]}
                  >
                    <InputNumber min={1} max={1000} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="maxPostLength"
                    label="帖子最大字数"
                    rules={[{ required: true, message: '请输入帖子最大字数' }]}
                  >
                    <InputNumber min={100} max={50000} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="maxImagesPerPost"
                    label="每篇帖子最大图片数"
                    rules={[{ required: true, message: '请输入每篇帖子最大图片数' }]}
                  >
                    <InputNumber min={0} max={20} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="maxImageSize"
                    label="单张图片最大大小（MB）"
                    rules={[{ required: true, message: '请输入单张图片最大大小' }]}
                  >
                    <InputNumber min={1} max={50} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="allowedImageFormats"
                    label="允许的图片格式"
                  >
                    <Select 
                      mode="multiple" 
                      placeholder="请选择允许的图片格式"
                      style={{ width: '100%' }}
                    >
                      <Option value="jpg">JPG</Option>
                      <Option value="jpeg">JPEG</Option>
                      <Option value="png">PNG</Option>
                      <Option value="gif">GIF</Option>
                      <Option value="webp">WebP</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="requireTitleForPosts"
                    label="帖子必须有标题"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="duplicateContentCheck"
                    label="重复内容检测"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Card>
                
                <Card title="内容推荐设置" style={{ marginTop: 16 }}>
                  <Form.Item
                    name="enableContentRecommendation"
                    label="启用内容推荐"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="recommendationAlgorithm"
                    label="推荐算法"
                    rules={[{ required: true, message: '请选择推荐算法' }]}
                  >
                    <Select placeholder="请选择推荐算法">
                      <Option value="latest">最新优先</Option>
                      <Option value="popular">热门优先</Option>
                      <Option value="personalized">个性化推荐</Option>
                      <Option value="mixed">混合推荐</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="hotPostThreshold"
                    label="热门帖子阈值（点赞数）"
                    rules={[{ required: true, message: '请输入热门帖子阈值' }]}
                  >
                    <InputNumber min={1} max={1000} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="trendingTimeWindow"
                    label="趋势时间窗口（小时）"
                    rules={[{ required: true, message: '请输入趋势时间窗口' }]}
                  >
                    <InputNumber min={1} max={168} style={{ width: '100%' }} />
                  </Form.Item>
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane tab={<Space><TrophyOutlined />成就设置</Space>} key="4">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card title="成就规则配置">
                  <Form.Item
                    name="enableAchievements"
                    label="启用成就系统"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Collapse>
                    <Panel header="签到成就" key="checkin">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Form.Item name="checkin7Days" label="连续签到7天">
                          <Row gutter={8}>
                            <Col span={12}>
                              <Form.Item name="checkin7DaysPoints" noStyle>
                                <InputNumber placeholder="积分奖励" style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item name="checkin7DaysBadge" noStyle>
                                <Input placeholder="徽章名称" />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                        
                        <Form.Item name="checkin30Days" label="连续签到30天">
                          <Row gutter={8}>
                            <Col span={12}>
                              <Form.Item name="checkin30DaysPoints" noStyle>
                                <InputNumber placeholder="积分奖励" style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item name="checkin30DaysBadge" noStyle>
                                <Input placeholder="徽章名称" />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                        
                        <Form.Item name="checkin100Days" label="连续签到100天">
                          <Row gutter={8}>
                            <Col span={12}>
                              <Form.Item name="checkin100DaysPoints" noStyle>
                                <InputNumber placeholder="积分奖励" style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item name="checkin100DaysBadge" noStyle>
                                <Input placeholder="徽章名称" />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                      </Space>
                    </Panel>
                    
                    <Panel header="发帖成就" key="posting">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Form.Item name="firstPost" label="首次发帖">
                          <Row gutter={8}>
                            <Col span={12}>
                              <Form.Item name="firstPostPoints" noStyle>
                                <InputNumber placeholder="积分奖励" style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item name="firstPostBadge" noStyle>
                                <Input placeholder="徽章名称" />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                        
                        <Form.Item name="posts10" label="发帖10篇">
                          <Row gutter={8}>
                            <Col span={12}>
                              <Form.Item name="posts10Points" noStyle>
                                <InputNumber placeholder="积分奖励" style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item name="posts10Badge" noStyle>
                                <Input placeholder="徽章名称" />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                        
                        <Form.Item name="posts100" label="发帖100篇">
                          <Row gutter={8}>
                            <Col span={12}>
                              <Form.Item name="posts100Points" noStyle>
                                <InputNumber placeholder="积分奖励" style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item name="posts100Badge" noStyle>
                                <Input placeholder="徽章名称" />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                      </Space>
                    </Panel>
                    
                    <Panel header="互动成就" key="interaction">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Form.Item name="likes100" label="获得100个赞">
                          <Row gutter={8}>
                            <Col span={12}>
                              <Form.Item name="likes100Points" noStyle>
                                <InputNumber placeholder="积分奖励" style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item name="likes100Badge" noStyle>
                                <Input placeholder="徽章名称" />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                        
                        <Form.Item name="comments50" label="评论50次">
                          <Row gutter={8}>
                            <Col span={12}>
                              <Form.Item name="comments50Points" noStyle>
                                <InputNumber placeholder="积分奖励" style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item name="comments50Badge" noStyle>
                                <Input placeholder="徽章名称" />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                      </Space>
                    </Panel>
                  </Collapse>
                </Card>
              </Col>
              
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card title="奖励配置">
                  <Form.Item
                    name="pointsPerPost"
                    label="每篇帖子基础积分"
                    rules={[{ required: true, message: '请输入每篇帖子基础积分' }]}
                  >
                    <InputNumber min={0} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="pointsPerComment"
                    label="每条评论基础积分"
                    rules={[{ required: true, message: '请输入每条评论基础积分' }]}
                  >
                    <InputNumber min={0} max={50} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="pointsPerLike"
                    label="每个赞获得积分"
                    rules={[{ required: true, message: '请输入每个赞获得积分' }]}
                  >
                    <InputNumber min={0} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="pointsPerCheckin"
                    label="每日签到积分"
                    rules={[{ required: true, message: '请输入每日签到积分' }]}
                  >
                    <InputNumber min={0} max={50} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="bonusMultiplier"
                    label="连续签到奖励倍数"
                    rules={[{ required: true, message: '请输入连续签到奖励倍数' }]}
                  >
                    <InputNumber min={1} max={5} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="maxDailyPoints"
                    label="每日最大积分获取"
                    rules={[{ required: true, message: '请输入每日最大积分获取' }]}
                  >
                    <InputNumber min={50} max={1000} style={{ width: '100%' }} />
                  </Form.Item>
                </Card>
                
                <Card title="徽章系统" style={{ marginTop: 16 }}>
                  <Form.Item
                    name="enableBadges"
                    label="启用徽章系统"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="badgeDisplayLimit"
                    label="用户资料显示徽章数量"
                    rules={[{ required: true, message: '请输入用户资料显示徽章数量' }]}
                  >
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="rareBadgeThreshold"
                    label="稀有徽章获得阈值"
                    rules={[{ required: true, message: '请输入稀有徽章获得阈值' }]}
                  >
                    <InputNumber min={1000} max={50000} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="customBadgeUpload"
                    label="允许上传自定义徽章"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Card>
                
                <Card title="排行榜设置" style={{ marginTop: 16 }}>
                  <Form.Item
                    name="enableLeaderboard"
                    label="启用排行榜"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="leaderboardUpdateFrequency"
                    label="排行榜更新频率"
                    rules={[{ required: true, message: '请选择排行榜更新频率' }]}
                  >
                    <Select placeholder="请选择排行榜更新频率">
                      <Option value="realtime">实时更新</Option>
                      <Option value="hourly">每小时</Option>
                      <Option value="daily">每日</Option>
                      <Option value="weekly">每周</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="leaderboardSize"
                    label="排行榜显示人数"
                    rules={[{ required: true, message: '请输入排行榜显示人数' }]}
                  >
                    <InputNumber min={10} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane tab={<Space><BellOutlined />通知设置</Space>} key="5">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card title="通知模板管理">
                  <Form.Item
                    name="enableNotifications"
                    label="启用通知系统"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Collapse>
                    <Panel header="系统通知模板" key="system">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Form.Item name="welcomeTemplate" label="欢迎消息模板">
                          <TextArea 
                            placeholder="欢迎加入ReBase正能量社区！开始您的正能量之旅吧！" 
                            rows={3} 
                          />
                        </Form.Item>
                        
                        <Form.Item name="levelUpTemplate" label="等级提升模板">
                          <TextArea 
                            placeholder="恭喜您升级到{level}！继续加油！" 
                            rows={3} 
                          />
                        </Form.Item>
                        
                        <Form.Item name="achievementTemplate" label="成就获得模板">
                          <TextArea 
                            placeholder="恭喜您获得成就：{achievement}！" 
                            rows={3} 
                          />
                        </Form.Item>
                      </Space>
                    </Panel>
                    
                    <Panel header="互动通知模板" key="interaction">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Form.Item name="likeTemplate" label="点赞通知模板">
                          <TextArea 
                            placeholder="{user}赞了您的帖子：{title}" 
                            rows={2} 
                          />
                        </Form.Item>
                        
                        <Form.Item name="commentTemplate" label="评论通知模板">
                          <TextArea 
                            placeholder="{user}评论了您的帖子：{comment}" 
                            rows={2} 
                          />
                        </Form.Item>
                        
                        <Form.Item name="followTemplate" label="关注通知模板">
                          <TextArea 
                            placeholder="{user}关注了您" 
                            rows={2} 
                          />
                        </Form.Item>
                        
                        <Form.Item name="mentionTemplate" label="提及通知模板">
                          <TextArea 
                            placeholder="{user}在帖子中提到了您" 
                            rows={2} 
                          />
                        </Form.Item>
                      </Space>
                    </Panel>
                    
                    <Panel header="管理通知模板" key="admin">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Form.Item name="postApprovedTemplate" label="帖子通过审核模板">
                          <TextArea 
                            placeholder="您的帖子《{title}》已通过审核并发布" 
                            rows={2} 
                          />
                        </Form.Item>
                        
                        <Form.Item name="postRejectedTemplate" label="帖子被拒绝模板">
                          <TextArea 
                            placeholder="您的帖子《{title}》未通过审核，原因：{reason}" 
                            rows={3} 
                          />
                        </Form.Item>
                        
                        <Form.Item name="warningTemplate" label="警告通知模板">
                          <TextArea 
                            placeholder="您收到一个警告：{reason}。请遵守社区规则。" 
                            rows={3} 
                          />
                        </Form.Item>
                      </Space>
                    </Panel>
                  </Collapse>
                </Card>
              </Col>
              
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card title="推送配置">
                  <Form.Item
                    name="enablePushNotifications"
                    label="启用推送通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="enableEmailNotifications"
                    label="启用邮件通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="enableSMSNotifications"
                    label="启用短信通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Divider />
                  
                  <Form.Item
                    name="pushProvider"
                    label="推送服务提供商"
                    rules={[{ required: true, message: '请选择推送服务提供商' }]}
                  >
                    <Select placeholder="请选择推送服务提供商">
                      <Option value="firebase">Firebase Cloud Messaging</Option>
                      <Option value="apns">Apple Push Notification Service</Option>
                      <Option value="jpush">极光推送</Option>
                      <Option value="getui">个推</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="emailProvider"
                    label="邮件服务提供商"
                    rules={[{ required: true, message: '请选择邮件服务提供商' }]}
                  >
                    <Select placeholder="请选择邮件服务提供商">
                      <Option value="smtp">SMTP</Option>
                      <Option value="sendgrid">SendGrid</Option>
                      <Option value="mailgun">Mailgun</Option>
                      <Option value="ses">Amazon SES</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="smsProvider"
                    label="短信服务提供商"
                    rules={[{ required: true, message: '请选择短信服务提供商' }]}
                  >
                    <Select placeholder="请选择短信服务提供商">
                      <Option value="aliyun">阿里云短信</Option>
                      <Option value="tencent">腾讯云短信</Option>
                      <Option value="twilio">Twilio</Option>
                      <Option value="yunpian">云片网</Option>
                    </Select>
                  </Form.Item>
                </Card>
                
                <Card title="通知频率控制" style={{ marginTop: 16 }}>
                  <Form.Item
                    name="maxNotificationsPerHour"
                    label="每小时最大通知数"
                    rules={[{ required: true, message: '请输入每小时最大通知数' }]}
                  >
                    <InputNumber min={1} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="maxNotificationsPerDay"
                    label="每日最大通知数"
                    rules={[{ required: true, message: '请输入每日最大通知数' }]}
                  >
                    <InputNumber min={10} max={1000} style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="quietHoursStart"
                    label="免打扰开始时间"
                  >
                    <TimePicker format="HH:mm" style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="quietHoursEnd"
                    label="免打扰结束时间"
                  >
                    <TimePicker format="HH:mm" style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="batchNotifications"
                    label="批量发送通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  
                  <Form.Item
                    name="batchInterval"
                    label="批量发送间隔（分钟）"
                    rules={[{ required: true, message: '请输入批量发送间隔' }]}
                  >
                    <InputNumber min={5} max={60} style={{ width: '100%' }} />
                  </Form.Item>
                </Card>
                
                <Card title="通知优先级设置" style={{ marginTop: 16 }}>
                  <Form.Item
                    name="systemNotificationPriority"
                    label="系统通知优先级"
                    rules={[{ required: true, message: '请选择系统通知优先级' }]}
                  >
                    <Select placeholder="请选择系统通知优先级">
                      <Option value="high">高</Option>
                      <Option value="medium">中</Option>
                      <Option value="low">低</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="interactionNotificationPriority"
                    label="互动通知优先级"
                    rules={[{ required: true, message: '请选择互动通知优先级' }]}
                  >
                    <Select placeholder="请选择互动通知优先级">
                      <Option value="high">高</Option>
                      <Option value="medium">中</Option>
                      <Option value="low">低</Option>
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="marketingNotificationPriority"
                    label="营销通知优先级"
                    rules={[{ required: true, message: '请选择营销通知优先级' }]}
                  >
                    <Select placeholder="请选择营销通知优先级">
                      <Option value="high">高</Option>
                      <Option value="medium">中</Option>
                      <Option value="low">低</Option>
                    </Select>
                  </Form.Item>
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Spin>
    </Space>
  )
}

export default Settings