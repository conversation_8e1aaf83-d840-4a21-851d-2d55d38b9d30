import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Statistic,
  Row,
  Col,
  Tabs,
  Form,
  Input,
  Select,
  DatePicker,
  Modal,
  message,
  Popconfirm,
  Alert,
  Spin,
  Typography,
  Tooltip,
  Badge
} from 'antd'
import {
  WalletOutlined,
  TransactionOutlined,
  DownloadOutlined,
  ShoppingCartOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  LockOutlined,
  UnlockOutlined,
  EditOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { walletService } from '../../services/walletService'
import type { Wallet, WalletTransaction, WithdrawalRequest, Order, WalletStats } from '../../services/walletService'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { RangePicker } = DatePicker
const { Option } = Select
const { TextArea } = Input

const WalletManagement: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('wallets')
  
  // 钱包相关状态
  const [wallets, setWallets] = useState<Wallet[]>([])
  const [walletStats, setWalletStats] = useState<WalletStats | null>(null)
  const [walletPage, setWalletPage] = useState(1)
  const [walletPageSize, setWalletPageSize] = useState(20)
  const [walletTotal, setWalletTotal] = useState(0)
  
  // 交易记录状态
  const [transactions, setTransactions] = useState<WalletTransaction[]>([])
  const [transactionPage, setTransactionPage] = useState(1)
  const [transactionPageSize, setTransactionPageSize] = useState(20)
  const [transactionTotal, setTransactionTotal] = useState(0)
  
  // 提现申请状态
  const [withdrawals, setWithdrawals] = useState<WithdrawalRequest[]>([])
  const [withdrawalPage, setWithdrawalPage] = useState(1)
  const [withdrawalPageSize, setWithdrawalPageSize] = useState(20)
  const [withdrawalTotal, setWithdrawalTotal] = useState(0)
  
  // 订单状态
  const [orders, setOrders] = useState<Order[]>([])
  const [orderPage, setOrderPage] = useState(1)
  const [orderPageSize, setOrderPageSize] = useState(20)
  const [orderTotal, setOrderTotal] = useState(0)
  
  // 模态框状态
  const [adjustModalVisible, setAdjustModalVisible] = useState(false)
  const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null)
  const [withdrawalModalVisible, setWithdrawalModalVisible] = useState(false)
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRequest | null>(null)
  
  const [form] = Form.useForm()
  const [withdrawalForm] = Form.useForm()

  // 加载数据
  const loadWallets = async () => {
    try {
      setLoading(true)
      const response = await walletService.getWallets({
        page: walletPage,
        pageSize: walletPageSize
      })
      setWallets(response.data)
      setWalletTotal(response.total)
    } catch (error) {
      console.error('加载钱包列表失败:', error)
      setError('加载钱包列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadTransactions = async () => {
    try {
      setLoading(true)
      const response = await walletService.getTransactions({
        page: transactionPage,
        pageSize: transactionPageSize
      })
      setTransactions(response.data)
      setTransactionTotal(response.total)
    } catch (error) {
      console.error('加载交易记录失败:', error)
      setError('加载交易记录失败')
    } finally {
      setLoading(false)
    }
  }

  const loadWithdrawals = async () => {
    try {
      setLoading(true)
      const response = await walletService.getWithdrawals({
        page: withdrawalPage,
        pageSize: withdrawalPageSize
      })
      setWithdrawals(response.data)
      setWithdrawalTotal(response.total)
    } catch (error) {
      console.error('加载提现申请失败:', error)
      setError('加载提现申请失败')
    } finally {
      setLoading(false)
    }
  }

  const loadOrders = async () => {
    try {
      setLoading(true)
      const response = await walletService.getOrders({
        page: orderPage,
        pageSize: orderPageSize
      })
      setOrders(response.data)
      setOrderTotal(response.total)
    } catch (error) {
      console.error('加载订单列表失败:', error)
      setError('加载订单列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const stats = await walletService.getWalletStats()
      setWalletStats(stats)
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  useEffect(() => {
    loadStats()
  }, [])

  useEffect(() => {
    if (activeTab === 'wallets') {
      loadWallets()
    } else if (activeTab === 'transactions') {
      loadTransactions()
    } else if (activeTab === 'withdrawals') {
      loadWithdrawals()
    } else if (activeTab === 'orders') {
      loadOrders()
    }
  }, [activeTab, walletPage, transactionPage, withdrawalPage, orderPage])

  // 钱包列表列配置
  const walletColumns = [
    {
      title: '用户信息',
      key: 'user',
      render: (_: any, record: Wallet) => (
        <Space>
          {record.user?.avatar_url && (
            <img 
              src={record.user.avatar_url} 
              alt="avatar" 
              style={{ width: 32, height: 32, borderRadius: '50%' }}
            />
          )}
          <div>
            <div>{record.user?.username || '未知用户'}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.user?.email}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '余额',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          ¥{balance.toFixed(2)}
        </Text>
      )
    },
    {
      title: '冻结余额',
      dataIndex: 'frozen_balance',
      key: 'frozen_balance',
      render: (amount: number) => (
        <Text style={{ color: amount > 0 ? '#faad14' : '#666' }}>
          ¥{amount.toFixed(2)}
        </Text>
      )
    },
    {
      title: '总收入',
      dataIndex: 'total_income',
      key: 'total_income',
      render: (amount: number) => `¥${amount.toFixed(2)}`
    },
    {
      title: '总支出',
      dataIndex: 'total_expense',
      key: 'total_expense',
      render: (amount: number) => `¥${amount.toFixed(2)}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          active: { text: '正常', color: 'success' },
          frozen: { text: '冻结', color: 'warning' },
          closed: { text: '关闭', color: 'error' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 200,
      render: (_: any, record: Wallet) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewWallet(record)} />
          </Tooltip>
          <Tooltip title="调整余额">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => {
                setSelectedWallet(record)
                setAdjustModalVisible(true)
              }}
            />
          </Tooltip>
          {record.status === 'active' ? (
            <Popconfirm
              title="确定要冻结此钱包吗？"
              onConfirm={() => handleWalletStatusChange(record.id, 'frozen')}
            >
              <Tooltip title="冻结钱包">
                <Button type="link" icon={<LockOutlined />} size="small" danger />
              </Tooltip>
            </Popconfirm>
          ) : (
            <Popconfirm
              title="确定要解冻此钱包吗？"
              onConfirm={() => handleWalletStatusChange(record.id, 'active')}
            >
              <Tooltip title="解冻钱包">
                <Button type="link" icon={<UnlockOutlined />} size="small" />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  // 交易记录列配置
  const transactionColumns = [
    {
      title: '交易ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '用户',
      key: 'user',
      render: (_: any, record: WalletTransaction) => (
        <div>
          <div>{record.wallet?.user?.username || '未知用户'}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.wallet?.user?.email}
          </Text>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          income: { text: '收入', color: 'success' },
          expense: { text: '支出', color: 'error' },
          transfer: { text: '转账', color: 'processing' },
          refund: { text: '退款', color: 'warning' }
        }
        const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record: WalletTransaction) => (
        <Text style={{ color: record.type === 'income' ? '#52c41a' : '#ff4d4f' }}>
          {record.type === 'income' ? '+' : '-'}¥{amount.toFixed(2)}
        </Text>
      )
    },
    {
      title: '余额',
      dataIndex: 'balance_after',
      key: 'balance_after',
      render: (balance: number) => `¥${balance.toFixed(2)}`
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '处理中', color: 'processing' },
          completed: { text: '已完成', color: 'success' },
          failed: { text: '失败', color: 'error' },
          cancelled: { text: '已取消', color: 'default' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString()
    }
  ]

  // 提现申请列配置
  const withdrawalColumns = [
    {
      title: '申请ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '用户信息',
      key: 'user',
      render: (_: any, record: WithdrawalRequest) => (
        <div>
          <div>{record.user?.username || '未知用户'}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.user?.email}
          </Text>
          {record.user?.phone && (
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.user.phone}
              </Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: '提现金额',
      key: 'amount_info',
      render: (_: any, record: WithdrawalRequest) => (
        <div>
          <div>申请: ¥{record.amount.toFixed(2)}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            手续费: ¥{record.fee.toFixed(2)}
          </Text>
          <div>
            <Text strong>实际: ¥{record.actual_amount.toFixed(2)}</Text>
          </div>
        </div>
      )
    },
    {
      title: '支付方式',
      key: 'payment_info',
      render: (_: any, record: WithdrawalRequest) => (
        <div>
          <div>
            {record.payment_method === 'alipay' && '支付宝'}
            {record.payment_method === 'wechat' && '微信'}
            {record.payment_method === 'bank_card' && '银行卡'}
          </div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.payment_account}
          </Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '待处理', color: 'processing' },
          processing: { text: '处理中', color: 'warning' },
          completed: { text: '已完成', color: 'success' },
          rejected: { text: '已拒绝', color: 'error' },
          cancelled: { text: '已取消', color: 'default' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '申请时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: WithdrawalRequest) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          {record.status === 'pending' && (
            <>
              <Tooltip title="批准">
                <Button 
                  type="link" 
                  icon={<CheckOutlined />} 
                  size="small"
                  style={{ color: '#52c41a' }}
                  onClick={() => {
                    setSelectedWithdrawal(record)
                    setWithdrawalModalVisible(true)
                  }}
                />
              </Tooltip>
              <Tooltip title="拒绝">
                <Button 
                  type="link" 
                  icon={<CloseOutlined />} 
                  size="small"
                  danger
                  onClick={() => handleWithdrawalAction(record.id, 'reject')}
                />
              </Tooltip>
            </>
          )}
        </Space>
      )
    }
  ]

  // 订单列配置
  const orderColumns = [
    {
      title: '订单号',
      dataIndex: 'order_no',
      key: 'order_no'
    },
    {
      title: '用户',
      key: 'user',
      render: (_: any, record: Order) => (
        <div>
          <div>{record.user?.username || '未知用户'}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.user?.email}
          </Text>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          subscription: { text: '订阅', color: 'blue' },
          donation: { text: '捐赠', color: 'green' },
          purchase: { text: '购买', color: 'orange' },
          reward: { text: '打赏', color: 'purple' }
        }
        const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <Text strong>¥{amount.toFixed(2)}</Text>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '待支付', color: 'processing' },
          paid: { text: '已支付', color: 'success' },
          cancelled: { text: '已取消', color: 'default' },
          refunded: { text: '已退款', color: 'warning' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '支付方式',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: (method: string) => method || '-'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Order) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} size="small" />
          </Tooltip>
        </Space>
      )
    }
  ]

  // 处理钱包状态变更
  const handleWalletStatusChange = async (walletId: string, status: 'active' | 'frozen') => {
    try {
      await walletService.updateWalletStatus(walletId, status)
      message.success(`钱包${status === 'active' ? '解冻' : '冻结'}成功`)
      loadWallets()
    } catch (error) {
      message.error(`操作失败: ${error}`)
    }
  }

  // 处理提现申请
  const handleWithdrawalAction = async (id: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      await walletService.processWithdrawal(id, action, reason)
      message.success(`提现申请${action === 'approve' ? '批准' : '拒绝'}成功`)
      loadWithdrawals()
    } catch (error) {
      message.error(`操作失败: ${error}`)
    }
  }

  // 处理余额调整
  const handleBalanceAdjust = async (values: any) => {
    try {
      await walletService.adjustBalance(
        selectedWallet!.id,
        values.amount,
        values.reason
      )
      message.success('余额调整成功')
      setAdjustModalVisible(false)
      form.resetFields()
      loadWallets()
    } catch (error) {
      message.error(`调整失败: ${error}`)
    }
  }

  // 处理提现批准
  const handleWithdrawalApprove = async (values: any) => {
    try {
      await walletService.processWithdrawal(selectedWithdrawal!.id, 'approve')
      message.success('提现申请批准成功')
      setWithdrawalModalVisible(false)
      withdrawalForm.resetFields()
      loadWithdrawals()
    } catch (error) {
      message.error(`操作失败: ${error}`)
    }
  }

  const handleRefresh = () => {
    if (activeTab === 'wallets') {
      loadWallets()
    } else if (activeTab === 'transactions') {
      loadTransactions()
    } else if (activeTab === 'withdrawals') {
      loadWithdrawals()
    } else if (activeTab === 'orders') {
      loadOrders()
    }
    loadStats()
  }

  // 查看钱包详情
  const handleViewWallet = (record: Wallet) => {
    Modal.info({
      title: '钱包详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <strong>用户：</strong>{record.user?.username || '未知用户'}
            </Col>
            <Col span={12}>
              <strong>邮箱：</strong>{record.user?.email || '-'}
            </Col>
            <Col span={12}>
              <strong>余额：</strong>¥{record.balance.toFixed(2)}
            </Col>
            <Col span={12}>
              <strong>冻结余额：</strong>¥{record.frozen_balance.toFixed(2)}
            </Col>
            <Col span={12}>
              <strong>总收入：</strong>¥{record.total_income.toFixed(2)}
            </Col>
            <Col span={12}>
              <strong>总支出：</strong>¥{record.total_expense.toFixed(2)}
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={record.status === 'active' ? 'success' : record.status === 'frozen' ? 'warning' : 'error'}>
                {record.status === 'active' ? '正常' : record.status === 'frozen' ? '冻结' : '关闭'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{new Date(record.created_at).toLocaleString()}
            </Col>
          </Row>
        </div>
      )
    });
  };

  // 查看交易详情
  const handleViewTransaction = (record: WalletTransaction) => {
    Modal.info({
      title: '交易详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <strong>用户：</strong>{record.user?.username || '未知用户'}
            </Col>
            <Col span={12}>
              <strong>交易类型：</strong>
              <Tag color={record.type === 'income' ? 'green' : record.type === 'expense' ? 'red' : record.type === 'transfer' ? 'blue' : 'orange'}>
                {record.type === 'income' ? '收入' : record.type === 'expense' ? '支出' : record.type === 'transfer' ? '转账' : '退款'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>金额：</strong>
              <span style={{ color: record.type === 'income' ? '#52c41a' : '#f5222d' }}>
                {record.type === 'income' ? '+' : '-'}¥{record.amount.toFixed(2)}
              </span>
            </Col>
            <Col span={12}>
              <strong>余额：</strong>¥{record.balance_after.toFixed(2)}
            </Col>
            <Col span={24}>
              <strong>描述：</strong>{record.description || '-'}
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{new Date(record.created_at).toLocaleString()}
            </Col>
          </Row>
        </div>
      )
    });
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>钱包管理</Title>
      
      {/* 统计卡片 */}
      {walletStats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总钱包数"
                value={walletStats.totalWallets}
                prefix={<WalletOutlined />}
                suffix="个"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="活跃钱包"
                value={walletStats.activeWallets}
                prefix={<Badge status="success" />}
                suffix="个"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总余额"
                value={walletStats.totalBalance}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="待处理提现"
                value={walletStats.pendingWithdrawals}
                prefix={<Badge status="warning" />}
                suffix="笔"
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* 钱包管理 */}
          <TabPane tab={<Space><WalletOutlined />钱包管理</Space>} key="wallets">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索用户名/邮箱"
              filters={[
                {
                  key: 'status',
                  label: '钱包状态',
                  type: 'select',
                  options: [
                    { value: 'active', label: '正常' },
                    { value: 'frozen', label: '冻结' },
                    { value: 'closed', label: '关闭' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={walletColumns} 
                dataSource={wallets} 
                rowKey="id"
                pagination={{ 
                  current: walletPage,
                  pageSize: walletPageSize,
                  total: walletTotal,
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, size) => {
                    setWalletPage(page)
                    setWalletPageSize(size || 20)
                  }
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>

          {/* 交易记录 */}
          <TabPane tab={<Space><TransactionOutlined />交易记录</Space>} key="transactions">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索交易ID/用户"
              filters={[
                {
                  key: 'type',
                  label: '交易类型',
                  type: 'select',
                  options: [
                    { value: 'income', label: '收入' },
                    { value: 'expense', label: '支出' },
                    { value: 'transfer', label: '转账' },
                    { value: 'refund', label: '退款' }
                  ]
                },
                {
                  key: 'status',
                  label: '交易状态',
                  type: 'select',
                  options: [
                    { value: 'pending', label: '处理中' },
                    { value: 'completed', label: '已完成' },
                    { value: 'failed', label: '失败' },
                    { value: 'cancelled', label: '已取消' }
                  ]
                },
                {
                  key: 'dateRange',
                  label: '时间范围',
                  type: 'dateRange'
                }
              ]}
              loading={loading}
            />
            
            <Spin spinning={loading}>
              <Table 
                columns={transactionColumns} 
                dataSource={transactions} 
                rowKey="id"
                pagination={{ 
                  current: transactionPage,
                  pageSize: transactionPageSize,
                  total: transactionTotal,
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, size) => {
                    setTransactionPage(page)
                    setTransactionPageSize(size || 20)
                  }
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>

          {/* 提现管理 */}
          <TabPane tab={<Space><DownloadOutlined />提现管理</Space>} key="withdrawals">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索用户名/邮箱"
              filters={[
                {
                  key: 'status',
                  label: '申请状态',
                  type: 'select',
                  options: [
                    { value: 'pending', label: '待处理' },
                    { value: 'processing', label: '处理中' },
                    { value: 'completed', label: '已完成' },
                    { value: 'rejected', label: '已拒绝' },
                    { value: 'cancelled', label: '已取消' }
                  ]
                },
                {
                  key: 'dateRange',
                  label: '申请时间',
                  type: 'dateRange'
                }
              ]}
              loading={loading}
            />
            
            <Spin spinning={loading}>
              <Table 
                columns={withdrawalColumns} 
                dataSource={withdrawals} 
                rowKey="id"
                pagination={{ 
                  current: withdrawalPage,
                  pageSize: withdrawalPageSize,
                  total: withdrawalTotal,
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, size) => {
                    setWithdrawalPage(page)
                    setWithdrawalPageSize(size || 20)
                  }
                }}
                scroll={{ x: 1400 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>

          {/* 订单管理 */}
          <TabPane tab={<Space><ShoppingCartOutlined />订单管理</Space>} key="orders">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索订单号/用户"
              filters={[
                {
                  key: 'type',
                  label: '订单类型',
                  type: 'select',
                  options: [
                    { value: 'subscription', label: '订阅' },
                    { value: 'donation', label: '捐赠' },
                    { value: 'purchase', label: '购买' },
                    { value: 'reward', label: '打赏' }
                  ]
                },
                {
                  key: 'status',
                  label: '订单状态',
                  type: 'select',
                  options: [
                    { value: 'pending', label: '待支付' },
                    { value: 'paid', label: '已支付' },
                    { value: 'cancelled', label: '已取消' },
                    { value: 'refunded', label: '已退款' }
                  ]
                },
                {
                  key: 'dateRange',
                  label: '创建时间',
                  type: 'dateRange'
                }
              ]}
              loading={loading}
            />
            
            <Spin spinning={loading}>
              <Table 
                columns={orderColumns} 
                dataSource={orders} 
                rowKey="id"
                pagination={{ 
                  current: orderPage,
                  pageSize: orderPageSize,
                  total: orderTotal,
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, size) => {
                    setOrderPage(page)
                    setOrderPageSize(size || 20)
                  }
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
        </Tabs>
      </Card>

      {/* 余额调整模态框 */}
      <Modal
        title="调整钱包余额"
        visible={adjustModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setAdjustModalVisible(false)
          form.resetFields()
        }}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical" onFinish={handleBalanceAdjust}>
          <Form.Item label="用户信息">
            <Text>{selectedWallet?.user?.username} ({selectedWallet?.user?.email})</Text>
          </Form.Item>
          <Form.Item label="当前余额">
            <Text strong>¥{selectedWallet?.balance.toFixed(2)}</Text>
          </Form.Item>
          <Form.Item 
            name="amount" 
            label="调整金额" 
            rules={[{ required: true, message: '请输入调整金额' }]}
          >
            <Input 
              type="number" 
              placeholder="正数为增加，负数为减少" 
              addonBefore="¥"
            />
          </Form.Item>
          <Form.Item 
            name="reason" 
            label="调整原因" 
            rules={[{ required: true, message: '请输入调整原因' }]}
          >
            <TextArea rows={3} placeholder="请输入调整原因" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 提现批准模态框 */}
      <Modal
        title="批准提现申请"
        visible={withdrawalModalVisible}
        onOk={() => withdrawalForm.submit()}
        onCancel={() => {
          setWithdrawalModalVisible(false)
          withdrawalForm.resetFields()
        }}
        okText="批准"
        cancelText="取消"
      >
        <Form form={withdrawalForm} layout="vertical" onFinish={handleWithdrawalApprove}>
          <Form.Item label="申请信息">
            <div>
              <div>用户: {selectedWithdrawal?.user?.username}</div>
              <div>申请金额: ¥{selectedWithdrawal?.amount.toFixed(2)}</div>
              <div>手续费: ¥{selectedWithdrawal?.fee.toFixed(2)}</div>
              <div>实际到账: ¥{selectedWithdrawal?.actual_amount.toFixed(2)}</div>
              <div>支付方式: {selectedWithdrawal?.payment_method}</div>
              <div>支付账户: {selectedWithdrawal?.payment_account}</div>
            </div>
          </Form.Item>
          <Alert
            message="确认信息"
            description="请确认提现信息无误后批准申请，批准后将无法撤销。"
            type="warning"
            showIcon
          />
        </Form>
      </Modal>
    </Space>
  )
}

export default WalletManagement