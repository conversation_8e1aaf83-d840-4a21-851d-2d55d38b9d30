import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Statistic,
  Row,
  Col,
  message,
  Tabs,
  InputNumber,
  Switch,
  Avatar,
  Tooltip,
  Popconfirm,
  Badge
} from 'antd'
import {
  CrownOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  SettingOutlined,
  ReloadOutlined,
  SearchOutlined,
  ExportOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons'
import EChartsComponent from '../../components/EChartsComponent'
import { subscriptionPaymentService } from '../../services/subscriptionPaymentService'
import type { SubscriptionPlan, UserSubscription, PaymentRecord } from '../../services/subscriptionPaymentService'
import { createLineChartOption, createPieChartOption, createBarChartOption } from '../../utils/chartOptions'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { TabPane } = Tabs
const { Option } = Select
const { TextArea } = Input

const SubscriptionManagement: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [subscriptions, setSubscriptions] = useState<UserSubscription[]>([])
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [filters, setFilters] = useState<any>({})
  const [planModalVisible, setPlanModalVisible] = useState(false)
  const [subscriptionModalVisible, setSubscriptionModalVisible] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [selectedSubscription, setSelectedSubscription] = useState<UserSubscription | null>(null)
  const [activeTab, setActiveTab] = useState('subscriptions')
  const [stats, setStats] = useState<any>(null)

  const [form] = Form.useForm()
  const [planForm] = Form.useForm()
  const [subscriptionForm] = Form.useForm()

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      if (activeTab === 'subscriptions') {
        const response = await subscriptionPaymentService.getUserSubscriptions({
          page: currentPage,
          pageSize,
          ...filters
        })
        setSubscriptions(response.data)
        setTotal(response.total)
      } else if (activeTab === 'plans') {
        const plansData = await subscriptionPaymentService.getSubscriptionPlans()
        setPlans(plansData)
      } else if (activeTab === 'payments') {
        const response = await subscriptionPaymentService.getPaymentRecords({
          page: currentPage,
          pageSize,
          ...filters
        })
        setPayments(response.data)
        setTotal(response.total)
      } else if (activeTab === 'statistics') {
        const statsData = await subscriptionPaymentService.getSubscriptionStats()
        setStats(statsData)
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [currentPage, pageSize, filters, activeTab])

  // 搜索处理
  const handleSearch = (values: any) => {
    const newFilters = { ...values }
    if (values.dateRange) {
      newFilters.start_date = values.dateRange[0].format('YYYY-MM-DD')
      newFilters.end_date = values.dateRange[1].format('YYYY-MM-DD')
      delete newFilters.dateRange
    }
    setFilters(newFilters)
    setCurrentPage(1)
  }

  // 重置搜索
  const handleReset = () => {
    form.resetFields()
    setFilters({})
    setCurrentPage(1)
  }

  // 查看订阅详情
  const handleViewSubscription = (record: UserSubscription) => {
    Modal.info({
      title: '订阅详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <strong>用户：</strong>{record.user?.username || '未知用户'}
            </Col>
            <Col span={12}>
              <strong>计划：</strong>{record.plan?.name || '未知计划'}
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={record.status === 'active' ? 'green' : record.status === 'expired' ? 'red' : record.status === 'cancelled' ? 'gray' : 'orange'}>
                {record.status === 'active' ? '活跃' : record.status === 'expired' ? '已过期' : record.status === 'cancelled' ? '已取消' : '待激活'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>价格：</strong>¥{record.plan?.price?.toFixed(2) || '0.00'}
            </Col>
            <Col span={12}>
              <strong>开始时间：</strong>{dayjs(record.start_date).format('YYYY-MM-DD')}
            </Col>
            <Col span={12}>
              <strong>结束时间：</strong>{dayjs(record.end_date).format('YYYY-MM-DD')}
            </Col>
            <Col span={12}>
              <strong>自动续费：</strong>
              <Badge status={record.auto_renew ? 'success' : 'default'} text={record.auto_renew ? '是' : '否'} />
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{dayjs(record.created_at).format('YYYY-MM-DD HH:mm:ss')}
            </Col>
          </Row>
        </div>
      )
    });
  };

  // 查看支付记录详情
  const handleViewPayment = (record: PaymentRecord) => {
    Modal.info({
      title: '支付记录详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <strong>用户：</strong>{record.user?.username || '未知用户'}
            </Col>
            <Col span={12}>
              <strong>订单号：</strong>{record.order_id}
            </Col>
            <Col span={12}>
              <strong>金额：</strong>¥{record.amount.toFixed(2)}
            </Col>
            <Col span={12}>
              <strong>支付方式：</strong>
              <Tag color={record.payment_method === 'wechat' ? 'green' : record.payment_method === 'alipay' ? 'blue' : 'purple'}>
                {record.payment_method === 'wechat' ? '微信支付' : record.payment_method === 'alipay' ? '支付宝' : 'Stripe'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={record.status === 'completed' ? 'green' : record.status === 'failed' ? 'red' : record.status === 'refunded' ? 'gray' : 'orange'}>
                {record.status === 'completed' ? '已完成' : record.status === 'failed' ? '失败' : record.status === 'refunded' ? '已退款' : '待支付'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{dayjs(record.created_at).format('YYYY-MM-DD HH:mm:ss')}
            </Col>
          </Row>
        </div>
      )
    });
  };

  // 创建/更新订阅计划
  const handleSavePlan = async (values: any) => {
    try {
      if (selectedPlan) {
        await subscriptionPaymentService.updateSubscriptionPlan(selectedPlan.id, values)
        message.success('订阅计划更新成功')
      } else {
        await subscriptionPaymentService.createSubscriptionPlan(values)
        message.success('订阅计划创建成功')
      }
      setPlanModalVisible(false)
      setSelectedPlan(null)
      planForm.resetFields()
      loadData()
    } catch (error) {
      console.error('保存订阅计划失败:', error)
      message.error('保存订阅计划失败')
    }
  }

  // 删除订阅计划
  const handleDeletePlan = async (id: string) => {
    try {
      await subscriptionPaymentService.deleteSubscriptionPlan(id)
      message.success('订阅计划删除成功')
      loadData()
    } catch (error) {
      console.error('删除订阅计划失败:', error)
      message.error('删除订阅计划失败')
    }
  }

  // 更新订阅状态
  const handleUpdateSubscriptionStatus = async (id: string, status: string) => {
    try {
      await subscriptionPaymentService.updateUserSubscription(id, { status })
      message.success('订阅状态更新成功')
      loadData()
    } catch (error) {
      console.error('更新订阅状态失败:', error)
      message.error('更新订阅状态失败')
    }
  }

  // 订阅表格列定义
  const subscriptionColumns = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user: any) => (
        <Space>
          <Avatar src={user?.avatar} size="small">
            {user?.username?.[0]}
          </Avatar>
          <span>{user?.username || '未知用户'}</span>
        </Space>
      )
    },
    {
      title: '订阅计划',
      dataIndex: 'plan',
      key: 'plan',
      render: (plan: any) => (
        <Space>
          <CrownOutlined style={{ color: plan?.color || '#1890ff' }} />
          <span>{plan?.name}</span>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          active: { text: '活跃', color: 'green' },
          expired: { text: '已过期', color: 'red' },
          cancelled: { text: '已取消', color: 'gray' },
          pending: { text: '待激活', color: 'orange' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '开始时间',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '结束时间',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '自动续费',
      dataIndex: 'auto_renew',
      key: 'auto_renew',
      render: (autoRenew: boolean) => (
        <Badge
          status={autoRenew ? 'success' : 'default'}
          text={autoRenew ? '是' : '否'}
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: UserSubscription) => (
        <Space>
          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              onClick={() => handleUpdateSubscriptionStatus(record.id, 'active')}
            >
              激活
            </Button>
          )}
          {record.status === 'active' && (
            <Button
              type="link"
              size="small"
              danger
              onClick={() => handleUpdateSubscriptionStatus(record.id, 'cancelled')}
            >
              取消
            </Button>
          )}
          <Button
            type="link"
            size="small"
            onClick={() => handleViewSubscription(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setSelectedSubscription(record)
              setSubscriptionModalVisible(true)
            }}
          >
            编辑
          </Button>
        </Space>
      )
    }
  ]

  // 订阅计划表格列定义
  const planColumns = [
    {
      title: '计划名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: SubscriptionPlan) => (
        <Space>
          <CrownOutlined style={{ color: '#1890ff' }} />
          <span>{name}</span>
        </Space>
      )
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => (
        <span style={{ color: '#f50', fontWeight: 'bold' }}>¥{price.toFixed(2)}</span>
      )
    },
    {
      title: '周期',
      dataIndex: 'duration_type',
      key: 'duration_type',
      render: (type: string, record: SubscriptionPlan) => {
        const typeMap = {
          monthly: '月',
          yearly: '年',
          lifetime: '终身'
        }
        const unit = typeMap[type as keyof typeof typeMap] || type
        return type === 'lifetime' ? '终身' : `1${unit}`
      }
    },
    {
      title: '功能特权',
      dataIndex: 'features',
      key: 'features',
      render: (features: string[]) => (
        <div>
          {features?.slice(0, 3).map((feature, index) => (
            <Tag key={index}>{feature}</Tag>
          ))}
          {features?.length > 3 && (
            <Tooltip title={features.slice(3).join(', ')}>
              <Tag>+{features.length - 3}</Tag>
            </Tooltip>
          )}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Badge
          status={isActive ? 'success' : 'default'}
          text={isActive ? '启用' : '禁用'}
        />
      )
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: SubscriptionPlan) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setSelectedPlan(record)
              planForm.setFieldsValue(record)
              setPlanModalVisible(true)
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个订阅计划吗？"
            onConfirm={() => handleDeletePlan(record.id)}
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 支付记录表格列定义
  const paymentColumns = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user: any) => (
        <Space>
          <Avatar src={user?.avatar} size="small">
            {user?.username?.[0]}
          </Avatar>
          <span>{user?.username || '未知用户'}</span>
        </Space>
      )
    },
    {
      title: '订单号',
      dataIndex: 'order_id',
      key: 'order_id'
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#f50', fontWeight: 'bold' }}>¥{amount.toFixed(2)}</span>
      )
    },
    {
      title: '支付方式',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: (method: string) => {
        const methodMap = {
          wechat: { text: '微信支付', color: 'green' },
          alipay: { text: '支付宝', color: 'blue' },
          stripe: { text: 'Stripe', color: 'purple' }
        }
        const config = methodMap[method as keyof typeof methodMap] || { text: method, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '待支付', color: 'orange' },
          completed: { text: '已完成', color: 'green' },
          failed: { text: '失败', color: 'red' },
          refunded: { text: '已退款', color: 'gray' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    }
  ]

  // 统计卡片数据
  const getStatsCards = () => {
    if (!stats) return []
    
    return [
      {
        title: '总订阅用户',
        value: stats.total_subscribers,
        icon: <UserOutlined style={{ color: '#1890ff' }} />
      },
      {
        title: '活跃订阅',
        value: stats.active_subscriptions,
        icon: <CrownOutlined style={{ color: '#52c41a' }} />
      },
      {
        title: '本月收入',
        value: stats.monthly_revenue,
        prefix: '¥',
        precision: 2,
        icon: <DollarOutlined style={{ color: '#f50' }} />
      },
      {
        title: '续费率',
        value: stats.renewal_rate,
        suffix: '%',
        precision: 1,
        icon: <CalendarOutlined style={{ color: '#722ed1' }} />
      }
    ]
  }

  // 图表配置
  const getRevenueChartOption = () => {
    if (!stats?.revenue_trend) return {}
    
    const dates = stats.revenue_trend.map((item: any) => item.date)
    const revenues = stats.revenue_trend.map((item: any) => item.revenue)
    
    return createLineChartOption(
      '收入趋势',
      dates,
      [{ name: '收入', data: revenues }]
    )
  }

  const getPlanDistributionChartOption = () => {
    if (!stats?.plan_distribution) return {}
    
    const data = stats.plan_distribution.map((item: any) => ({
      name: item.plan_name,
      value: item.count
    }))
    
    return createPieChartOption('订阅计划分布', data)
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card title="订阅管理" extra={
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadData}>
            刷新
          </Button>
          <Button icon={<ExportOutlined />}>
            导出
          </Button>
        </Space>
      }>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="用户订阅" key="subscriptions">
            {/* 搜索表单 */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Form
                form={form}
                layout="inline"
                onFinish={handleSearch}
              >
                <Form.Item name="status" label="状态">
                  <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
                    <Option value="active">活跃</Option>
                    <Option value="expired">已过期</Option>
                    <Option value="cancelled">已取消</Option>
                    <Option value="pending">待激活</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="plan_id" label="订阅计划">
                  <Select placeholder="选择计划" allowClear style={{ width: 150 }}>
                    {plans.map(plan => (
                      <Option key={plan.id} value={plan.id}>{plan.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item name="dateRange" label="时间范围">
                  <RangePicker />
                </Form.Item>
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      搜索
                    </Button>
                    <Button onClick={handleReset}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>

            {/* 数据表格 */}
            <Table
              columns={subscriptionColumns}
              dataSource={subscriptions}
              rowKey="id"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize,
                total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: (page, size) => {
                  setCurrentPage(page)
                  setPageSize(size || 20)
                }
              }}
            />
          </TabPane>

          <TabPane tab="订阅计划" key="plans">
            <Card
              size="small"
              style={{ marginBottom: 16 }}
              extra={
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setSelectedPlan(null)
                    planForm.resetFields()
                    setPlanModalVisible(true)
                  }}
                >
                  新增计划
                </Button>
              }
            >
              <Table
                columns={planColumns}
                dataSource={plans}
                rowKey="id"
                loading={loading}
                pagination={false}
              />
            </Card>
          </TabPane>

          <TabPane tab="支付记录" key="payments">
            {/* 搜索表单 */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Form
                form={form}
                layout="inline"
                onFinish={handleSearch}
              >
                <Form.Item name="status" label="状态">
                  <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
                    <Option value="pending">待支付</Option>
                    <Option value="completed">已完成</Option>
                    <Option value="failed">失败</Option>
                    <Option value="refunded">已退款</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="payment_method" label="支付方式">
                  <Select placeholder="选择支付方式" allowClear style={{ width: 120 }}>
                    <Option value="wechat">微信支付</Option>
                    <Option value="alipay">支付宝</Option>
                    <Option value="stripe">Stripe</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="dateRange" label="时间范围">
                  <RangePicker />
                </Form.Item>
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      搜索
                    </Button>
                    <Button onClick={handleReset}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>

            {/* 数据表格 */}
            <Table
              columns={paymentColumns}
              dataSource={payments}
              rowKey="id"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize,
                total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: (page, size) => {
                  setCurrentPage(page)
                  setPageSize(size || 20)
                }
              }}
            />
          </TabPane>

          <TabPane tab="统计分析" key="statistics">
            {stats && (
              <>
                {/* 统计卡片 */}
                <Row gutter={16} style={{ marginBottom: 24 }}>
                  {getStatsCards().map((card, index) => (
                    <Col span={6} key={index}>
                      <Card>
                        <Statistic
                          title={card.title}
                          value={card.value}
                          prefix={card.prefix}
                          suffix={card.suffix}
                          precision={card.precision}
                        />
                        <div style={{ marginTop: 8 }}>{card.icon}</div>
                      </Card>
                    </Col>
                  ))}
                </Row>

                {/* 图表 */}
                <Row gutter={16}>
                  <Col span={12}>
                    <Card title="收入趋势">
                      <EChartsComponent
                        option={getRevenueChartOption()}
                        height={300}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="订阅计划分布">
                      <EChartsComponent
                        option={getPlanDistributionChartOption()}
                        height={300}
                      />
                    </Card>
                  </Col>
                </Row>
              </>
            )}
          </TabPane>
        </Tabs>
      </Card>

      {/* 订阅计划编辑模态框 */}
      <Modal
        title={selectedPlan ? '编辑订阅计划' : '新增订阅计划'}
        open={planModalVisible}
        onCancel={() => {
          setPlanModalVisible(false)
          setSelectedPlan(null)
          planForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={planForm}
          layout="vertical"
          onFinish={handleSavePlan}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="计划名称"
                rules={[{ required: true, message: '请输入计划名称' }]}
              >
                <Input placeholder="请输入计划名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  precision={2}
                  addonBefore="¥"
                  style={{ width: '100%' }}
                  placeholder="请输入价格"
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="duration_type"
                label="周期类型"
                rules={[{ required: true, message: '请选择周期类型' }]}
              >
                <Select placeholder="选择周期类型">
                  <Option value="monthly">月</Option>
                  <Option value="yearly">年</Option>
                  <Option value="lifetime">终身</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="duration_value"
                label="周期数值"
                rules={[{ required: true, message: '请输入周期数值' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="请输入周期数值"
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="计划描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入计划描述"
            />
          </Form.Item>
          
          <Form.Item
            name="features"
            label="功能特权"
            rules={[{ required: true, message: '请输入功能特权' }]}
          >
            <Select
              mode="tags"
              placeholder="输入功能特权后按回车添加"
              tokenSeparators={[',']}
            />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="color"
                label="主题色"
              >
                <Input placeholder="#1890ff" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sort_order"
                label="排序"
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="排序值"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="is_active"
                label="启用状态"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {selectedPlan ? '更新' : '创建'}
              </Button>
              <Button onClick={() => {
                setPlanModalVisible(false)
                setSelectedPlan(null)
                planForm.resetFields()
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default SubscriptionManagement