import React, { useState, useEffect } from 'react'
import {
  Card,
  Tabs,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  Progress,
  message,
  Tooltip,
  Switch,
  Divider,
  Typography,
  Badge
} from 'antd'
import {
  BellOutlined,
  MailOutlined,
  MessageOutlined,
  MobileOutlined,
  NotificationOutlined,
  PlusOutlined,
  SendOutlined,
  SettingOutlined,
  UserOutlined,
  Bar<PERSON>hartOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import RichTextEditor from '../../components/RichTextEditor'
import { notificationService } from '../../services/notificationService'
import type { NotificationTemplate, NotificationRecord, UserSubscription, NotificationStats } from '../../services/notificationService'

const { TabPane } = Tabs
const { Option } = Select
const { RangePicker } = DatePicker
const { TextArea } = Input
const { Title, Text } = Typography

const NotificationManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('templates')
  const [loading, setLoading] = useState(false)
  
  // 模板管理状态
  const [templates, setTemplates] = useState<NotificationTemplate[]>([])
  const [templateModalVisible, setTemplateModalVisible] = useState(false)
  const [templateForm] = Form.useForm()
  
  // 推送记录状态
  const [records, setRecords] = useState<NotificationRecord[]>([])
  const [recordsTotal, setRecordsTotal] = useState(0)
  
  // 用户订阅状态
  const [subscriptions, setSubscriptions] = useState<UserSubscription[]>([])
  
  // 统计数据状态
  const [stats, setStats] = useState<NotificationStats | null>(null)
  
  // 批量发送状态
  const [bulkSendModalVisible, setBulkSendModalVisible] = useState(false)
  const [bulkSendForm] = Form.useForm()

  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = async () => {
    setLoading(true)
    try {
      switch (activeTab) {
        case 'templates':
          await loadTemplates()
          break
        case 'records':
          await loadRecords()
          break
        case 'subscriptions':
          await loadSubscriptions()
          break
        case 'statistics':
          await loadStatistics()
          break
      }
    } finally {
      setLoading(false)
    }
  }

  const loadTemplates = async () => {
    const result = await notificationService.getNotificationTemplates()
    if (result.success && result.data) {
      setTemplates(result.data)
    }
  }

  const loadRecords = async () => {
    const result = await notificationService.getNotificationRecords()
    if (result.success && result.data) {
      setRecords(result.data.records)
      setRecordsTotal(result.data.total)
    }
  }

  const loadSubscriptions = async () => {
    const result = await notificationService.getUserSubscriptions()
    if (result.success && result.data) {
      setSubscriptions(result.data)
    }
  }

  const loadStatistics = async () => {
    const result = await notificationService.getNotificationStatistics()
    if (result.success && result.data) {
      setStats(result.data)
    }
  }

  // 模板管理相关方法
  const handleCreateTemplate = async (values: any) => {
    const result = await notificationService.createNotificationTemplate(values)
    if (result.success) {
      message.success('模板创建成功')
      setTemplateModalVisible(false)
      templateForm.resetFields()
      loadTemplates()
    } else {
      message.error(result.error || '创建失败')
    }
  }

  // 批量发送相关方法
  const handleBulkSend = async (values: any) => {
    const result = await notificationService.sendBulkNotifications({
      ...values,
      user_ids: values.user_ids.split(',').map((id: string) => id.trim())
    })
    if (result.success) {
      message.success(`发送完成：成功 ${result.data?.sent}，失败 ${result.data?.failed}`)
      setBulkSendModalVisible(false)
      bulkSendForm.resetFields()
    } else {
      message.error(result.error || '发送失败')
    }
  }

  // 模板表格列定义
  const templateColumns: ColumnsType<NotificationTemplate> = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: NotificationTemplate) => (
        <Space>
          <Text strong>{text}</Text>
          <Tag color={record.status === 'active' ? 'green' : 'red'}>
            {record.status === 'active' ? '启用' : '禁用'}
          </Tag>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          system: { color: 'blue', text: '系统' },
          marketing: { color: 'orange', text: '营销' },
          transaction: { color: 'green', text: '交易' },
          security: { color: 'red', text: '安全' }
        }
        const config = typeMap[type as keyof typeof typeMap]
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      render: (channel: string) => {
        const channelMap = {
          email: { icon: <MailOutlined />, text: '邮件' },
          sms: { icon: <MessageOutlined />, text: '短信' },
          push: { icon: <MobileOutlined />, text: '推送' },
          in_app: { icon: <BellOutlined />, text: '站内' }
        }
        const config = channelMap[channel as keyof typeof channelMap]
        return (
          <Space>
            {config.icon}
            {config.text}
          </Space>
        )
      }
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true
    },
    {
      title: '变量',
      dataIndex: 'variables',
      key: 'variables',
      render: (variables: string[]) => (
        <Space wrap>
          {variables.map(variable => (
            <Tag key={variable} color="purple">
              {`{{${variable}}}`}
            </Tag>
          ))}
        </Space>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: NotificationTemplate) => (
        <Space>
          <Tooltip title="查看">
            <Button type="text" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="编辑">
            <Button type="text" icon={<EditOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="删除">
            <Button type="text" danger icon={<DeleteOutlined />} size="small" />
          </Tooltip>
        </Space>
      )
    }
  ]

  // 推送记录表格列定义
  const recordColumns: ColumnsType<NotificationRecord> = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user: any) => (
        <Space>
          <UserOutlined />
          <div>
            <div>{user?.username || '未知用户'}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {user?.email}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true
    },
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      render: (channel: string) => {
        const channelMap = {
          email: { icon: <MailOutlined />, text: '邮件', color: 'blue' },
          sms: { icon: <MessageOutlined />, text: '短信', color: 'green' },
          push: { icon: <MobileOutlined />, text: '推送', color: 'orange' },
          in_app: { icon: <BellOutlined />, text: '站内', color: 'purple' }
        }
        const config = channelMap[channel as keyof typeof channelMap]
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        )
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { color: 'processing', text: '待发送' },
          sent: { color: 'default', text: '已发送' },
          delivered: { color: 'success', text: '已送达' },
          failed: { color: 'error', text: '发送失败' },
          read: { color: 'success', text: '已读' }
        }
        const config = statusMap[status as keyof typeof statusMap]
        return <Badge status={config.color as any} text={config.text} />
      }
    },
    {
      title: '发送时间',
      dataIndex: 'sent_at',
      key: 'sent_at',
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: NotificationRecord) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="text" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          {record.status === 'failed' && (
            <Tooltip title="重新发送">
              <Button type="text" icon={<SendOutlined />} size="small" />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  // 用户订阅表格列定义
  const subscriptionColumns: ColumnsType<UserSubscription> = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user: any) => (
        <Space>
          <UserOutlined />
          <div>
            <div>{user?.username || '未知用户'}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {user?.email}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '通知类型',
      dataIndex: 'notification_type',
      key: 'notification_type',
      render: (type: string) => {
        const typeMap = {
          system: { color: 'blue', text: '系统通知' },
          marketing: { color: 'orange', text: '营销通知' },
          transaction: { color: 'green', text: '交易通知' },
          security: { color: 'red', text: '安全通知' }
        }
        const config = typeMap[type as keyof typeof typeMap]
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      render: (channel: string) => {
        const channelMap = {
          email: { icon: <MailOutlined />, text: '邮件' },
          sms: { icon: <MessageOutlined />, text: '短信' },
          push: { icon: <MobileOutlined />, text: '推送' },
          in_app: { icon: <BellOutlined />, text: '站内' }
        }
        const config = channelMap[channel as keyof typeof channelMap]
        return (
          <Space>
            {config.icon}
            {config.text}
          </Space>
        )
      }
    },
    {
      title: '订阅状态',
      dataIndex: 'subscribed',
      key: 'subscribed',
      render: (subscribed: boolean, record: UserSubscription) => (
        <Switch
          checked={subscribed}
          checkedChildren="已订阅"
          unCheckedChildren="未订阅"
          onChange={(checked) => {
            // 这里可以调用API更新订阅状态
            message.success(`${checked ? '已订阅' : '已取消订阅'}`)
          }}
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString()
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <SettingOutlined />
                模板管理
              </Space>
            }
            key="templates"
          >
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setTemplateModalVisible(true)}
                >
                  创建模板
                </Button>
                <Button icon={<SendOutlined />} onClick={() => setBulkSendModalVisible(true)}>
                  批量发送
                </Button>
              </Space>
            </div>
            
            <Table
              columns={templateColumns}
              dataSource={templates}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <NotificationOutlined />
                推送记录
              </Space>
            }
            key="records"
          >
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <RangePicker placeholder={['开始日期', '结束日期']} />
                <Select placeholder="选择渠道" style={{ width: 120 }} allowClear>
                  <Option value="email">邮件</Option>
                  <Option value="sms">短信</Option>
                  <Option value="push">推送</Option>
                  <Option value="in_app">站内</Option>
                </Select>
                <Select placeholder="选择状态" style={{ width: 120 }} allowClear>
                  <Option value="pending">待发送</Option>
                  <Option value="sent">已发送</Option>
                  <Option value="delivered">已送达</Option>
                  <Option value="failed">发送失败</Option>
                  <Option value="read">已读</Option>
                </Select>
                <Button type="primary">查询</Button>
              </Space>
            </div>
            
            <Table
              columns={recordColumns}
              dataSource={records}
              rowKey="id"
              loading={loading}
              pagination={{
                total: recordsTotal,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <UserOutlined />
                用户订阅
              </Space>
            }
            key="subscriptions"
          >
            <Table
              columns={subscriptionColumns}
              dataSource={subscriptions}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <BarChartOutlined />
                统计分析
              </Space>
            }
            key="statistics"
          >
            {stats && (
              <div>
                <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="总发送量"
                        value={stats.totalSent}
                        prefix={<SendOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="送达率"
                        value={stats.deliveryRate}
                        suffix="%"
                        precision={1}
                        valueStyle={{ color: '#3f8600' }}
                      />
                      <Progress percent={stats.deliveryRate} showInfo={false} />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="阅读率"
                        value={stats.readRate}
                        suffix="%"
                        precision={1}
                        valueStyle={{ color: '#1890ff' }}
                      />
                      <Progress percent={stats.readRate} showInfo={false} />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="失败数量"
                        value={stats.totalFailed}
                        valueStyle={{ color: '#cf1322' }}
                      />
                    </Card>
                  </Col>
                </Row>

                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Card title="渠道统计" extra={<BarChartOutlined />}>
                      <div>
                        {Object.entries(stats.channelStats).map(([channel, data]) => {
                          const channelMap = {
                            email: '邮件',
                            sms: '短信',
                            push: '推送',
                            in_app: '站内'
                          }
                          return (
                            <div key={channel} style={{ marginBottom: '12px' }}>
                              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
                                <Text>{channelMap[channel as keyof typeof channelMap]}</Text>
                                <Text>{data.sent} / {data.delivered} / {data.failed}</Text>
                              </div>
                              <Progress
                                percent={(data.delivered / data.sent) * 100}
                                showInfo={false}
                                strokeColor="#52c41a"
                              />
                            </div>
                          )
                        })}
                      </div>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="类型统计" extra={<BarChartOutlined />}>
                      <div>
                        {Object.entries(stats.typeStats).map(([type, count]) => {
                          const typeMap = {
                            system: '系统通知',
                            marketing: '营销通知',
                            transaction: '交易通知',
                            security: '安全通知'
                          }
                          return (
                            <div key={type} style={{ marginBottom: '12px' }}>
                              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
                                <Text>{typeMap[type as keyof typeof typeMap]}</Text>
                                <Text strong>{count}</Text>
                              </div>
                              <Progress
                                percent={(count / stats.totalSent) * 100}
                                showInfo={false}
                                strokeColor="#1890ff"
                              />
                            </div>
                          )
                        })}
                      </div>
                    </Card>
                  </Col>
                </Row>
              </div>
            )}
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建模板弹窗 */}
      <Modal
        title="创建通知模板"
        open={templateModalVisible}
        onCancel={() => {
          setTemplateModalVisible(false)
          templateForm.resetFields()
        }}
        onOk={() => templateForm.submit()}
        width={600}
      >
        <Form
          form={templateForm}
          layout="vertical"
          onFinish={handleCreateTemplate}
        >
          <Form.Item
            name="name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="通知类型"
                rules={[{ required: true, message: '请选择通知类型' }]}
              >
                <Select placeholder="请选择通知类型">
                  <Option value="system">系统通知</Option>
                  <Option value="marketing">营销通知</Option>
                  <Option value="transaction">交易通知</Option>
                  <Option value="security">安全通知</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="channel"
                label="发送渠道"
                rules={[{ required: true, message: '请选择发送渠道' }]}
              >
                <Select placeholder="请选择发送渠道">
                  <Option value="email">邮件</Option>
                  <Option value="sms">短信</Option>
                  <Option value="push">推送</Option>
                  <Option value="in_app">站内信</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="title"
            label="消息标题"
            rules={[{ required: true, message: '请输入消息标题' }]}
          >
            <Input placeholder="支持变量，如：{{username}}" />
          </Form.Item>
          
          <Form.Item
            name="content"
            label="消息内容"
            rules={[{ required: true, message: '请输入消息内容' }]}
          >
            <RichTextEditor placeholder="支持变量，如：{{username}}、{{amount}}" />
          </Form.Item>
          
          <Form.Item
            name="variables"
            label="模板变量"
            help="多个变量用逗号分隔，如：username,amount"
          >
            <Input placeholder="username,amount" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量发送弹窗 */}
      <Modal
        title="批量发送通知"
        open={bulkSendModalVisible}
        onCancel={() => {
          setBulkSendModalVisible(false)
          bulkSendForm.resetFields()
        }}
        onOk={() => bulkSendForm.submit()}
        width={600}
      >
        <Form
          form={bulkSendForm}
          layout="vertical"
          onFinish={handleBulkSend}
        >
          <Form.Item
            name="user_ids"
            label="目标用户ID"
            rules={[{ required: true, message: '请输入用户ID' }]}
            help="多个用户ID用逗号分隔"
          >
            <TextArea rows={3} placeholder="user1,user2,user3" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="通知类型"
                rules={[{ required: true, message: '请选择通知类型' }]}
              >
                <Select placeholder="请选择通知类型">
                  <Option value="system">系统通知</Option>
                  <Option value="marketing">营销通知</Option>
                  <Option value="transaction">交易通知</Option>
                  <Option value="security">安全通知</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="channel"
                label="发送渠道"
                rules={[{ required: true, message: '请选择发送渠道' }]}
              >
                <Select placeholder="请选择发送渠道">
                  <Option value="email">邮件</Option>
                  <Option value="sms">短信</Option>
                  <Option value="push">推送</Option>
                  <Option value="in_app">站内信</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="title"
            label="消息标题"
            rules={[{ required: true, message: '请输入消息标题' }]}
          >
            <Input placeholder="请输入消息标题" />
          </Form.Item>
          
          <Form.Item
            name="content"
            label="消息内容"
            rules={[{ required: true, message: '请输入消息内容' }]}
          >
            <TextArea rows={4} placeholder="请输入消息内容" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default NotificationManagement