import React, { useState, useContext, useEffect, useMemo } from 'react';
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Table, 
  Button, 
  Spin,
  Alert,
  Progress,
  Tag
} from 'antd';
import { 
  UserOutlined, 
  FileTextOutlined, 
  CheckSquareOutlined, 
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  ReloadOutlined,
  CrownOutlined,
  TeamOutlined,
  WarningOutlined
} from '@ant-design/icons';
import type { TableProps } from 'antd';
import SearchFilter from '../components/SearchFilter';
import QuickActions from '../components/QuickActions';
import { GlobalMessageContext } from '../components/GlobalMessage';
import { statisticsService } from '../services/statisticsService';
import type { DashboardStats, UserStats, ContentStats } from '../services/statisticsService';
import { useUserStore } from '../store/userStore';
import EChartsComponent from '../components/EChartsComponent';
import { createLineChartOption } from '../utils/chartOptions';

const { Title } = Typography

// 统计数据接口
interface StatCardData {
  id: number;
  name: string;
  value: number;
  icon: React.ReactNode;
  change: number;
}

// 用户数据接口
interface UserData {
  id: string;
  username: string;
  email: string;
  created_at: string;
  level: string;
}

const userColumns: TableProps<UserData>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 100,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
  },
  {
    title: '注册日期',
    dataIndex: 'created_at',
    key: 'created_at',
    render: (date: string) => new Date(date).toLocaleDateString('zh-CN'),
  },
  {
    title: '等级',
    dataIndex: 'level',
    key: 'level',
  },
]

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null)
  const [userStats, setUserStats] = useState<UserStats | null>(null)
  const [contentStats, setContentStats] = useState<ContentStats | null>(null)
  const [recentUsers, setRecentUsers] = useState<UserData[]>([])
  const { message } = useContext(GlobalMessageContext)
  const { isAdmin } = useUserStore()

  // 用户增长数据状态
  const [userGrowthData, setUserGrowthData] = useState<{ date: string; count: number }[]>([]);
  
  // 用户增长趋势图表数据
  const userGrowthChartOption = useMemo(() => {
    if (!userGrowthData.length) return null
    
    const dates = userGrowthData.map(item => {
      const date = new Date(item.date)
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    })
    
    const userData = {
      xAxisData: dates,
      series: [
        {
          name: '新增用户',
          data: userGrowthData.map(item => item.count),
          color: '#1890ff'
        }
      ]
    }
    
    return createLineChartOption(userData)
  }, [userGrowthData])

  // 生成统计卡片数据
  const generateStatsData = (stats: DashboardStats): StatCardData[] => [
    { id: 1, name: '总用户数', value: stats.totalUsers, icon: <UserOutlined />, change: stats.userGrowth },
    { id: 2, name: '总内容数', value: stats.totalPosts, icon: <FileTextOutlined />, change: stats.postGrowth },
    { id: 3, name: '总打卡数', value: stats.totalCheckins, icon: <CheckSquareOutlined />, change: stats.checkinGrowth },
    { id: 4, name: '总成就数', value: stats.totalAchievements, icon: <TrophyOutlined />, change: stats.achievementGrowth },
  ]

  // 加载仪表盘数据
  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // 基础数据
      const [statsResult, usersResult, growthResult] = await Promise.all([
        statisticsService.getDashboardStats(),
        statisticsService.getRecentUsers(5),
        statisticsService.getUserGrowthData()
      ])
      
      if (statsResult.success && statsResult.data) {
        setDashboardStats(statsResult.data)
      }
      
      if (usersResult.success && usersResult.data) {
        setRecentUsers(usersResult.data)
      }
      
      if (growthResult.success && growthResult.data) {
        setUserGrowthData(growthResult.data)
      }
      
      // 管理员专用数据
      let userStatsResult, contentStatsResult
      if (isAdmin()) {
        [userStatsResult, contentStatsResult] = await Promise.all([
          statisticsService.getUserStats(),
          statisticsService.getContentStats()
        ])
      }
      
      if (statsResult.success && statsResult.data) {
        setDashboardStats(statsResult.data)
      } else {
        throw new Error(statsResult.error || '获取统计数据失败')
      }
      
      if (usersResult.success && usersResult.data) {
        setRecentUsers(usersResult.data as UserData[])
      } else {
        throw new Error(usersResult.error || '获取用户数据失败')
      }
      
      if (growthResult.success && growthResult.data) {
        setUserGrowthData(growthResult.data)
      }
      
      // 管理员数据
      if (isAdmin() && userStatsResult && userStatsResult.success && userStatsResult.data) {
        setUserStats(userStatsResult.data)
      }
      
      if (isAdmin() && contentStatsResult && contentStatsResult.success && contentStatsResult.data) {
        setContentStats(contentStatsResult.data)
      }
      
      message.success('数据加载成功')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '数据加载失败'
      setError(errorMessage)
      message.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // 组件加载时获取数据
  useEffect(() => {
    loadDashboardData()
  }, [])
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    loadDashboardData()
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>仪表盘</Title>
      
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <QuickActions 
            onRefresh={handleRefresh}
            showRefresh
            refreshLoading={loading}
          />
        </Col>
      </Row>
      
      <SearchFilter 
        onSearch={handleSearch}
        onRefresh={handleRefresh}
        placeholder="搜索..."
        loading={loading}
      />
      
      {error && (
        <Alert 
          message="错误" 
          description={error} 
          type="error" 
          showIcon 
          style={{ marginTop: 16 }}
        />
      )}
      
      <Spin spinning={loading}>
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          {dashboardStats && generateStatsData(dashboardStats).map((stat: StatCardData) => (
            <Col xs={24} sm={12} md={6} lg={6} xl={6} key={stat.id}>
              <Card style={{ height: '100%' }}>
                <Statistic 
                  title={stat.name} 
                  value={stat.value} 
                  prefix={stat.icon}
                  suffix={
                    <span style={{ fontSize: '14px', color: stat.change >= 0 ? '#52c41a' : '#ff4d4f' }}>
                      {stat.change >= 0 ? <RiseOutlined /> : <FallOutlined />} {Math.abs(stat.change)}%
                    </span>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
        
        {/* 管理员专用统计 */}
        {isAdmin() && userStats && (
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24} sm={12} md={8} lg={8} xl={8}>
              <Card title="用户统计" style={{ height: '100%' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Statistic title="活跃用户" value={userStats.activeUsers} prefix={<TeamOutlined />} />
                  <Statistic title="VIP用户" value={userStats.premiumUsers} prefix={<CrownOutlined />} />
                  <Statistic title="封禁用户" value={userStats.bannedUsers} prefix={<WarningOutlined />} />
                </Space>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={8} xl={8}>
              <Card title="新用户增长" style={{ height: '100%' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <span>今日新增: </span>
                    <Tag color="green">{userStats.newUsersToday}</Tag>
                  </div>
                  <div>
                    <span>本周新增: </span>
                    <Tag color="blue">{userStats.newUsersThisWeek}</Tag>
                  </div>
                  <div>
                    <span>本月新增: </span>
                    <Tag color="purple">{userStats.newUsersThisMonth}</Tag>
                  </div>
                </Space>
              </Card>
            </Col>
            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <Card title="用户活跃度" style={{ height: '100%' }}>
                <Progress 
                  type="circle" 
                  percent={Math.round((userStats.activeUsers / userStats.totalUsers) * 100)} 
                  format={percent => `${percent}%`}
                  strokeColor="#52c41a"
                />
                <div style={{ textAlign: 'center', marginTop: 8 }}>
                  活跃用户占比
                </div>
              </Card>
            </Col>
          </Row>
        )}
        
        {/* 管理员专用内容统计 */}
        {isAdmin() && contentStats && (
          <Card title="内容管理统计" style={{ marginTop: 16 }}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Statistic title="总帖子数" value={contentStats.totalPosts} prefix={<FileTextOutlined />} />
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Statistic title="已发布" value={contentStats.publishedPosts} prefix={<CheckSquareOutlined />} />
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Statistic title="待审核" value={contentStats.pendingPosts} prefix={<WarningOutlined />} />
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Statistic title="草稿" value={contentStats.draftPosts} prefix={<FileTextOutlined />} />
              </Col>
            </Row>
            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Statistic title="总评论数" value={contentStats.totalComments} />
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Statistic title="已审核评论" value={contentStats.approvedComments} />
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Statistic title="待审核评论" value={contentStats.pendingComments} />
              </Col>
            </Row>
          </Card>
        )}
        
        <Card 
          title="最新用户" 
          style={{ marginTop: 16 }}
          extra={<a href="#">查看更多</a>}
        >
          <Table 
            columns={userColumns} 
            dataSource={recentUsers} 
            pagination={false}
            scroll={{ x: 800 }}
            rowKey="id"
          />
        </Card>
        
        <Card 
          title="用户增长趋势" 
          style={{ marginTop: 16 }}
          extra={<a href="#">查看更多</a>}
        >
          {userGrowthChartOption ? (
            <EChartsComponent 
              option={userGrowthChartOption}
              height={300}
              loading={loading}
            />
          ) : (
            <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <span>暂无数据</span>
            </div>
          )}
        </Card>
      </Spin>
    </Space>
  )
}

export default Dashboard