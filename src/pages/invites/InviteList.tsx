import React, { useState, useContext, useEffect } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Modal,
  Form,
  DatePicker,
  Switch,
  Dropdown,
  Menu,
  Divider,
  Tabs,
  Statistic,
  Popconfirm,
  Spin,
  Alert,
  message
} from 'antd'
import type { TableProps } from 'antd';
import { 
  UserOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  CopyOutlined,
  UsergroupAddOutlined,
  Bar<PERSON>hartOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'
import { inviteService } from '../../services/inviteService'
import type { Invite, InviteRecord } from '../../services/inviteService'

const { Title } = Typography
const { Option } = Select
const { RangePicker } = DatePicker
const { TabPane } = Tabs

const InviteList: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('invites')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()
  
  // 添加真实数据状态
  const [invites, setInvites] = useState<Invite[]>([])
  const [inviteRecords, setInviteRecords] = useState<InviteRecord[]>([])
  
  const { message: globalMessage } = useContext(GlobalMessageContext)
  
  // 页面加载时获取数据
  useEffect(() => {
    loadInvites()
    loadInviteRecords()
  }, [])
  
  const loadInvites = async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await inviteService.getAllInvites()
      if (result.success && result.data) {
        setInvites(result.data)
      } else {
        throw new Error(result.error?.message || '获取邀请码列表失败')
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取邀请码列表失败'
      setError(errorMsg)
      globalMessage.error(errorMsg)
    } finally {
      setLoading(false)
    }
  }
  
  const loadInviteRecords = async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await inviteService.getAllInviteRecords()
      if (result.success && result.data) {
        setInviteRecords(result.data)
      } else {
        throw new Error(result.error?.message || '获取邀请记录失败')
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取邀请记录失败'
      setError(errorMsg)
      globalMessage.error(errorMsg)
    } finally {
      setLoading(false)
    }
  }

  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)
      
      // 生成邀请码
      const inviteCode = inviteService.generateInviteCode()
      
      const inviteData = {
        code: inviteCode,
        creator: values.creator,
        max_uses: parseInt(values.maxUses),
        expires_at: values.expiresAt.format('YYYY-MM-DD'),
        status: values.isActive ? 'active' : 'disabled' as 'active' | 'disabled',
        current_uses: 0
      }
      
      const result = await inviteService.createInvite(inviteData)
      
      if (result.success) {
        globalMessage.success('邀请码创建成功')
        setIsModalVisible(false)
        form.resetFields()
        await loadInvites() // 重新加载数据
      } else {
        throw new Error(result.error?.message || '创建邀请码失败')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '创建邀请码失败'
      globalMessage.error(errorMsg)
    } finally {
      setLoading(false)
    }
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = async () => {
    if (activeTab === 'invites') {
      await loadInvites()
    } else {
      await loadInviteRecords()
    }
  }
  
  const handleAddInvite = () => {
    showModal()
  }

  // 定义邀请码表格列
  const inviteColumnsDefinition: TableProps<Invite>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '邀请码',
      dataIndex: 'code',
      key: 'code',
      render: (code: string) => (
        <Button type="link" icon={<CopyOutlined />} onClick={() => navigator.clipboard.writeText(code)}>
          {code}
        </Button>
      )
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
    },
    {
      title: '使用者',
      dataIndex: 'used_by',
      key: 'used_by',
      render: (used_by: string) => used_by || '-'
    },
    {
      title: '使用次数',
      key: 'usage',
      render: (_, record) => `${record.current_uses}/${record.max_uses}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap: Record<string, { text: string, color: 'success' | 'processing' | 'default' | 'error' }> = {
          'active': { text: '有效', color: 'processing' },
          'used': { text: '已用完', color: 'success' },
          'expired': { text: '已过期', color: 'default' },
          'disabled': { text: '已禁用', color: 'error' }
        }
        
        const stat = statusMap[status] || { text: status, color: 'default' }
        return <Tag color={stat.color}>{stat.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '过期时间',
      dataIndex: 'expires_at',
      key: 'expires_at',
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<EyeOutlined />} size="small">查看</Button>
          <Button type="link" icon={<EditOutlined />} size="small">编辑</Button>
          <Dropdown 
            overlay={
              <Menu>
                <Menu.Item key="1" icon={<EyeOutlined />}>查看详情</Menu.Item>
                <Menu.Item key="2" icon={<EditOutlined />}>编辑邀请码</Menu.Item>
                <Menu.Item key="3" icon={<CopyOutlined />}>复制邀请码</Menu.Item>
                {record.status === 'active' ? (
                  <Menu.Item key="4" icon={<DeleteOutlined />}>禁用邀请码</Menu.Item>
                ) : (
                  <Menu.Item key="5" icon={<EyeOutlined />}>启用邀请码</Menu.Item>
                )}
                <Menu.Divider />
                <Menu.Item 
                  key="6" 
                  danger 
                  icon={<DeleteOutlined />} 
                  onClick={async () => {
                    try {
                      setLoading(true)
                      const result = await inviteService.deleteInvite(record.id)
                      if (result.success) {
                        globalMessage.success(`邀请码 "${record.code}" 删除成功`)
                        await loadInvites() // 重新加载数据
                      } else {
                        throw new Error('删除失败')
                      }
                    } catch (error) {
                      const errorMsg = error instanceof Error ? error.message : '删除失败'
                      globalMessage.error(errorMsg)
                    } finally {
                      setLoading(false)
                    }
                  }}
                >
                  删除邀请码
                </Menu.Item>
              </Menu>
            }
          >
            <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
          </Dropdown>
        </Space>
      ),
    },
  ]

  // 定义邀请记录表格列
  const inviteRecordColumnsDefinition: TableProps<InviteRecord>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '邀请码',
      dataIndex: 'invite_code',
      key: 'invite_code',
    },
    {
      title: '邀请人',
      dataIndex: 'inviter',
      key: 'inviter',
    },
    {
      title: '被邀请人',
      dataIndex: 'invitee',
      key: 'invitee',
      render: (invitee: string) => invitee || '未注册'
    },
    {
      title: '邀请时间',
      dataIndex: 'invited_at',
      key: 'invited_at',
    },
    {
      title: '注册时间',
      dataIndex: 'registered_at',
      key: 'registered_at',
      render: (registered_at: string) => registered_at || '未注册'
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<EyeOutlined />} size="small">查看</Button>
        </Space>
      ),
    },
  ]

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>邀请码管理</Title>
      
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<Space><UsergroupAddOutlined />邀请码管理</Space>} key="invites">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  onAdd={handleAddInvite}
                  showRefresh
                  showAdd
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索邀请码/创建者"
              filters={[
                {
                  key: 'status',
                  label: '邀请码状态',
                  type: 'select',
                  options: [
                    { value: 'active', label: '有效' },
                    { value: 'used', label: '已用完' },
                    { value: 'expired', label: '已过期' },
                    { value: 'disabled', label: '已禁用' }
                  ]
                },
                {
                  key: 'dateRange',
                  label: '日期范围',
                  type: 'dateRange'
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={inviteColumnsDefinition} 
                dataSource={invites} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  defaultPageSize: 20
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
          
          <TabPane tab={<Space><UsergroupAddOutlined />邀请记录</Space>} key="records">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索邀请人/被邀请人"
              filters={[
                {
                  key: 'status',
                  label: '注册状态',
                  type: 'select',
                  options: [
                    { value: 'registered', label: '已注册' },
                    { value: 'pending', label: '未注册' }
                  ]
                },
                {
                  key: 'dateRange',
                  label: '日期范围',
                  type: 'dateRange'
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={inviteRecordColumnsDefinition} 
                dataSource={inviteRecords} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  defaultPageSize: 20
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
          
          <TabPane tab={<Space><BarChartOutlined />统计分析</Space>} key="statistics">
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="总邀请码数" value={24} prefix={<UsergroupAddOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="已使用邀请码" value={18} prefix={<UsergroupAddOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="总邀请人数" value={42} prefix={<UserOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="注册转化率" value="78%" prefix={<BarChartOutlined />} />
                </Card>
              </Col>
            </Row>
            
            <Card title="邀请趋势">
              <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <img src="https://placehold.co/600x300" alt="邀请趋势图表" />
              </div>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
      
      <Modal
        title="生成邀请码"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="creator" 
            label="创建者" 
            rules={[{ required: true, message: '请输入创建者' }]}
          >
            <Input placeholder="请输入创建者" />
          </Form.Item>
          <Form.Item 
            name="maxUses" 
            label="最大使用次数" 
            rules={[{ required: true, message: '请输入最大使用次数' }]}
          >
            <Input type="number" placeholder="请输入最大使用次数" />
          </Form.Item>
          <Form.Item 
            name="expiresAt" 
            label="过期时间" 
            rules={[{ required: true, message: '请选择过期时间' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="isActive" label="是否激活" valuePropName="checked">
            <Switch defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default InviteList