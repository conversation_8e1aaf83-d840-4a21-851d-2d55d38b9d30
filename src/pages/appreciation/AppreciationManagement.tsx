import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Statistic,
  Row,
  Col,
  message,
  Tabs,
  InputNumber,
  Switch,
  Avatar,
  Tooltip,
  Popconfirm
} from 'antd'
import {
  DollarOutlined,
  GiftOutlined,
  TrophyOutlined,
  SettingOutlined,
  ReloadOutlined,
  SearchOutlined,
  ExportOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import EChartsComponent from '../../components/EChartsComponent'
import RichTextEditor from '../../components/RichTextEditor'
import { appreciationService } from '../../services/appreciationService'
import type { AppreciationRecord, AppreciationStats, AppreciationSettings } from '../../services/appreciationService'
import { createLineChartOption, createPieChartOption, createBarChartOption } from '../../utils/chartOptions'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { TabPane } = Tabs
const { Option } = Select

const AppreciationManagement: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [records, setRecords] = useState<AppreciationRecord[]>([])
  const [stats, setStats] = useState<AppreciationStats | null>(null)
  const [settings, setSettings] = useState<AppreciationSettings | null>(null)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [filters, setFilters] = useState<any>({})
  const [settingsModalVisible, setSettingsModalVisible] = useState(false)
  const [refundModalVisible, setRefundModalVisible] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<AppreciationRecord | null>(null)
  const [activeTab, setActiveTab] = useState('records')

  const [form] = Form.useForm()
  const [settingsForm] = Form.useForm()
  const [refundForm] = Form.useForm()

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      if (activeTab === 'records') {
        const response = await appreciationService.getAppreciationRecords({
          page: currentPage,
          pageSize,
          ...filters
        })
        setRecords(response.data)
        setTotal(response.total)
      } else if (activeTab === 'statistics') {
        const statsData = await appreciationService.getAppreciationStats()
        setStats(statsData)
      } else if (activeTab === 'settings') {
        const settingsData = await appreciationService.getAppreciationSettings()
        setSettings(settingsData)
        if (settingsData) {
          settingsForm.setFieldsValue(settingsData)
        }
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [currentPage, pageSize, filters, activeTab])

  // 搜索处理
  const handleSearch = (values: any) => {
    const newFilters = { ...values }
    if (values.dateRange) {
      newFilters.start_date = values.dateRange[0].format('YYYY-MM-DD')
      newFilters.end_date = values.dateRange[1].format('YYYY-MM-DD')
      delete newFilters.dateRange
    }
    setFilters(newFilters)
    setCurrentPage(1)
  }

  // 重置搜索
  const handleReset = () => {
    form.resetFields()
    setFilters({})
    setCurrentPage(1)
  }

  // 更新记录状态
  const handleUpdateStatus = async (id: string, status: string) => {
    try {
      await appreciationService.updateAppreciationStatus(id, status)
      message.success('状态更新成功')
      loadData()
    } catch (error) {
      console.error('更新状态失败:', error)
      message.error('更新状态失败')
    }
  }

  // 处理退款
  const handleRefund = async (values: any) => {
    if (!selectedRecord) return
    
    try {
      await appreciationService.refundAppreciation(selectedRecord.id, values.reason)
      message.success('退款处理成功')
      setRefundModalVisible(false)
      setSelectedRecord(null)
      refundForm.resetFields()
      loadData()
    } catch (error) {
      console.error('退款处理失败:', error)
      message.error('退款处理失败')
    }
  }

  // 保存设置
  const handleSaveSettings = async (values: any) => {
    try {
      await appreciationService.updateAppreciationSettings(values)
      message.success('设置保存成功')
      setSettingsModalVisible(false)
      loadData()
    } catch (error) {
      console.error('保存设置失败:', error)
      message.error('保存设置失败')
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '赞赏者',
      dataIndex: 'from_user',
      key: 'from_user',
      render: (user: any) => (
        <Space>
          <Avatar src={user?.avatar} size="small">
            {user?.username?.[0]}
          </Avatar>
          <span>{user?.username || '未知用户'}</span>
        </Space>
      )
    },
    {
      title: '接收者',
      dataIndex: 'to_user',
      key: 'to_user',
      render: (user: any) => (
        <Space>
          <Avatar src={user?.avatar} size="small">
            {user?.username?.[0]}
          </Avatar>
          <span>{user?.username || '未知用户'}</span>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'target_type',
      key: 'target_type',
      render: (type: string) => {
        const typeMap = {
          article: { text: '文章', color: 'blue' },
          comment: { text: '评论', color: 'green' },
          user: { text: '用户', color: 'purple' }
        }
        const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#f50', fontWeight: 'bold' }}>¥{amount.toFixed(2)}</span>
      )
    },
    {
      title: '支付方式',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: (method: string) => {
        const methodMap = {
          wechat: { text: '微信支付', color: 'green' },
          alipay: { text: '支付宝', color: 'blue' },
          balance: { text: '余额支付', color: 'orange' }
        }
        const config = methodMap[method as keyof typeof methodMap] || { text: method, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '待处理', color: 'orange' },
          completed: { text: '已完成', color: 'green' },
          failed: { text: '失败', color: 'red' },
          refunded: { text: '已退款', color: 'gray' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '留言',
      dataIndex: 'message',
      key: 'message',
      render: (message: string) => (
        message ? (
          <Tooltip title={message}>
            <span>{message.length > 20 ? `${message.slice(0, 20)}...` : message}</span>
          </Tooltip>
        ) : '-'
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: AppreciationRecord) => (
        <Space>
          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              onClick={() => handleUpdateStatus(record.id, 'completed')}
            >
              确认完成
            </Button>
          )}
          {record.status === 'completed' && (
            <Button
              type="link"
              size="small"
              danger
              onClick={() => {
                setSelectedRecord(record)
                setRefundModalVisible(true)
              }}
            >
              退款
            </Button>
          )}
        </Space>
      )
    }
  ]

  // 统计卡片数据
  const getStatsCards = () => {
    if (!stats) return []
    
    return [
      {
        title: '总赞赏金额',
        value: stats.total_amount,
        prefix: '¥',
        precision: 2,
        icon: <DollarOutlined style={{ color: '#f50' }} />
      },
      {
        title: '总赞赏次数',
        value: stats.total_count,
        icon: <GiftOutlined style={{ color: '#1890ff' }} />
      },
      {
        title: '今日赞赏',
        value: stats.today_amount,
        prefix: '¥',
        precision: 2,
        icon: <TrophyOutlined style={{ color: '#52c41a' }} />
      },
      {
        title: '平均金额',
        value: stats.avg_amount,
        prefix: '¥',
        precision: 2,
        icon: <DollarOutlined style={{ color: '#722ed1' }} />
      }
    ]
  }

  // 图表配置
  const getTrendsChartOption = () => {
    if (!stats) return {}
    
    // 这里应该从服务获取趋势数据，暂时用模拟数据
    const dates = Array.from({ length: 7 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - 6 + i)
      return date.toISOString().split('T')[0]
    })
    
    const amounts = dates.map(() => Math.floor(Math.random() * 1000) + 100)
    
    return createLineChartOption(
      '赞赏趋势',
      dates,
      [{ name: '赞赏金额', data: amounts }]
    )
  }

  const getPaymentMethodChartOption = () => {
    if (!stats?.payment_method_stats) return {}
    
    const data = stats.payment_method_stats.map(item => ({
      name: item.method === 'wechat' ? '微信支付' : item.method === 'alipay' ? '支付宝' : '余额支付',
      value: item.amount
    }))
    
    return createPieChartOption('支付方式分布', data)
  }

  const getTopReceiversChartOption = () => {
    if (!stats?.top_receivers) return {}
    
    const names = stats.top_receivers.slice(0, 10).map(item => item.username)
    const amounts = stats.top_receivers.slice(0, 10).map(item => item.total_amount)
    
    return createBarChartOption(
      '收赞排行榜',
      names,
      [{ name: '收赞金额', data: amounts }]
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card title="赞赏管理" extra={
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadData}>
            刷新
          </Button>
          <Button icon={<ExportOutlined />}>
            导出
          </Button>
        </Space>
      }>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="赞赏记录" key="records">
            {/* 搜索表单 */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Form
                form={form}
                layout="inline"
                onFinish={handleSearch}
              >
                <Form.Item name="status" label="状态">
                  <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
                    <Option value="pending">待处理</Option>
                    <Option value="completed">已完成</Option>
                    <Option value="failed">失败</Option>
                    <Option value="refunded">已退款</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="target_type" label="类型">
                  <Select placeholder="选择类型" allowClear style={{ width: 120 }}>
                    <Option value="article">文章</Option>
                    <Option value="comment">评论</Option>
                    <Option value="user">用户</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="dateRange" label="时间范围">
                  <RangePicker />
                </Form.Item>
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      搜索
                    </Button>
                    <Button onClick={handleReset}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>

            {/* 数据表格 */}
            <Table
              columns={columns}
              dataSource={records}
              rowKey="id"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize,
                total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: (page, size) => {
                  setCurrentPage(page)
                  setPageSize(size || 20)
                }
              }}
            />
          </TabPane>

          <TabPane tab="统计分析" key="statistics">
            {stats && (
              <>
                {/* 统计卡片 */}
                <Row gutter={16} style={{ marginBottom: 24 }}>
                  {getStatsCards().map((card, index) => (
                    <Col span={6} key={index}>
                      <Card>
                        <Statistic
                          title={card.title}
                          value={card.value}
                          prefix={card.prefix}
                          precision={card.precision}
                          suffix={card.icon}
                        />
                      </Card>
                    </Col>
                  ))}
                </Row>

                {/* 图表 */}
                <Row gutter={16} style={{ marginBottom: 24 }}>
                  <Col span={12}>
                    <Card title="赞赏趋势">
                      <EChartsComponent
                        option={getTrendsChartOption()}
                        height={300}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="支付方式分布">
                      <EChartsComponent
                        option={getPaymentMethodChartOption()}
                        height={300}
                      />
                    </Card>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={24}>
                    <Card title="收赞排行榜">
                      <EChartsComponent
                        option={getTopReceiversChartOption()}
                        height={400}
                      />
                    </Card>
                  </Col>
                </Row>
              </>
            )}
          </TabPane>

          <TabPane tab="赞赏设置" key="settings">
            <Card
              title="赞赏设置"
              extra={
                <Button
                  type="primary"
                  icon={<SettingOutlined />}
                  onClick={() => setSettingsModalVisible(true)}
                >
                  编辑设置
                </Button>
              }
            >
              {settings ? (
                <Row gutter={16}>
                  <Col span={12}>
                    <Card size="small" title="金额设置">
                      <p>最小金额: ¥{settings.min_amount}</p>
                      <p>最大金额: ¥{settings.max_amount}</p>
                      <p>默认金额: {settings.default_amounts.map(amount => `¥${amount}`).join(', ')}</p>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card size="small" title="其他设置">
                      <p>佣金比例: {(settings.commission_rate * 100).toFixed(1)}%</p>
                      <p>自动提现阈值: ¥{settings.auto_withdraw_threshold}</p>
                      <p>允许匿名赞赏: {settings.enable_anonymous ? '是' : '否'}</p>
                      <p>允许留言: {settings.enable_message ? '是' : '否'}</p>
                    </Card>
                  </Col>
                </Row>
              ) : (
                <p>暂无设置数据</p>
              )}
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* 设置编辑模态框 */}
      <Modal
        title="编辑赞赏设置"
        open={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={settingsForm}
          layout="vertical"
          onFinish={handleSaveSettings}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="min_amount"
                label="最小金额"
                rules={[{ required: true, message: '请输入最小金额' }]}
              >
                <InputNumber
                  min={0.01}
                  step={0.01}
                  precision={2}
                  addonBefore="¥"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="max_amount"
                label="最大金额"
                rules={[{ required: true, message: '请输入最大金额' }]}
              >
                <InputNumber
                  min={0.01}
                  step={0.01}
                  precision={2}
                  addonBefore="¥"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="default_amounts"
            label="默认金额选项"
            rules={[{ required: true, message: '请输入默认金额选项' }]}
          >
            <Select
              mode="tags"
              placeholder="输入金额后按回车添加"
              tokenSeparators={[',']}
            />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="commission_rate"
                label="佣金比例"
                rules={[{ required: true, message: '请输入佣金比例' }]}
              >
                <InputNumber
                  min={0}
                  max={1}
                  step={0.01}
                  precision={2}
                  formatter={value => `${(Number(value) * 100).toFixed(1)}%`}
                  parser={(value: any) => Number(value!.replace('%', '')) / 100}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="auto_withdraw_threshold"
                label="自动提现阈值"
                rules={[{ required: true, message: '请输入自动提现阈值' }]}
              >
                <InputNumber
                  min={0}
                  step={1}
                  precision={2}
                  addonBefore="¥"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enable_anonymous"
                label="允许匿名赞赏"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="enable_message"
                label="允许留言"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存设置
              </Button>
              <Button onClick={() => setSettingsModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 退款模态框 */}
      <Modal
        title="处理退款"
        open={refundModalVisible}
        onCancel={() => {
          setRefundModalVisible(false)
          setSelectedRecord(null)
          refundForm.resetFields()
        }}
        footer={null}
      >
        <Form
          form={refundForm}
          layout="vertical"
          onFinish={handleRefund}
        >
          <Form.Item
            name="reason"
            label="退款原因"
            rules={[{ required: true, message: '请输入退款原因' }]}
          >
            <RichTextEditor
              placeholder="请输入退款原因"
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" danger>
                确认退款
              </Button>
              <Button onClick={() => {
                setRefundModalVisible(false)
                setSelectedRecord(null)
                refundForm.resetFields()
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default AppreciationManagement