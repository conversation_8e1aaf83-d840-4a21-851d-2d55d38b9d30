import React, { useState, useContext } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  DatePicker,
  Spin,
  Alert
} from 'antd'
import { 
  SearchOutlined,
  UserOutlined,
  FileTextOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'

const { Title } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface LogDataType {
  id: number
  admin: string
  action: string
  resource: string
  ip: string
  createdAt: string
  status: 'success' | 'failed'
}

const logData: LogDataType[] = [
  { 
    id: 1, 
    admin: 'admin', 
    action: '创建用户', 
    resource: '用户:test09',
    ip: '*************',
    createdAt: '2023-06-10 14:30:22',
    status: 'success'
  },
  { 
    id: 2, 
    admin: 'moderator', 
    action: '删除内容', 
    resource: '帖子:如何保持积极心态',
    ip: '*************',
    createdAt: '2023-06-10 11:45:10',
    status: 'success'
  },
  { 
    id: 3, 
    admin: 'operator1', 
    action: '编辑成就', 
    resource: '成就:坚持不懈',
    ip: '*************',
    createdAt: '2023-06-10 09:20:35',
    status: 'success'
  },
  { 
    id: 4, 
    admin: 'admin', 
    action: '删除用户', 
    resource: '用户:spam_user',
    ip: '*************',
    createdAt: '2023-06-09 16:40:12',
    status: 'success'
  },
  { 
    id: 5, 
    admin: 'moderator', 
    action: '审核内容', 
    resource: '帖子:健康生活方式分享',
    ip: '*************',
    createdAt: '2023-06-09 14:15:55',
    status: 'failed'
  },
]

const logColumns: TableProps<LogDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '管理员',
    dataIndex: 'admin',
    key: 'admin',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
  {
    title: '资源',
    dataIndex: 'resource',
    key: 'resource',
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => {
      const statusMap: Record<string, { text: string, color: 'success' | 'error' }> = {
        'success': { text: '成功', color: 'success' },
        'failed': { text: '失败', color: 'error' }
      }
      
      const stat = statusMap[status] || { text: status, color: 'default' }
      return <Tag color={stat.color}>{stat.text}</Tag>
    }
  },
  {
    title: '操作时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
  },
]

const OperationLog: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { message } = useContext(GlobalMessageContext)
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    setLoading(true)
    setError(null)
    // 模拟API调用
    setTimeout(() => {
      // 模拟随机错误
      if (Math.random() > 0.8) {
        setError('数据加载失败，请稍后重试')
        message.error('数据加载失败，请稍后重试')
      } else {
        message.success('数据刷新成功')
      }
      setLoading(false)
    }, 1000)
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>操作日志</Title>
      
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <QuickActions 
              onRefresh={handleRefresh}
              showRefresh
              refreshLoading={loading}
            />
          </Col>
        </Row>
        
        <SearchFilter 
          onSearch={handleSearch}
          onRefresh={handleRefresh}
          placeholder="搜索管理员/操作/资源"
          filters={[
            {
              key: 'action',
              label: '操作类型',
              type: 'select',
              options: [
                { value: 'create', label: '创建' },
                { value: 'update', label: '编辑' },
                { value: 'delete', label: '删除' },
                { value: 'approve', label: '审核' }
              ]
            },
            {
              key: 'status',
              label: '操作状态',
              type: 'select',
              options: [
                { value: 'success', label: '成功' },
                { value: 'failed', label: '失败' }
              ]
            },
            {
              key: 'dateRange',
              label: '日期范围',
              type: 'dateRange'
            }
          ]}
          loading={loading}
        />
        
        {error && (
          <Alert 
            message="错误" 
            description={error} 
            type="error" 
            showIcon 
            style={{ marginTop: 16 }}
          />
        )}
        
        <Spin spinning={loading}>
          <Table 
            columns={logColumns} 
            dataSource={logData} 
            pagination={{ 
              showSizeChanger: true, 
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 条记录`,
              defaultPageSize: 20
            }}
            scroll={{ x: 1200 }}
            style={{ marginTop: 16 }}
          />
        </Spin>
      </Card>
    </Space>
  )
}

export default OperationLog