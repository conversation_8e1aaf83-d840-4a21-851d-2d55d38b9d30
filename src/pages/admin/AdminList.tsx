import React, { useState, useContext } from 'react'
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Switch,
  Dropdown,
  Menu,
  Divider,
  Popconfirm,
  Spin,
  Alert
} from 'antd'
import { 
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  UserOutlined,
  LockOutlined,
  UnlockOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'

const { Title } = Typography
const { Option } = Select

interface AdminDataType {
  id: number
  username: string
  email: string
  role: 'super_admin' | 'admin' | 'operator'
  status: 'active' | 'inactive'
  lastLogin: string
  createdAt: string
}

const adminData: AdminDataType[] = [
  { 
    id: 1, 
    username: 'admin', 
    email: '<EMAIL>', 
    role: 'super_admin',
    status: 'active',
    lastLogin: '2023-06-10 14:30',
    createdAt: '2023-01-01'
  },
  { 
    id: 2, 
    username: 'operator1', 
    email: '<EMAIL>', 
    role: 'operator',
    status: 'active',
    lastLogin: '2023-06-09 10:15',
    createdAt: '2023-03-15'
  },
  { 
    id: 3, 
    username: 'moderator', 
    email: '<EMAIL>', 
    role: 'admin',
    status: 'active',
    lastLogin: '2023-06-10 09:45',
    createdAt: '2023-02-20'
  },
  { 
    id: 4, 
    username: 'inactive_admin', 
    email: '<EMAIL>', 
    role: 'admin',
    status: 'inactive',
    lastLogin: '2023-05-01 16:20',
    createdAt: '2023-04-10'
  },
]

const adminColumns: TableProps<AdminDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    render: (role: string) => {
      const roleMap: Record<string, { text: string, color: string }> = {
        'super_admin': { text: '超级管理员', color: 'red' },
        'admin': { text: '管理员', color: 'gold' },
        'operator': { text: '操作员', color: 'blue' }
      }
      
      const r = roleMap[role] || { text: role, color: 'default' }
      return <Tag color={r.color}>{r.text}</Tag>
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => {
      const statusMap: Record<string, { text: string, color: 'success' | 'default' }> = {
        'active': { text: '正常', color: 'success' },
        'inactive': { text: '禁用', color: 'default' }
      }
      
      const stat = statusMap[status] || { text: status, color: 'default' }
      return <Tag color={stat.color}>{stat.text}</Tag>
    }
  },
  {
    title: '最后登录',
    dataIndex: 'lastLogin',
    key: 'lastLogin',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    render: (_, record) => (
      <Space size="middle">
        <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewAdmin(record)}>查看</Button>
        <Button type="link" icon={<EditOutlined />} size="small" onClick={() => handleEditAdmin(record)}>编辑</Button>
        <Dropdown 
          overlay={
            <Menu>
              <Menu.Item key="1" icon={<EyeOutlined />} onClick={() => handleViewAdmin(record)}>查看详情</Menu.Item>
              <Menu.Item key="2" icon={<EditOutlined />} onClick={() => handleEditAdmin(record)}>编辑管理员</Menu.Item>
              {record.status === 'active' ? (
                <Menu.Item key="3" icon={<LockOutlined />}>禁用账户</Menu.Item>
              ) : (
                <Menu.Item key="4" icon={<UnlockOutlined />}>启用账户</Menu.Item>
              )}
              <Menu.Divider />
              <Popconfirm
                title="确定删除此管理员吗？"
                description="删除后将无法恢复，请确认操作"
                onConfirm={() => {
                  // 在实际应用中，这里会调用删除API
                  message.success(`管理员 ${record.username} 删除成功`)
                }}
                okText="确定"
                cancelText="取消"
              >
                <Menu.Item key="5" danger icon={<DeleteOutlined />}>删除管理员</Menu.Item>
              </Popconfirm>
            </Menu>
          }
        >
          <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
        </Dropdown>
      </Space>
    ),
  },
]

const AdminList: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()
  const { message } = useContext(GlobalMessageContext)
  
  // 查看管理员详情
  const handleViewAdmin = (admin: AdminDataType) => {
    Modal.info({
      title: '管理员详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <strong>用户名：</strong>{admin.username}
            </Col>
            <Col span={12}>
              <strong>邮箱：</strong>{admin.email}
            </Col>
            <Col span={12}>
              <strong>角色：</strong>
              <Tag color={admin.role === 'super_admin' ? 'red' : admin.role === 'admin' ? 'gold' : 'blue'}>
                {admin.role === 'super_admin' ? '超级管理员' : admin.role === 'admin' ? '管理员' : '操作员'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={admin.status === 'active' ? 'success' : 'default'}>
                {admin.status === 'active' ? '正常' : '禁用'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>最后登录：</strong>{admin.lastLogin}
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{admin.createdAt}
            </Col>
          </Row>
        </div>
      ),
    });
  };

  // 编辑管理员
  const handleEditAdmin = (admin: AdminDataType) => {
    form.setFieldsValue({
      username: admin.username,
      email: admin.email,
      role: admin.role,
      status: admin.status
    });
    setIsModalVisible(true);
  };

  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('Received values of form: ', values)
      message.success('管理员创建成功')
      setIsModalVisible(false)
      form.resetFields()
    }).catch(info => {
      console.log('Validate Failed:', info)
      message.error('表单验证失败')
    })
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    setLoading(true)
    setError(null)
    // 模拟API调用
    setTimeout(() => {
      // 模拟随机错误
      if (Math.random() > 0.8) {
        setError('数据加载失败，请稍后重试')
        message.error('数据加载失败，请稍后重试')
      } else {
        message.success('数据刷新成功')
      }
      setLoading(false)
    }, 1000)
  }
  
  const handleAddAdmin = () => {
    showModal()
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>权限管理</Title>
      
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <QuickActions 
              onRefresh={handleRefresh}
              onAdd={handleAddAdmin}
              showRefresh
              showAdd
              refreshLoading={loading}
            />
          </Col>
        </Row>
        
        <SearchFilter 
          onSearch={handleSearch}
          onRefresh={handleRefresh}
          placeholder="搜索用户名/邮箱"
          filters={[
            {
              key: 'role',
              label: '管理员角色',
              type: 'select',
              options: [
                { value: 'super_admin', label: '超级管理员' },
                { value: 'admin', label: '管理员' },
                { value: 'operator', label: '操作员' }
              ]
            },
            {
              key: 'status',
              label: '状态',
              type: 'select',
              options: [
                { value: 'active', label: '正常' },
                { value: 'inactive', label: '禁用' }
              ]
            }
          ]}
          loading={loading}
        />
        
        {error && (
          <Alert 
            message="错误" 
            description={error} 
            type="error" 
            showIcon 
            style={{ marginTop: 16 }}
          />
        )}
        
        <Spin spinning={loading}>
          <Table 
            columns={adminColumns} 
            dataSource={adminData} 
            pagination={{ 
              showSizeChanger: true, 
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 条记录`,
              defaultPageSize: 20
            }}
            scroll={{ x: 1200 }}
            style={{ marginTop: 16 }}
          />
        </Spin>
      </Card>
      
      <Modal
        title="添加管理员"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="username" 
            label="用户名" 
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item 
            name="email" 
            label="邮箱" 
            rules={[{ required: true, message: '请输入邮箱' }]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>
          <Form.Item 
            name="role" 
            label="角色" 
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              <Option value="super_admin">超级管理员</Option>
              <Option value="admin">管理员</Option>
              <Option value="operator">操作员</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="password" 
            label="密码" 
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>
          <Form.Item name="status" label="状态" valuePropName="checked">
            <Switch defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default AdminList