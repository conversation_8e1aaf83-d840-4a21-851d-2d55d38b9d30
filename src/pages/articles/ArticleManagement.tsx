import React, { useState, useEffect } from 'react'
import { Table, Button, Space, Tag, Modal, Form, Input, Select, Switch, message, Card, Statistic, Row, Col, Popconfirm, Tooltip } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, StarOutlined, CrownOutlined } from '@ant-design/icons'
import type { Article } from '../../services/articleService'
import { articleService } from '../../services/articleService'
import RichTextEditor from '../../components/RichTextEditor'

const { TextArea } = Input
const { Option } = Select

interface ArticleStats {
  totalArticles: number
  publishedArticles: number
  draftArticles: number
  featuredArticles: number
  premiumArticles: number
  totalViews: number
  totalLikes: number
  categoryStats: Record<string, number>
}

const ArticleManagement: React.FC = () => {
  const [articles, setArticles] = useState<Article[]>([])
  const [stats, setStats] = useState<ArticleStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingArticle, setEditingArticle] = useState<Article | null>(null)
  const [form] = Form.useForm()

  // 加载文章列表
  const loadArticles = async () => {
    setLoading(true)
    try {
      const result = await articleService.getArticles()
      if (result.success && result.data) {
        setArticles(result.data)
      } else {
        message.error(result.error || '加载文章列表失败')
      }
    } catch (error) {
      message.error('加载文章列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载统计数据
  const loadStats = async () => {
    try {
      const result = await articleService.getArticleStats()
      if (result.success && result.data) {
        setStats(result.data)
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  useEffect(() => {
    loadArticles()
    loadStats()
  }, [])

  // 处理新增/编辑文章
  const handleSubmit = async (values: any) => {
    try {
      if (editingArticle) {
        // 编辑文章
        const result = await articleService.updateArticle(editingArticle.id, values)
        if (result.success) {
          message.success('文章更新成功')
          loadArticles()
          loadStats()
        } else {
          message.error(result.error || '文章更新失败')
        }
      } else {
        // 新增文章
        const result = await articleService.createArticle({
          ...values,
          author_id: 'current-user-id', // 这里应该从当前登录用户获取
          view_count: 0,
          like_count: 0,
          comment_count: 0
        })
        if (result.success) {
          message.success('文章创建成功')
          loadArticles()
          loadStats()
        } else {
          message.error(result.error || '文章创建失败')
        }
      }
      setModalVisible(false)
      setEditingArticle(null)
      form.resetFields()
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 删除文章
  const handleDelete = async (id: string) => {
    try {
      const result = await articleService.deleteArticle(id)
      if (result.success) {
        message.success('文章删除成功')
        loadArticles()
        loadStats()
      } else {
        message.error(result.error || '文章删除失败')
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 更新文章状态
  const handleStatusChange = async (id: string, status: 'draft' | 'published' | 'archived') => {
    try {
      const result = await articleService.updateArticleStatus(id, status)
      if (result.success) {
        message.success('状态更新成功')
        loadArticles()
        loadStats()
      } else {
        message.error(result.error || '状态更新失败')
      }
    } catch (error) {
      message.error('状态更新失败')
    }
  }

  // 切换精选状态
  const handleToggleFeatured = async (id: string, isFeatured: boolean) => {
    try {
      const result = await articleService.toggleFeatured(id, isFeatured)
      if (result.success) {
        message.success(`${isFeatured ? '设为' : '取消'}精选成功`)
        loadArticles()
        loadStats()
      } else {
        message.error(result.error || '操作失败')
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 切换付费状态
  const handleTogglePremium = async (id: string, isPremium: boolean) => {
    try {
      const result = await articleService.togglePremium(id, isPremium)
      if (result.success) {
        message.success(`${isPremium ? '设为' : '取消'}付费成功`)
        loadArticles()
        loadStats()
      } else {
        message.error(result.error || '操作失败')
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 打开编辑模态框
  const openEditModal = (article: Article) => {
    setEditingArticle(article)
    form.setFieldsValue({
      title: article.title,
      content: article.content,
      summary: article.summary,
      category: article.category,
      tags: article.tags?.join(', '),
      status: article.status,
      is_featured: article.is_featured,
      is_premium: article.is_premium,
      cover_image: article.cover_image,
      seo_title: article.seo_title,
      seo_description: article.seo_description
    })
    setModalVisible(true)
  }

  // 查看文章详情
  const handleViewArticle = (article: Article) => {
    Modal.info({
      title: '文章详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <strong>标题：</strong>{article.title}
            </Col>
            <Col span={12}>
              <strong>分类：</strong>{article.category}
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={article.status === 'published' ? 'success' : article.status === 'draft' ? 'default' : 'warning'}>
                {article.status === 'published' ? '已发布' : article.status === 'draft' ? '草稿' : '已归档'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>标签：</strong>
              <Space wrap>
                {article.tags?.map(tag => <Tag key={tag}>{tag}</Tag>)}
              </Space>
            </Col>
            <Col span={12}>
              <strong>作者：</strong>{article.author_id}
            </Col>
            <Col span={12}>
              <strong>浏览量：</strong>{article.view_count || 0}
            </Col>
            <Col span={12}>
              <strong>点赞数：</strong>{article.like_count || 0}
            </Col>
            <Col span={12}>
              <strong>评论数：</strong>{article.comment_count || 0}
            </Col>
            <Col span={12}>
              <strong>精选：</strong>
              <Tag color={article.is_featured ? 'gold' : 'default'}>
                {article.is_featured ? '是' : '否'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>付费：</strong>
              <Tag color={article.is_premium ? 'purple' : 'default'}>
                {article.is_premium ? '是' : '否'}
              </Tag>
            </Col>
            <Col span={24}>
              <strong>摘要：</strong>
              <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                {article.summary || '暂无摘要'}
              </div>
            </Col>
            <Col span={24}>
              <strong>内容：</strong>
              <div 
                style={{ 
                  marginTop: 8, 
                  padding: 12, 
                  backgroundColor: '#f5f5f5', 
                  borderRadius: 4,
                  maxHeight: '300px',
                  overflow: 'auto'
                }}
                dangerouslySetInnerHTML={{ __html: article.content }}
              />
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{new Date(article.created_at).toLocaleString()}
            </Col>
            <Col span={12}>
              <strong>更新时间：</strong>{article.updated_at ? new Date(article.updated_at).toLocaleString() : '-'}
            </Col>
          </Row>
        </div>
      ),
      okText: '关闭'
    })
  }

  // 打开新增模态框
  const openAddModal = () => {
    setEditingArticle(null)
    form.resetFields()
    setModalVisible(true)
  }

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'green'
      case 'draft': return 'orange'
      case 'archived': return 'red'
      default: return 'default'
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'published': return '已发布'
      case 'draft': return '草稿'
      case 'archived': return '已归档'
      default: return status
    }
  }

  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
      render: (text: string, record: Article) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          {record.summary && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              {record.summary.length > 50 ? `${record.summary.substring(0, 50)}...` : record.summary}
            </div>
          )}
        </div>
      )
    },
    {
      title: '作者',
      dataIndex: ['author', 'username'],
      key: 'author',
      width: 100
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: Article) => (
        <Select
          value={status}
          size="small"
          style={{ width: 80 }}
          onChange={(value) => handleStatusChange(record.id, value as 'draft' | 'published' | 'archived')}
        >
          <Option value="draft">草稿</Option>
          <Option value="published">发布</Option>
          <Option value="archived">归档</Option>
        </Select>
      )
    },
    {
      title: '标签',
      key: 'tags',
      width: 120,
      render: (record: Article) => (
        <Space>
          {record.is_featured && (
            <Tooltip title="精选文章">
              <Tag color="gold" icon={<StarOutlined />}>
                精选
              </Tag>
            </Tooltip>
          )}
          {record.is_premium && (
            <Tooltip title="付费文章">
              <Tag color="purple" icon={<CrownOutlined />}>
                付费
              </Tag>
            </Tooltip>
          )}
        </Space>
      )
    },
    {
      title: '数据',
      key: 'stats',
      width: 120,
      render: (record: Article) => (
        <div style={{ fontSize: '12px' }}>
          <div>浏览: {record.view_count || 0}</div>
          <div>点赞: {record.like_count || 0}</div>
          <div>评论: {record.comment_count || 0}</div>
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: Article) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewArticle(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openEditModal(record)}
            />
          </Tooltip>
          <Tooltip title={record.is_featured ? '取消精选' : '设为精选'}>
            <Button
              type="text"
              icon={<StarOutlined />}
              style={{ color: record.is_featured ? '#faad14' : '#d9d9d9' }}
              onClick={() => handleToggleFeatured(record.id, !record.is_featured)}
            />
          </Tooltip>
          <Tooltip title={record.is_premium ? '取消付费' : '设为付费'}>
            <Button
              type="text"
              icon={<CrownOutlined />}
              style={{ color: record.is_premium ? '#722ed1' : '#d9d9d9' }}
              onClick={() => handleTogglePremium(record.id, !record.is_premium)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这篇文章吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <h2>文章管理</h2>
      
      {/* 统计概览 */}
      {stats && (
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={4}>
            <Card>
              <Statistic title="总文章数" value={stats.totalArticles} />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic title="已发布" value={stats.publishedArticles} valueStyle={{ color: '#3f8600' }} />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic title="草稿" value={stats.draftArticles} valueStyle={{ color: '#cf1322' }} />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic title="精选文章" value={stats.featuredArticles} valueStyle={{ color: '#faad14' }} />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic title="总浏览量" value={stats.totalViews} />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic title="总点赞数" value={stats.totalLikes} />
            </Card>
          </Col>
        </Row>
      )}

      {/* 操作按钮 */}
      <div style={{ marginBottom: '16px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={openAddModal}
        >
          新增文章
        </Button>
      </div>

      {/* 文章列表 */}
      <Table
        columns={columns}
        dataSource={articles}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`
        }}
        scroll={{ x: 1200 }}
      />

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingArticle ? '编辑文章' : '新增文章'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingArticle(null)
          form.resetFields()
        }}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: 'draft',
            is_featured: false,
            is_premium: false
          }}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入文章标题' }]}
          >
            <Input placeholder="请输入文章标题" />
          </Form.Item>

          <Form.Item
            name="summary"
            label="摘要"
          >
            <RichTextEditor placeholder="请输入文章摘要" style={{ minHeight: '100px' }} />
          </Form.Item>

          <Form.Item
            name="content"
            label="内容"
            rules={[{ required: true, message: '请输入文章内容' }]}
          >
            <RichTextEditor 
              placeholder="请输入文章内容"
              style={{ height: '300px' }}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择文章分类' }]}
              >
                <Select placeholder="请选择分类">
                  <Option value="技术">技术</Option>
                  <Option value="生活">生活</Option>
                  <Option value="思考">思考</Option>
                  <Option value="教程">教程</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
              >
                <Select>
                  <Option value="draft">草稿</Option>
                  <Option value="published">发布</Option>
                  <Option value="archived">归档</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Input placeholder="请输入标签，多个标签用逗号分隔" />
          </Form.Item>

          <Form.Item
            name="cover_image"
            label="封面图片"
          >
            <Input placeholder="请输入封面图片URL" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="seo_title"
                label="SEO标题"
              >
                <Input placeholder="请输入SEO标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="seo_description"
                label="SEO描述"
              >
                <Input placeholder="请输入SEO描述" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="is_featured"
                label="精选文章"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_premium"
                label="付费文章"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingArticle ? '更新' : '创建'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingArticle(null)
                form.resetFields()
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ArticleManagement