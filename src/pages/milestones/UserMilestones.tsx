import React, { useState, useEffect } from 'react'
import { Card, Table, Button, Space, Tag, message, Modal, Statistic, Row, Col, Progress, Avatar, Tooltip } from 'antd'
import { TrophyOutlined, CheckCircleOutlined, ReloadOutlined, DeleteOutlined, UserOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UserMilestone } from '../../services/milestoneService'
import { milestoneService } from '../../services/milestoneService'

const UserMilestones: React.FC = () => {
  const [milestones, setMilestones] = useState<UserMilestone[]>([])
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState<any>({})

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      const [milestonesResult, statsResult] = await Promise.all([
        milestoneService.getUserMilestones(),
        milestoneService.getMilestoneStats()
      ])

      if (milestonesResult.success) {
        setMilestones(milestonesResult.data || [])
      } else {
        message.error(milestonesResult.error || '加载里程碑数据失败')
      }

      if (statsResult.success) {
        setStats(statsResult.data || {})
      }
    } catch (error) {
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  // 标记为已达成
  const handleMarkAchieved = async (id: string) => {
    try {
      const result = await milestoneService.markMilestoneAchieved(id)
      if (result.success) {
        message.success('标记成功')
        loadData()
      } else {
        message.error(result.error || '标记失败')
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 重置进度
  const handleResetProgress = async (id: string) => {
    Modal.confirm({
      title: '确认重置',
      content: '确定要重置这个里程碑的进度吗？',
      onOk: async () => {
        try {
          const result = await milestoneService.resetMilestoneProgress(id)
          if (result.success) {
            message.success('重置成功')
            loadData()
          } else {
            message.error(result.error || '重置失败')
          }
        } catch (error) {
          message.error('操作失败')
        }
      }
    })
  }

  // 删除里程碑
  const handleDelete = async (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个里程碑吗？此操作不可恢复。',
      okType: 'danger',
      onOk: async () => {
        try {
          const result = await milestoneService.deleteUserMilestone(id)
          if (result.success) {
            message.success('删除成功')
            loadData()
          } else {
            message.error(result.error || '删除失败')
          }
        } catch (error) {
          message.error('操作失败')
        }
      }
    })
  }

  // 获取里程碑类型标签颜色
  const getMilestoneTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'daily_login': 'blue',
      'post_count': 'green',
      'comment_count': 'orange',
      'like_received': 'red',
      'flow_score': 'purple',
      'achievement': 'gold'
    }
    return colorMap[type] || 'default'
  }

  // 获取里程碑类型显示名称
  const getMilestoneTypeName = (type: string) => {
    const nameMap: Record<string, string> = {
      'daily_login': '每日登录',
      'post_count': '发帖数量',
      'comment_count': '评论数量',
      'like_received': '获赞数量',
      'flow_score': '心流分数',
      'achievement': '成就达成'
    }
    return nameMap[type] || type
  }

  const columns: ColumnsType<UserMilestone> = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      width: 120,
      render: (user: any) => (
        <Space>
          <Avatar 
            size="small" 
            src={user?.avatar_url} 
            icon={<UserOutlined />}
          />
          <span>{user?.username || '未知用户'}</span>
        </Space>
      )
    },
    {
      title: '里程碑名称',
      dataIndex: 'milestone_name',
      key: 'milestone_name',
      width: 200
    },
    {
      title: '类型',
      dataIndex: 'milestone_type',
      key: 'milestone_type',
      width: 100,
      render: (type: string) => (
        <Tag color={getMilestoneTypeColor(type)}>
          {getMilestoneTypeName(type)}
        </Tag>
      )
    },
    {
      title: '进度',
      key: 'progress',
      width: 150,
      render: (_, record) => {
        const progress = Math.min((record.current_value / record.target_value) * 100, 100)
        return (
          <div>
            <Progress 
              percent={Math.round(progress)} 
              size="small" 
              status={record.is_achieved ? 'success' : 'active'}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
              {record.current_value} / {record.target_value}
            </div>
          </div>
        )
      }
    },
    {
      title: '状态',
      dataIndex: 'is_achieved',
      key: 'is_achieved',
      width: 80,
      render: (isAchieved: boolean) => (
        <Tag color={isAchieved ? 'success' : 'processing'}>
          {isAchieved ? '已达成' : '进行中'}
        </Tag>
      )
    },
    {
      title: '奖励积分',
      dataIndex: 'reward_points',
      key: 'reward_points',
      width: 100,
      render: (points: number) => points ? `${points} 分` : '-'
    },
    {
      title: '达成时间',
      dataIndex: 'achieved_at',
      key: 'achieved_at',
      width: 150,
      render: (achievedAt: string) => 
        achievedAt ? new Date(achievedAt).toLocaleString() : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (createdAt: string) => new Date(createdAt).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          {!record.is_achieved && (
            <Tooltip title="标记为已达成">
              <Button
                type="primary"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleMarkAchieved(record.id)}
              >
                达成
              </Button>
            </Tooltip>
          )}
          {record.is_achieved && (
            <Tooltip title="重置进度">
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => handleResetProgress(record.id)}
              >
                重置
              </Button>
            </Tooltip>
          )}
          <Tooltip title="删除里程碑">
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
            >
              删除
            </Button>
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总里程碑数"
              value={stats.totalMilestones || 0}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已达成数"
              value={stats.achievedMilestones || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日达成"
              value={stats.todayAchieved || 0}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="达成率"
              value={stats.achievementRate || 0}
              suffix="%"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 里程碑列表 */}
      <Card 
        title="用户里程碑管理" 
        extra={
          <Button 
            type="primary" 
            onClick={loadData}
            loading={loading}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={milestones}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            total: milestones.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>
    </div>
  )
}

export default UserMilestones