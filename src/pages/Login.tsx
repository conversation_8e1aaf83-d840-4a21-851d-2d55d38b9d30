import React, { useState } from 'react'
import { 
  Space, 
  Typography, 
  Card, 
  Form, 
  Input, 
  Button, 
  Checkbox,
  Row,
  Col,
  message,
  Tabs
} from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useAuth } from '../hooks/useAuth'
import { useNavigate } from 'react-router-dom'

const { Title } = Typography
const { TabPane } = Tabs

interface LoginFormValues {
  email: string
  password: string
  remember?: boolean
}

const Login: React.FC = () => {
  const [activeTab, setActiveTab] = useState('login')
  const [loading, setLoading] = useState(false)
  const { login, signup } = useAuth()
  const navigate = useNavigate()

  const onFinish = async (values: LoginFormValues) => {
    setLoading(true)
    try {
      if (activeTab === 'login') {
        const result = await login(values.email, values.password)
        if (result.success) {
          message.success('登录成功')
          navigate('/')
        } else {
          message.error(result.error?.message || '登录失败，请检查邮箱和密码')
        }
      } else {
        const result = await signup(values.email, values.password)
        if (result.success) {
          message.success('注册成功，请登录')
          setActiveTab('login')
        } else {
          message.error(result.error?.message || '注册失败，请稍后再试')
        }
      }
    } catch (error) {
      message.error('操作失败，请稍后再试')
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Row justify="center" align="middle" style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Col xs={22} sm={18} md={12} lg={8} xl={6} xxl={5}>
        <div style={{ maxWidth: '400px', margin: '0 auto' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Title level={2} style={{ textAlign: 'center', marginBottom: '32px' }}>Rebase Admin</Title>
            <Card style={{ borderRadius: '8px', boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}>
              <Tabs activeKey={activeTab} onChange={setActiveTab} centered>
                <TabPane tab="登录" key="login">
                  <Form
                    name="normal_login"
                    className="login-form"
                    initialValues={{ remember: true }}
                    onFinish={onFinish}
                  >
                    <Form.Item
                      name="email"
                      rules={[
                        { required: true, message: '请输入邮箱!' },
                        { type: 'email', message: '请输入有效的邮箱地址!' }
                      ]}
                    >
                      <Input prefix={<UserOutlined className="site-form-item-icon" />} placeholder="邮箱" size="large" />
                    </Form.Item>
                    <Form.Item
                      name="password"
                      rules={[{ required: true, message: '请输入密码!' }]}
                    >
                      <Input
                        prefix={<LockOutlined className="site-form-item-icon" />}
                        type="password"
                        placeholder="密码"
                        size="large"
                      />
                    </Form.Item>
                    <Form.Item>
                      <Form.Item name="remember" valuePropName="checked" noStyle>
                        <Checkbox>记住我</Checkbox>
                      </Form.Item>

                      <a className="login-form-forgot" href="#" style={{ float: 'right' }}>
                        忘记密码
                      </a>
                    </Form.Item>

                    <Form.Item>
                      <Button type="primary" htmlType="submit" loading={loading} size="large" style={{ width: '100%' }}>
                        登录
                      </Button>
                    </Form.Item>
                  </Form>
                </TabPane>
                <TabPane tab="注册" key="register">
                  <Form
                    name="register"
                    className="register-form"
                    onFinish={onFinish}
                  >
                    <Form.Item
                      name="email"
                      rules={[
                        { required: true, message: '请输入邮箱!' },
                        { type: 'email', message: '请输入有效的邮箱地址!' }
                      ]}
                    >
                      <Input prefix={<UserOutlined className="site-form-item-icon" />} placeholder="邮箱" size="large" />
                    </Form.Item>
                    <Form.Item
                      name="password"
                      rules={[
                        { required: true, message: '请输入密码!' },
                        { min: 6, message: '密码长度至少为6个字符!' }
                      ]}
                    >
                      <Input
                        prefix={<LockOutlined className="site-form-item-icon" />}
                        type="password"
                        placeholder="密码"
                        size="large"
                      />
                    </Form.Item>
                    <Form.Item>
                      <Button type="primary" htmlType="submit" loading={loading} size="large" style={{ width: '100%' }}>
                        注册
                      </Button>
                    </Form.Item>
                  </Form>
                </TabPane>
              </Tabs>
            </Card>
          </Space>
        </div>
      </Col>
    </Row>
  )
}

export default Login