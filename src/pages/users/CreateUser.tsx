import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Select, 
  Switch, 
  Space, 
  Typography, 
  message,
  Row,
  Col,
  Divider
} from 'antd'
import { UserOutlined, MailOutlined, PhoneOutlined, SaveOutlined, ArrowLeftOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { userService, type User } from '../../services/userService'

const { Title } = Typography
const { Option } = Select

const CreateUser: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()

  const handleSubmit = async (values: any) => {
    setLoading(true)
    try {
      const userData: Omit<User, 'id' | 'created_at' | 'updated_at'> = {
        username: values.username,
        email: values.email,
        phone: values.phone || null,
        join_date: new Date().toISOString(),
        last_active: null,
        level: values.level || '1',
        flow_value: values.flow_value || 0,
        status: values.status || 'active',
        is_premium: values.is_premium || false,
        streak: 0,
        posts_count: 0,
        checkins_count: 0,
        role: values.role || 'user',
        avatar_url: null
      }

      const result = await userService.createUser(userData)
      
      if (result.success) {
        message.success('用户创建成功')
        navigate('/users')
      } else {
        message.error(result.error?.message || '创建用户失败')
      }
    } catch (error) {
      message.error('创建用户失败')
      console.error('创建用户错误:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    navigate('/users')
  }

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleCancel}
            style={{ marginBottom: 16 }}
          >
            返回用户列表
          </Button>
          <Title level={2}>创建新用户</Title>
        </div>

        <Card>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              status: 'active',
              is_premium: false,
              level: '1',
              flow_value: 0,
              role: 'user'
            }}
          >
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="username"
                  label="用户名"
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 2, max: 20, message: '用户名长度为2-20个字符' }
                  ]}
                >
                  <Input 
                    prefix={<UserOutlined />} 
                    placeholder="请输入用户名" 
                  />
                </Form.Item>
              </Col>
              
              <Col span={12}>
                <Form.Item
                  name="email"
                  label="邮箱"
                  rules={[
                    { required: true, message: '请输入邮箱' },
                    { type: 'email', message: '请输入有效的邮箱地址' }
                  ]}
                >
                  <Input 
                    prefix={<MailOutlined />} 
                    placeholder="请输入邮箱" 
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="phone"
                  label="手机号"
                  rules={[
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                  ]}
                >
                  <Input 
                    prefix={<PhoneOutlined />} 
                    placeholder="请输入手机号（可选）" 
                  />
                </Form.Item>
              </Col>
              
              <Col span={12}>
                <Form.Item
                  name="level"
                  label="等级"
                >
                  <Select placeholder="选择用户等级">
                    {Array.from({ length: 10 }, (_, i) => (
                      <Option key={i + 1} value={(i + 1).toString()}>
                        等级 {i + 1}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="flow_value"
                  label="心流值"
                >
                  <Input 
                    type="number" 
                    placeholder="初始心流值" 
                    min={0}
                  />
                </Form.Item>
              </Col>
              
              <Col span={12}>
                <Form.Item
                  name="role"
                  label="角色"
                >
                  <Select placeholder="选择用户角色">
                    <Option value="user">普通用户</Option>
                    <Option value="moderator">版主</Option>
                    <Option value="admin">管理员</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="状态"
                >
                  <Select placeholder="选择用户状态">
                    <Option value="active">活跃</Option>
                    <Option value="inactive">非活跃</Option>
                    <Option value="banned">已封禁</Option>
                  </Select>
                </Form.Item>
              </Col>
              
              <Col span={12}>
                <Form.Item
                  name="is_premium"
                  label="会员状态"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="会员" 
                    unCheckedChildren="普通" 
                  />
                </Form.Item>
              </Col>
            </Row>

            <Divider />

            <Form.Item>
              <Space>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                  icon={<SaveOutlined />}
                >
                  创建用户
                </Button>
                <Button onClick={handleCancel}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      </Space>
    </div>
  )
}

export default CreateUser