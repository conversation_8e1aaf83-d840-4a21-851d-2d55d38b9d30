import React, { useState, useContext, useEffect } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Switch,
  Modal,
  Form,
  Dropdown,
  Menu,
  Divider,
  Popconfirm,
  Spin,
  Alert,
  message
} from 'antd'
import { 
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  LockOutlined,
  UnlockOutlined,
  UserOutlined,
  CrownOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'
import { userService, type User } from '../../services/userService'

const { Title } = Typography
const { Option } = Select

interface UserDataType {
  id: string
  username: string
  email: string
  phone?: string
  created_at: string
  last_sign_in?: string
  level: number
  points: number
  status: 'active' | 'inactive' | 'banned'
  is_premium: boolean
  streak_days: number
  posts_count: number
  check_in_count: number
  avatar_url?: string
  role?: string
}

// 将User类型转换为UserDataType
const mapUserToUserData = (user: User): UserDataType => {
  const anyUser = user as any
  const levelNum = Number(anyUser.level ?? 0)
  return {
    id: anyUser.id,
    username: anyUser.username || '',
    email: anyUser.email || '',
    phone: anyUser.phone || '',
    created_at: anyUser.created_at || anyUser.join_date || '',
    last_sign_in: anyUser.last_active || anyUser.last_sign_in || '',
    level: Number.isFinite(levelNum) ? levelNum : 0,
    points: Number(anyUser.flow_value ?? anyUser.points ?? 0),
    status: anyUser.status || 'active',
    is_premium: Boolean(anyUser.is_premium),
    streak_days: Number(anyUser.streak ?? anyUser.streak_days ?? 0),
    posts_count: Number(anyUser.posts_count ?? 0),
    check_in_count: Number(anyUser.check_in_count ?? anyUser.checkins_count ?? 0),
    avatar_url: anyUser.avatar_url || '',
    role: anyUser.role || 'user',
  }
}

const UserList: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [userData, setUserData] = useState<UserDataType[]>([])
  const [searchText, setSearchText] = useState('')
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [currentUser, setCurrentUser] = useState<UserDataType | null>(null)
  const [form] = Form.useForm()
  const globalMessage = useContext(GlobalMessageContext)

  // 加载用户数据
  const loadUsers = async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await userService.getAllUsers()
      if (result.success && result.data) {
        setUserData(result.data.map(mapUserToUserData))
      } else {
        setError(new Error('数据加载失败，请稍后重试'))
      }
    } catch (err) {
      setError(err as Error)
      message.error('加载用户数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 搜索用户
  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      loadUsers()
      return
    }
    
    setLoading(true)
    try {
      const result = await userService.searchUsers(query)
      if (result.success && result.data) {
        setUserData(result.data.map(mapUserToUserData))
      } else {
        setUserData([])
      }
    } catch (err) {
      message.error('搜索失败')
    } finally {
      setLoading(false)
    }
  }

  // 更新用户状态
  const updateUserStatus = async (userId: string, status: 'active' | 'inactive' | 'banned') => {
    try {
      const { success, error } = await userService.updateUser(userId, { status })
      if (error) throw error
      if (success) {
        message.success('用户状态已更新')
        loadUsers()
      }
    } catch (err) {
      message.error('更新用户状态失败')
      console.error(err)
    }
  }

  // 更新用户角色
  const updateUserRole = async (userId: string, role: string) => {
    try {
      const { success, error } = await userService.updateUser(userId, { role })
      if (error) throw error
      if (success) {
        message.success('用户角色已更新')
        loadUsers()
      }
    } catch (err) {
      message.error('更新用户角色失败')
      console.error(err)
    }
  }

  // 编辑用户
  const handleEdit = (user: UserDataType) => {
    setCurrentUser(user)
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      phone: user.phone,
      level: user.level,
      points: user.points,
      is_premium: user.is_premium,
      role: user.role || 'user'
    })
    setEditModalVisible(true)
  }

  // 保存编辑
  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      if (!currentUser) return

      const { success, error } = await userService.updateUser(currentUser.id, values)
      if (error) throw error

      if (success) {
        message.success('用户信息已更新')
        setEditModalVisible(false)
        loadUsers()
      }
    } catch (err) {
      message.error('保存用户信息失败')
      console.error(err)
    }
  }

  // 初始加载
  useEffect(() => {
    loadUsers()
  }, [])

  // 表格列定义
  const userColumns: TableProps<UserDataType>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text, record) => (
        <Space>
          {record.avatar_url ? (
            <img 
              src={record.avatar_url} 
              alt={text} 
              style={{ width: 24, height: 24, borderRadius: '50%', marginRight: 8 }} 
            />
          ) : (
            <UserOutlined style={{ marginRight: 8 }} />
          )}
          {text}
          {(record.role === 'ADMIN' || record.role === 'SUPER_ADMIN') && <CrownOutlined style={{ color: '#faad14' }} />}
        </Space>
      )
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone) => phone || '-'
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
      render: (level: number) => {
        let color = 'green'
       if (level === 0) color = 'gray'
      if (level === 1) color = 'blue'
      if (level === 2) color = 'green'
      if (level === 3) color = 'gold'
      if (level >= 4) color = 'purple'
      
      return <Tag color={color}>L{level}</Tag>
    }
  },
  {
    title: '积分',
    dataIndex: 'points',
    key: 'points',
    render: (points: number) => {
      return `${points} 点`
    }
  },
  {
    title: '连续天数',
    dataIndex: 'streak_days',
    key: 'streak_days',
  },
  {
    title: '帖子数',
    dataIndex: 'posts_count',
    key: 'posts_count',
  },
  {
    title: '打卡数',
    dataIndex: 'check_in_count',
    key: 'check_in_count',
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    render: (role: string) => {
      let color = 'default'
      let text = '普通用户'
      
      if (role === 'ADMIN') {
        color = 'gold'
        text = '管理员'
      } else if (role === 'SUPER_ADMIN') {
        color = 'red'
        text = '超级管理员'
      } else if (role === 'PARTNER') {
        color = 'blue'
        text = '合作伙伴'
      } else if (role === 'MEMBER') {
        color = 'green'
        text = '会员'
      }
      
      return <Tag color={color} icon={(role === 'ADMIN' || role === 'SUPER_ADMIN') ? <CrownOutlined /> : null}>{text}</Tag>
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => {
      let color: 'success' | 'error' | 'default' = 'default'
      let text = ''
      
      if (status === 'active') {
        color = 'success'
        text = '正常'
      } else if (status === 'inactive') {
        color = 'default'
        text = '未激活'
      } else if (status === 'banned') {
        color = 'error'
        text = '已封禁'
      }
      
      return <Tag color={color}>{text}</Tag>
    }
  },
  {
    title: 'VIP',
    dataIndex: 'is_premium',
    key: 'is_premium',
    render: (is_premium: boolean) => (
      <Tag color={is_premium ? 'gold' : 'default'} icon={is_premium ? <CrownOutlined /> : null}>
        {is_premium ? '是' : '否'}
      </Tag>
    )
  },
  {
    title: '注册日期',
    dataIndex: 'created_at',
    key: 'created_at',
    render: (date: string) => new Date(date).toLocaleDateString()
  },
  {
    title: '最后活跃',
    dataIndex: 'last_sign_in',
    key: 'last_sign_in',
    render: (date: string) => date ? new Date(date).toLocaleDateString() : '-'
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    render: (_, record) => (
      <Space size="middle">
        <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewUser(record)}>查看</Button>
        <Button type="link" icon={<EditOutlined />} size="small" onClick={() => handleEdit(record)}>编辑</Button>
        <Dropdown 
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: '查看详情',
                onClick: () => handleViewUser(record)
              },
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: '编辑用户',
                onClick: () => handleEdit(record)
              },
              {
                key: 'reset',
                icon: <UserOutlined />,
                label: '重置密码'
              },
              {
                key: 'status',
                icon: record.status === 'banned' ? <UnlockOutlined /> : <LockOutlined />,
                label: record.status === 'banned' ? '解封用户' : '封禁用户',
                onClick: () => updateUserStatus(record.id, record.status === 'banned' ? 'active' : 'banned')
              },
              {
                type: 'divider'
              },
              {
                key: 'premium',
                icon: <CrownOutlined />,
                label: record.is_premium ? '取消VIP' : '设为VIP',
                onClick: () => userService.updateUser(record.id, { is_premium: !record.is_premium })
                  .then(({success}) => {
                    if (success) {
                      message.success(`用户 ${record.username} ${record.is_premium ? '取消VIP' : '设为VIP'} 成功`);
                      loadUsers();
                    }
                  })
              },
              {
                key: 'role',
                icon: <UserOutlined />,
                label: '角色管理',
                children: [
                  {
                    key: 'role-user',
                    label: '设为普通用户',
                    onClick: () => updateUserRole(record.id, 'USER')
                  },
                  {
                    key: 'role-member',
                    label: '设为会员',
                    onClick: () => updateUserRole(record.id, 'MEMBER')
                  },
                  {
                    key: 'role-partner',
                    label: '设为合作伙伴',
                    onClick: () => updateUserRole(record.id, 'PARTNER')
                  },
                  {
                    key: 'role-admin',
                    label: '设为管理员',
                    onClick: () => updateUserRole(record.id, 'ADMIN')
                  }
                ]
              }
            ]
          }}
        >
          <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
        </Dropdown>
      </Space>
    ),
  },
]

  // 查看用户详情
  const handleViewUser = (record: UserDataType) => {
    Modal.info({
      title: '用户详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <strong>用户名：</strong>{record.username}
            </Col>
            <Col span={12}>
              <strong>邮箱：</strong>{record.email}
            </Col>
            <Col span={12}>
              <strong>手机号：</strong>{record.phone || '-'}
            </Col>
            <Col span={12}>
              <strong>用户等级：</strong>L{record.level}
            </Col>
            <Col span={12}>
              <strong>积分：</strong>{record.points}
            </Col>
            <Col span={12}>
              <strong>连续签到：</strong>{record.streak_days}天
            </Col>
            <Col span={12}>
              <strong>发帖数：</strong>{record.posts_count}
            </Col>
            <Col span={12}>
              <strong>签到次数：</strong>{record.check_in_count}
            </Col>
            <Col span={12}>
              <strong>VIP状态：</strong>
              <Tag color={record.is_premium ? 'gold' : 'default'} icon={record.is_premium ? <CrownOutlined /> : null}>
                {record.is_premium ? '是' : '否'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>用户状态：</strong>
              <Tag color={record.status === 'active' ? 'success' : record.status === 'banned' ? 'error' : 'default'}>
                {record.status === 'active' ? '正常' : record.status === 'banned' ? '已封禁' : '未激活'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>注册时间：</strong>{new Date(record.created_at).toLocaleString()}
            </Col>
            <Col span={12}>
              <strong>最后活跃：</strong>{record.last_sign_in ? new Date(record.last_sign_in).toLocaleString() : '-'}
            </Col>
          </Row>
        </div>
      ),
      okText: '关闭'
    })
  }

  // 模态框状态和处理函数
  const [isModalVisible, setIsModalVisible] = useState(false)
  
  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('Received values of form: ', values)
      message.success('用户创建成功')
      setIsModalVisible(false)
      form.resetFields()
    }).catch(info => {
      console.log('Validate Failed:', info)
      message.error('表单验证失败')
    })
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = (query?: string) => {
    if (query) {
      setSearchText(query)
      searchUsers(query)
    } else {
      message.success('搜索完成')
    }
  }
  
  const handleRefresh = () => {
    loadUsers()
  }
  
  const handleAddUser = () => {
    showModal()
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>用户管理</Title>
      
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <QuickActions 
              onRefresh={handleRefresh}
              onAdd={handleAddUser}
              showRefresh
              showAdd
              refreshLoading={loading}
            />
          </Col>
        </Row>
        
        <SearchFilter 
          onSearch={handleSearch}
          onRefresh={handleRefresh}
          placeholder="搜索用户名/邮箱/手机号"
          filters={[
            {
              key: 'status',
              label: '用户状态',
              type: 'select',
              options: [
                { value: 'active', label: '正常' },
                { value: 'inactive', label: '未激活' },
                { value: 'banned', label: '已封禁' }
              ]
            },
            {
              key: 'level',
              label: '用户等级',
              type: 'select',
              options: [
                { value: 'L1', label: 'L1' },
                { value: 'L2', label: 'L2' },
                { value: 'L3', label: 'L3' },
                { value: 'L4', label: 'L4' },
                { value: 'L5', label: 'L5' },
                { value: 'L6', label: 'L6' },
                { value: 'L7', label: 'L7' }
              ]
            },
            {
              key: 'isPremium',
              label: 'VIP状态',
              type: 'select',
              options: [
                { value: 'true', label: 'VIP用户' },
                { value: 'false', label: '普通用户' }
              ]
            }
          ]}
          loading={loading}
        />
        
        {error && (
          <Alert 
            message="错误" 
            description={error.message || '发生未知错误'} 
            type="error" 
            showIcon 
            closable
            style={{ marginTop: 16 }}
          />
        )}
        
        <Spin spinning={loading}>
          <Table 
            columns={userColumns} 
            dataSource={userData} 
            pagination={{ 
              showSizeChanger: true, 
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 条记录`,
              defaultPageSize: 20
            }}
            scroll={{ x: 1200 }}
            style={{ marginTop: 16 }}
          />
        </Spin>
      </Card>
      
      <Modal
        title="新增用户"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="username" 
            label="用户名" 
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item 
            name="email" 
            label="邮箱" 
            rules={[{ required: true, message: '请输入邮箱' }]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>
          <Form.Item 
            name="phone" 
            label="手机号" 
            rules={[{ required: true, message: '请输入手机号' }]}
          >
            <Input placeholder="请输入手机号" />
          </Form.Item>
          <Form.Item 
            name="role" 
            label="用户角色" 
            rules={[{ required: true, message: '请选择用户角色' }]}
          >
            <Select placeholder="请选择用户角色">
              <Option value="USER">普通用户</Option>
              <Option value="MEMBER">会员</Option>
              <Option value="PARTNER">合作伙伴</Option>
              <Option value="ADMIN">管理员</Option>
              <Option value="SUPER_ADMIN">超级管理员</Option>
            </Select>
          </Form.Item>
          <Form.Item name="isPremium" label="VIP用户" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default UserList