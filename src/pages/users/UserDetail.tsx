import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Avatar,
  Spin,
  Alert,
  Row,
  Col,
  Statistic,
  Timeline,
  Tabs,
  Table,
  message
} from 'antd'
import {
  ArrowLeftOutlined,
  EditOutlined,
  UserOutlined,
  CrownOutlined,
  CalendarOutlined,
  TrophyOutlined,
  FireOutlined,
  MessageOutlined
} from '@ant-design/icons'
import { userService, type User } from '../../services/userService'

const { TabPane } = Tabs

interface UserDetailProps {
  userId?: string
}

const UserDetail: React.FC<UserDetailProps> = ({ userId: propUserId }) => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const userId = propUserId || id
  
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [error, setError] = useState<Error | null>(null)

  // 加载用户详情
  const loadUserDetail = async () => {
    if (!userId) return
    
    setLoading(true)
    setError(null)
    try {
      const result = await userService.getUserById(userId)
      if (result.success && result.data) {
        setUser(result.data)
      } else {
        throw new Error(result.error || '加载用户失败')
      }
    } catch (err) {
      setError(err as Error)
      message.error('加载用户详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadUserDetail()
  }, [userId])

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error || !user) {
    return (
      <Alert
        message="加载失败"
        description={error?.message || '用户不存在'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => navigate('/users')}>
            返回列表
          </Button>
        }
      />
    )
  }

  // 获取用户状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'green', text: '正常' },
      inactive: { color: 'orange', text: '未激活' },
      banned: { color: 'red', text: '已封禁' }
    }
    const config = statusMap[status as keyof typeof statusMap] || statusMap.inactive
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取等级颜色
  const getLevelColor = (level: number) => {
    if (level === 0) return '#8c8c8c'
    if (level <= 5) return '#1890ff'
    if (level <= 10) return '#52c41a'
    if (level <= 20) return '#faad14'
    return '#f5222d'
  }

  // 获取角色标签
  const getRoleTag = (role: string) => {
    const roleMap = {
      SUPER_ADMIN: { color: 'red', text: '超级管理员', icon: <CrownOutlined /> },
      ADMIN: { color: 'orange', text: '管理员', icon: <CrownOutlined /> },
      user: { color: 'blue', text: '普通用户', icon: <UserOutlined /> }
    }
    const config = roleMap[role as keyof typeof roleMap] || roleMap.user
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  return (
    <div>
      {/* 头部操作栏 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/users')}
          >
            返回列表
          </Button>
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => navigate(`/users/${userId}/edit`)}
          >
            编辑用户
          </Button>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        {/* 基本信息卡片 */}
        <Col xs={24} lg={16}>
          <Card title="基本信息" style={{ marginBottom: 24 }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: 24 }}>
              <Avatar 
                size={80} 
                icon={<UserOutlined />}
                style={{ marginRight: 24 }}
              />
              <div style={{ flex: 1 }}>
                <h2 style={{ margin: 0, marginBottom: 8 }}>
                  {user.username || '未设置用户名'}
                  {user.is_premium && (
                    <Tag color="gold" style={{ marginLeft: 8 }}>
                      <CrownOutlined /> 会员
                    </Tag>
                  )}
                </h2>
                <Space size="middle">
                  {getStatusTag(user.status || 'inactive')}
                  {getRoleTag(user.role || 'user')}
                </Space>
              </div>
            </div>
            
            <Descriptions column={2} bordered>
              <Descriptions.Item label="用户ID">{user.id}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{user.email || '-'}</Descriptions.Item>
              <Descriptions.Item label="手机号">{user.phone || '-'}</Descriptions.Item>
              <Descriptions.Item label="注册时间">
                {user.created_at ? new Date(user.created_at).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="最后活跃">
                {user.last_active ? new Date(user.last_active).toLocaleString() : '从未活跃'}
              </Descriptions.Item>
              <Descriptions.Item label="个人简介" span={2}>
                暂无简介
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 统计信息卡片 */}
        <Col xs={24} lg={8}>
          <Card title="用户统计" style={{ marginBottom: 24 }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="等级"
                  value={user.level || 0}
                  valueStyle={{ color: getLevelColor(user.level || 0) }}
                  prefix={<TrophyOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="积分"
                  value={user.flow_value || 0}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<FireOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="连续签到"
                  value={user.streak || 0}
                  suffix="天"
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CalendarOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="发帖数量"
                  value={user.posts_count || 0}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<MessageOutlined />}
                />
              </Col>
              <Col span={24}>
                <Statistic
                  title="总签到次数"
                  value={user.checkins_count || 0}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 详细信息标签页 */}
      <Card>
        <Tabs defaultActiveKey="activity">
          <TabPane tab="活动记录" key="activity">
            <Timeline>
              <Timeline.Item color="green">
                用户注册 - {user.created_at ? new Date(user.created_at).toLocaleString() : '未知时间'}
              </Timeline.Item>
              {user.last_active && (
                <Timeline.Item color="blue">
                  最后活跃 - {new Date(user.last_active).toLocaleString()}
                </Timeline.Item>
              )}
              <Timeline.Item color="gray">
                更多活动记录开发中...
              </Timeline.Item>
            </Timeline>
          </TabPane>
          
          <TabPane tab="发布内容" key="posts">
            <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
              用户发布内容列表功能开发中...
            </div>
          </TabPane>
          
          <TabPane tab="操作日志" key="logs">
            <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
              用户操作日志功能开发中...
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default UserDetail