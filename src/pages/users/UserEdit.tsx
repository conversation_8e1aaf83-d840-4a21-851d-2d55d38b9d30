import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Select,
  Switch,
  InputNumber,
  Upload,
  Avatar,
  Spin,
  Alert,
  Row,
  Col,
  Divider,
  message
} from 'antd'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  UserOutlined,
  UploadOutlined
} from '@ant-design/icons'
import { userService, type User } from '../../services/userService'
import RichTextEditor from '../../components/RichTextEditor'

const { Option } = Select
const { TextArea } = Input

interface UserEditProps {
  userId?: string
  mode?: 'edit' | 'create'
}

const UserEdit: React.FC<UserEditProps> = ({ userId: propUserId, mode = 'edit' }) => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const userId = propUserId || id
  const isCreateMode = mode === 'create' || !userId
  
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [error, setError] = useState<Error | null>(null)
  const [avatarUrl, setAvatarUrl] = useState<string>('')

  // 加载用户详情（编辑模式）
  const loadUserDetail = async () => {
    if (isCreateMode || !userId) return
    
    setLoading(true)
    setError(null)
    try {
      const result = await userService.getUserById(userId)
      if (result.success && result.data) {
        const data = result.data
        setUser(data)
        setAvatarUrl(data.avatar_url || '')
        // 设置表单初始值
        form.setFieldsValue({
          username: data.username,
          email: data.email,
          phone: data.phone,
          bio: data.bio,
          level: data.level,
          points: data.points,
          status: data.status,
          role: data.role,
          is_premium: data.is_premium,
          streak_days: data.streak_days,
          posts_count: data.posts_count,
          check_in_count: data.check_in_count
        })
      } else {
        setError(new Error('用户不存在'))
      }
    } catch (err) {
      setError(err as Error)
      message.error('加载用户详情失败')
    } finally {
      setLoading(false)
    }
  }

  // 保存用户信息
  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      setSaving(true)

      // 添加头像URL到表单数据
      const formData = {
        ...values,
        avatar_url: avatarUrl
      }

      if (isCreateMode) {
        // 创建新用户的逻辑（需要扩展userService）
        message.info('创建用户功能开发中...')
        return
      } else {
        // 更新用户
        const result = await userService.updateUser(userId!, formData)
        
        if (result.success) {
          message.success('用户信息已更新')
          navigate(`/users/${userId}`)
        } else {
          throw new Error('更新失败')
        }
      }
    } catch (err) {
      message.error(isCreateMode ? '创建用户失败' : '更新用户失败')
      console.error(err)
    } finally {
      setSaving(false)
    }
  }

  // 处理头像上传
  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'done') {
      // 这里应该处理实际的文件上传逻辑
      message.success('头像上传成功')
      setAvatarUrl(info.file.response?.url || '')
    } else if (info.file.status === 'error') {
      message.error('头像上传失败')
    }
  }

  useEffect(() => {
    loadUserDetail()
  }, [userId])

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error && !isCreateMode) {
    return (
      <Alert
        message="加载失败"
        description={error?.message || '用户不存在'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => navigate('/users')}>
            返回列表
          </Button>
        }
      />
    )
  }

  return (
    <div>
      {/* 头部操作栏 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate(isCreateMode ? '/users' : `/users/${userId}`)}
          >
            {isCreateMode ? '返回列表' : '返回详情'}
          </Button>
          <Button 
            type="primary" 
            icon={<SaveOutlined />}
            loading={saving}
            onClick={handleSave}
          >
            {isCreateMode ? '创建用户' : '保存更改'}
          </Button>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card title={isCreateMode ? '创建用户' : '编辑用户'}>
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                status: 'active',
                role: 'user',
                is_premium: false,
                level: 0,
                points: 0,
                streak_days: 0,
                posts_count: 0,
                check_in_count: 0
              }}
            >
              {/* 基本信息 */}
              <Divider orientation="left">基本信息</Divider>
              
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="用户名"
                    name="username"
                    rules={[
                      { required: true, message: '请输入用户名' },
                      { min: 2, max: 20, message: '用户名长度为2-20个字符' }
                    ]}
                  >
                    <Input placeholder="请输入用户名" />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="邮箱"
                    name="email"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="请输入邮箱" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="手机号"
                    name="phone"
                    rules={[
                      { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                    ]}
                  >
                    <Input placeholder="请输入手机号" />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="状态"
                    name="status"
                    rules={[{ required: true, message: '请选择用户状态' }]}
                  >
                    <Select placeholder="请选择用户状态">
                      <Option value="active">正常</Option>
                      <Option value="inactive">未激活</Option>
                      <Option value="banned">已封禁</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="个人简介"
                name="bio"
              >
                <RichTextEditor 
                  placeholder="请输入个人简介" 
                  style={{ minHeight: '100px' }}
                />
              </Form.Item>

              {/* 权限设置 */}
              <Divider orientation="left">权限设置</Divider>
              
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="用户角色"
                    name="role"
                    rules={[{ required: true, message: '请选择用户角色' }]}
                  >
                    <Select placeholder="请选择用户角色">
                      <Option value="user">普通用户</Option>
                      <Option value="ADMIN">管理员</Option>
                      <Option value="SUPER_ADMIN">超级管理员</Option>
                    </Select>
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="会员状态"
                    name="is_premium"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="会员" unCheckedChildren="普通" />
                  </Form.Item>
                </Col>
              </Row>

              {/* 游戏数据 */}
              <Divider orientation="left">游戏数据</Divider>
              
              <Row gutter={16}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="等级"
                    name="level"
                    rules={[{ type: 'number', min: 0, max: 100, message: '等级范围为0-100' }]}
                  >
                    <InputNumber 
                      min={0} 
                      max={100} 
                      style={{ width: '100%' }}
                      placeholder="用户等级"
                    />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="积分"
                    name="points"
                    rules={[{ type: 'number', min: 0, message: '积分不能为负数' }]}
                  >
                    <InputNumber 
                      min={0} 
                      style={{ width: '100%' }}
                      placeholder="用户积分"
                    />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="连续签到天数"
                    name="streak_days"
                    rules={[{ type: 'number', min: 0, message: '签到天数不能为负数' }]}
                  >
                    <InputNumber 
                      min={0} 
                      style={{ width: '100%' }}
                      placeholder="连续签到天数"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="发帖数量"
                    name="posts_count"
                    rules={[{ type: 'number', min: 0, message: '发帖数量不能为负数' }]}
                  >
                    <InputNumber 
                      min={0} 
                      style={{ width: '100%' }}
                      placeholder="发帖数量"
                    />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="签到次数"
                    name="check_in_count"
                    rules={[{ type: 'number', min: 0, message: '签到次数不能为负数' }]}
                  >
                    <InputNumber 
                      min={0} 
                      style={{ width: '100%' }}
                      placeholder="总签到次数"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Card>
        </Col>

        {/* 头像上传 */}
        <Col xs={24} lg={8}>
          <Card title="头像设置">
            <div style={{ textAlign: 'center' }}>
              <Avatar 
                size={120} 
                src={avatarUrl} 
                icon={<UserOutlined />}
                style={{ marginBottom: 16 }}
              />
              <br />
              <Upload
                name="avatar"
                listType="text"
                showUploadList={false}
                action="/api/upload" // 需要实现文件上传接口
                onChange={handleAvatarChange}
              >
                <Button icon={<UploadOutlined />}>
                  上传头像
                </Button>
              </Upload>
              <div style={{ marginTop: 8, color: '#999', fontSize: '12px' }}>
                支持 JPG、PNG 格式，文件大小不超过 2MB
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default UserEdit