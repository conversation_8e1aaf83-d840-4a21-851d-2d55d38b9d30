import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Statistic,
  Row,
  Col,
  message,
  Tabs,
  InputNumber,
  Switch,
  Avatar,
  Tooltip,
  Popconfirm,
  Badge,
  Progress
} from 'antd'
import {
  TeamOutlined,
  UserOutlined,
  DollarOutlined,
  TrophyOutlined,
  SettingOutlined,
  ReloadOutlined,
  SearchOutlined,
  ExportOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined
} from '@ant-design/icons'
import EChartsComponent from '../../components/EChartsComponent'
import { subscriptionPaymentService } from '../../services/subscriptionPaymentService'
import type { PartnerApplication } from '../../services/subscriptionPaymentService'

// 定义佣金记录类型
interface PartnerCommission {
  id: string
  partner_id: string
  partner: {
    id: string
    username: string
    avatar?: string
  }
  order: {
    order_id: string
    amount: number
  }
  commission_rate: number
  commission_amount: number
  status: 'pending' | 'paid' | 'cancelled'
  settled_at?: string
  created_at: string
  notes?: string
}
import { createLineChartOption, createPieChartOption, createBarChartOption } from '../../utils/chartOptions'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { TabPane } = Tabs
const { Option } = Select
const { TextArea } = Input

const PartnerManagement: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [applications, setApplications] = useState<PartnerApplication[]>([])
  const [commissions, setCommissions] = useState<PartnerCommission[]>([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [filters, setFilters] = useState<any>({})
  const [applicationModalVisible, setApplicationModalVisible] = useState(false)
  const [commissionModalVisible, setCommissionModalVisible] = useState(false)
  const [selectedApplication, setSelectedApplication] = useState<PartnerApplication | null>(null)
  const [selectedCommission, setSelectedCommission] = useState<PartnerCommission | null>(null)
  const [activeTab, setActiveTab] = useState('applications')
  const [stats, setStats] = useState<any>(null)

  const [form] = Form.useForm()
  const [applicationForm] = Form.useForm()
  const [commissionForm] = Form.useForm()

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      if (activeTab === 'applications') {
        const response = await subscriptionPaymentService.getPartnerApplications({
          page: currentPage,
          pageSize,
          ...filters
        })
        setApplications(response.data)
        setTotal(response.total)
      } else if (activeTab === 'commissions') {
        const response = await subscriptionPaymentService.getPartnerApplications({
          page: currentPage,
          pageSize,
          ...filters
        })
        setCommissions(response.data)
        setTotal(response.total)
      } else if (activeTab === 'statistics') {
        const statsData = await subscriptionPaymentService.getPartners()
        setStats(statsData)
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [currentPage, pageSize, filters, activeTab])

  // 搜索处理
  const handleSearch = (values: any) => {
    const newFilters = { ...values }
    if (values.dateRange) {
      newFilters.start_date = values.dateRange[0].format('YYYY-MM-DD')
      newFilters.end_date = values.dateRange[1].format('YYYY-MM-DD')
      delete newFilters.dateRange
    }
    setFilters(newFilters)
    setCurrentPage(1)
  }

  // 重置搜索
  const handleReset = () => {
    form.resetFields()
    setFilters({})
    setCurrentPage(1)
  }

  // 审核合伙人申请
  const handleReviewApplication = async (id: string, status: 'approved' | 'rejected' | 'suspended', reason?: string) => {
    try {
      await subscriptionPaymentService.updatePartnerApplication(id, { status, rejection_reason: reason })
      message.success('审核完成')
      loadData()
    } catch (error) {
      console.error('审核失败:', error)
      message.error('审核失败')
    }
  }

  // 更新佣金设置
  const handleUpdateCommission = async (values: any) => {
    if (!selectedCommission) return
    
    try {
      await subscriptionPaymentService.updatePartnerApplication(selectedCommission.id, values)
      message.success('佣金设置更新成功')
      setCommissionModalVisible(false)
      setSelectedCommission(null)
      commissionForm.resetFields()
      loadData()
    } catch (error) {
      console.error('更新佣金设置失败:', error)
      message.error('更新佣金设置失败')
    }
  }

  // 合伙人申请表格列定义
  const applicationColumns = [
    {
      title: '申请人',
      dataIndex: 'user',
      key: 'user',
      render: (user: any) => (
        <Space>
          <Avatar src={user?.avatar} size="small">
            {user?.username?.[0]}
          </Avatar>
          <div>
            <div>{user?.username || '未知用户'}</div>
            <div style={{ fontSize: '12px', color: '#999' }}>ID: {user?.id}</div>
          </div>
        </Space>
      )
    },
    {
      title: '申请类型',
      dataIndex: 'application_type',
      key: 'application_type',
      render: (type: string) => {
        const typeMap = {
          individual: { text: '个人', color: 'blue' },
          company: { text: '企业', color: 'green' },
          influencer: { text: '网红', color: 'purple' }
        }
        const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '推广渠道',
      dataIndex: 'promotion_channels',
      key: 'promotion_channels',
      render: (record: PartnerApplication) => (
        <div>
          <Tag>线上推广</Tag>
          <Tag>社交媒体</Tag>
        </div>
      )
    },
    {
      title: '预期月推广量',
      dataIndex: 'expected_monthly_sales',
      key: 'expected_monthly_sales',
      render: (record: PartnerApplication) => record.user?.username ? `预计推广` : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '待审核', color: 'orange' },
          approved: { text: '已通过', color: 'green' },
          rejected: { text: '已拒绝', color: 'red' },
          suspended: { text: '已暂停', color: 'gray' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '申请时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: PartnerApplication) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedApplication(record)
              applicationForm.setFieldsValue(record)
              setApplicationModalVisible(true)
            }}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <>
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleReviewApplication(record.id, 'approved')}
              >
                通过
              </Button>
              <Button
                type="link"
                size="small"
                danger
                icon={<CloseOutlined />}
                onClick={() => {
                  Modal.confirm({
                    title: '拒绝申请',
                    content: (
                      <Input.TextArea
                        placeholder="请输入拒绝原因"
                        onChange={(e) => {
                          const reason = e.target.value
                          Modal.destroyAll()
                          if (reason.trim()) {
                            handleReviewApplication(record.id, 'rejected', reason)
                          }
                        }}
                      />
                    ),
                    okText: '确认拒绝',
                    cancelText: '取消'
                  })
                }}
              >
                拒绝
              </Button>
            </>
          )}
          {record.status === 'approved' && (
            <Button
              type="link"
              size="small"
              danger
              onClick={() => handleReviewApplication(record.id, 'suspended')}
            >
              暂停
            </Button>
          )}
        </Space>
      )
    }
  ]

  // 佣金记录表格列定义
  const commissionColumns = [
    {
      title: '合伙人',
      dataIndex: 'partner',
      key: 'partner',
      render: (partner: any) => (
        <Space>
          <Avatar src={partner?.avatar} size="small">
            {partner?.username?.[0]}
          </Avatar>
          <div>
            <div>{partner?.username || '未知用户'}</div>
            <div style={{ fontSize: '12px', color: '#999' }}>ID: {partner?.id}</div>
          </div>
        </Space>
      )
    },
    {
      title: '订单信息',
      dataIndex: 'order',
      key: 'order',
      render: (order: any) => (
        <div>
          <div>订单号: {order?.order_id}</div>
          <div style={{ fontSize: '12px', color: '#999' }}>金额: ¥{order?.amount}</div>
        </div>
      )
    },
    {
      title: '佣金比例',
      dataIndex: 'commission_rate',
      key: 'commission_rate',
      render: (rate: number) => (
        <Progress
          percent={rate * 100}
          size="small"
          format={(percent) => `${percent}%`}
          strokeColor="#52c41a"
        />
      )
    },
    {
      title: '佣金金额',
      dataIndex: 'commission_amount',
      key: 'commission_amount',
      render: (amount: number) => (
        <span style={{ color: '#f50', fontWeight: 'bold' }}>¥{amount.toFixed(2)}</span>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '待结算', color: 'orange' },
          paid: { text: '已支付', color: 'green' },
          cancelled: { text: '已取消', color: 'red' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '结算时间',
      dataIndex: 'settled_at',
      key: 'settled_at',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: PartnerCommission) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setSelectedCommission(record)
              commissionForm.setFieldsValue(record)
              setCommissionModalVisible(true)
            }}
          >
            编辑
          </Button>
          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              onClick={() => handleReviewApplication(record.id, 'paid')}
            >
              标记已付
            </Button>
          )}
        </Space>
      )
    }
  ]

  // 统计卡片数据
  const getStatsCards = () => {
    if (!stats) return []
    
    return [
      {
        title: '合伙人总数',
        value: stats.total_partners,
        icon: <TeamOutlined style={{ color: '#1890ff' }} />
      },
      {
        title: '活跃合伙人',
        value: stats.active_partners,
        icon: <UserOutlined style={{ color: '#52c41a' }} />
      },
      {
        title: '本月佣金',
        value: stats.monthly_commission,
        prefix: '¥',
        precision: 2,
        icon: <DollarOutlined style={{ color: '#f50' }} />
      },
      {
        title: '平均转化率',
        value: stats.avg_conversion_rate,
        suffix: '%',
        precision: 1,
        icon: <TrophyOutlined style={{ color: '#722ed1' }} />
      }
    ]
  }

  // 图表配置
  const getCommissionTrendChartOption = () => {
    if (!stats?.commission_trend) return {}
    
    const dates = stats.commission_trend.map((item: any) => item.date)
    const commissions = stats.commission_trend.map((item: any) => item.commission)
    
    return createLineChartOption(
      '佣金趋势',
      dates,
      [{ name: '佣金', data: commissions }],
      {}
    )
  }

  const getPartnerPerformanceChartOption = () => {
    if (!stats?.partner_performance) return {}
    
    const names = stats.partner_performance.slice(0, 10).map((item: any) => item.partner_name)
    const commissions = stats.partner_performance.slice(0, 10).map((item: any) => item.total_commission)
    
    return createBarChartOption(
      '合伙人业绩排行',
      names,
      [{ name: '佣金收入', data: commissions }],
      {}
    )
  }

  const getChannelDistributionChartOption = () => {
    if (!stats?.channel_distribution) return {}
    
    const data = stats.channel_distribution.map((item: any) => ({
      name: item.channel,
      value: item.count
    }))
    
    return createPieChartOption('推广渠道分布', data, {})
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card title="合伙人管理" extra={
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadData}>
            刷新
          </Button>
          <Button icon={<ExportOutlined />}>
            导出
          </Button>
        </Space>
      }>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="合伙人申请" key="applications">
            {/* 搜索表单 */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Form
                form={form}
                layout="inline"
                onFinish={handleSearch}
              >
                <Form.Item name="status" label="状态">
                  <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
                    <Option value="pending">待审核</Option>
                    <Option value="approved">已通过</Option>
                    <Option value="rejected">已拒绝</Option>
                    <Option value="suspended">已暂停</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="application_type" label="申请类型">
                  <Select placeholder="选择类型" allowClear style={{ width: 120 }}>
                    <Option value="individual">个人</Option>
                    <Option value="company">企业</Option>
                    <Option value="influencer">网红</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="dateRange" label="时间范围">
                  <RangePicker />
                </Form.Item>
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      搜索
                    </Button>
                    <Button onClick={handleReset}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>

            {/* 数据表格 */}
            <Table
              columns={applicationColumns}
              dataSource={applications}
              rowKey="id"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize,
                total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: (page, size) => {
                  setCurrentPage(page)
                  setPageSize(size || 20)
                }
              }}
            />
          </TabPane>

          <TabPane tab="佣金记录" key="commissions">
            {/* 搜索表单 */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Form
                form={form}
                layout="inline"
                onFinish={handleSearch}
              >
                <Form.Item name="status" label="状态">
                  <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
                    <Option value="pending">待结算</Option>
                    <Option value="paid">已支付</Option>
                    <Option value="cancelled">已取消</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="dateRange" label="时间范围">
                  <RangePicker />
                </Form.Item>
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      搜索
                    </Button>
                    <Button onClick={handleReset}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>

            {/* 数据表格 */}
            <Table
              columns={commissionColumns}
              dataSource={commissions}
              rowKey="id"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize,
                total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: (page, size) => {
                  setCurrentPage(page)
                  setPageSize(size || 20)
                }
              }}
            />
          </TabPane>

          <TabPane tab="统计分析" key="statistics">
            {stats && (
              <>
                {/* 统计卡片 */}
                <Row gutter={16} style={{ marginBottom: 24 }}>
                  {getStatsCards().map((card, index) => (
                    <Col span={6} key={index}>
                      <Card>
                        <Statistic
                          title={card.title}
                          value={card.value}
                          prefix={card.prefix}
                          suffix={card.suffix}
                          precision={card.precision}
                        />
                        <div style={{ marginTop: 8 }}>{card.icon}</div>
                      </Card>
                    </Col>
                  ))}
                </Row>

                {/* 图表 */}
                <Row gutter={16} style={{ marginBottom: 24 }}>
                  <Col span={12}>
                    <Card title="佣金趋势">
                      <EChartsComponent
                        option={getCommissionTrendChartOption()}
                        height={300}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="推广渠道分布">
                      <EChartsComponent
                        option={getChannelDistributionChartOption()}
                        height={300}
                      />
                    </Card>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={24}>
                    <Card title="合伙人业绩排行">
                      <EChartsComponent
                        option={getPartnerPerformanceChartOption()}
                        height={400}
                      />
                    </Card>
                  </Col>
                </Row>
              </>
            )}
          </TabPane>
        </Tabs>
      </Card>

      {/* 申请详情模态框 */}
      <Modal
        title="合伙人申请详情"
        open={applicationModalVisible}
        onCancel={() => {
          setApplicationModalVisible(false)
          setSelectedApplication(null)
          applicationForm.resetFields()
        }}
        footer={null}
        width={800}
      >
        {selectedApplication && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Card size="small" title="基本信息">
                  <p><strong>申请人:</strong> {selectedApplication.user?.username}</p>
                  <p><strong>用户ID:</strong> {selectedApplication.user?.id}</p>
                  <p><strong>申请类型:</strong> {selectedApplication.application_type}</p>
                  <p><strong>申请状态:</strong> {selectedApplication.status}</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="推广信息">
                  <p><strong>推广渠道:</strong></p>
                  <div>
                    <Tag>线上推广</Tag>
                    <Tag>社交媒体</Tag>
                  </div>
                  <p style={{ marginTop: 8 }}><strong>推广经验:</strong></p>
                  <p>具有丰富的线上推广经验</p>
                </Card>
              </Col>
            </Row>
            
            <Card size="small" title="申请说明" style={{ marginTop: 16 }}>
              <p>希望成为合伙人，共同推广产品</p>
            </Card>
            
            {selectedApplication.status === 'rejected' && (
              <Card size="small" title="拒绝原因" style={{ marginTop: 16 }}>
                <p style={{ color: '#f50' }}>申请材料不完整</p>
              </Card>
            )}
          </div>
        )}
      </Modal>

      {/* 佣金编辑模态框 */}
      <Modal
        title="编辑佣金记录"
        open={commissionModalVisible}
        onCancel={() => {
          setCommissionModalVisible(false)
          setSelectedCommission(null)
          commissionForm.resetFields()
        }}
        footer={null}
      >
        <Form
          form={commissionForm}
          layout="vertical"
          onFinish={handleUpdateCommission}
        >
          <Form.Item
            name="commission_rate"
            label="佣金比例"
            rules={[{ required: true, message: '请输入佣金比例' }]}
          >
            <InputNumber
              min={0}
              max={1}
              step={0.01}
              precision={2}
              formatter={(value) => `${(Number(value) * 100).toFixed(1)}%`}
              parser={(value: any) => Number(value!.replace('%', '')) / 100}
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value="pending">待结算</Option>
              <Option value="paid">已支付</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea
              rows={3}
              placeholder="请输入备注"
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                更新
              </Button>
              <Button onClick={() => {
                setCommissionModalVisible(false)
                setSelectedCommission(null)
                commissionForm.resetFields()
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default PartnerManagement