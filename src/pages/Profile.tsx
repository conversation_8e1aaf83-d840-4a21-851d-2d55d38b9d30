import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Avatar, 
  Descriptions, 
  Statistic, 
  Button, 
  Space, 
  Tag, 
  Timeline, 
  Tabs, 
  Form, 
  Input, 
  Select, 
  message,
  Spin,
  Alert
} from 'antd'
import { 
  UserOutlined, 
  EditOutlined, 
  TrophyOutlined, 
  FireOutlined, 
  CalendarOutlined, 
  MessageOutlined,
  CrownOutlined,
  SettingOutlined,
  SafetyOutlined
} from '@ant-design/icons'
import { useUserStore } from '../store/userStore'
import { userService } from '../services/userService'

const { TabPane } = Tabs

const Profile: React.FC = () => {
  const { user, profile, refreshUser } = useUserStore()
  const [loading, setLoading] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    if (user) {
      refreshUser()
    }
  }, [user, refreshUser])

  const handleEdit = () => {
    if (profile) {
      form.setFieldsValue({
        username: profile.username,
        bio: '', // 需要从数据库获取
        gender: '', // 需要从数据库获取
        age: '', // 需要从数据库获取
        education: '', // 需要从数据库获取
        province: '' // 需要从数据库获取
      })
    }
    setEditMode(true)
  }

  const handleSave = async () => {
    try {
      setLoading(true)
      const values = await form.validateFields()
      
      // 更新用户资料
      const result = await userService.updateUser(user?.id || '', values)
      
      if (result.success) {
        message.success('个人资料更新成功')
        setEditMode(false)
        refreshUser()
      } else {
        message.error(typeof result.error === 'string' ? result.error : '更新失败')
      }
    } catch (error) {
      message.error('请检查输入信息')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setEditMode(false)
    form.resetFields()
  }

  const getRoleTag = (role: string) => {
    const roleMap: Record<string, { color: string; text: string }> = {
      'SUPER_ADMIN': { color: 'red', text: '超级管理员' },
      'ADMIN': { color: 'orange', text: '管理员' },
      'PARTNER': { color: 'purple', text: '合伙人' },
      'MEMBER': { color: 'blue', text: '会员' },
      'USER': { color: 'default', text: '普通用户' }
    }
    const roleInfo = roleMap[role] || roleMap['USER']
    return <Tag color={roleInfo.color}>{roleInfo.text}</Tag>
  }

  const getMembershipTag = (membershipType: string) => {
    const membershipMap: Record<string, { color: string; text: string }> = {
      'PARTNER': { color: 'purple', text: '合伙人' },
      'MEMBER': { color: 'gold', text: '年度会员' },
      'USER': { color: 'default', text: '普通用户' }
    }
    const membershipInfo = membershipMap[membershipType] || membershipMap['USER']
    return <Tag color={membershipInfo.color}>{membershipInfo.text}</Tag>
  }

  if (!user || !profile) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载个人信息中...</div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[24, 24]}>
        {/* 基本信息卡片 */}
        <Col xs={24} lg={16}>
          <Card 
            title="个人资料" 
            extra={
              !editMode ? (
                <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
                  编辑资料
                </Button>
              ) : (
                <Space>
                  <Button onClick={handleCancel}>取消</Button>
                  <Button type="primary" loading={loading} onClick={handleSave}>
                    保存
                  </Button>
                </Space>
              )
            }
          >
            {!editMode ? (
              <>
                <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: 24 }}>
                  <Avatar 
                    size={80} 
                    src={profile.avatar_url}
                    icon={<UserOutlined />}
                    style={{ marginRight: 24 }}
                  />
                  <div style={{ flex: 1 }}>
                    <h2 style={{ margin: 0, marginBottom: 8 }}>
                      {profile.username || '未设置用户名'}
                      {profile.is_premium && (
                        <Tag color="gold" style={{ marginLeft: 8 }}>
                          <CrownOutlined /> 会员
                        </Tag>
                      )}
                    </h2>
                    <Space size="middle">
                      {getRoleTag(profile.role)}
                      {getMembershipTag(profile.membership_type || 'USER')}
                    </Space>
                  </div>
                </div>
                
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="用户ID">{profile.id}</Descriptions.Item>
                  <Descriptions.Item label="邮箱">{user.email || '-'}</Descriptions.Item>
                  <Descriptions.Item label="用户名">{profile.username || '-'}</Descriptions.Item>
                  <Descriptions.Item label="注册时间">
                    {profile.created_at ? new Date(profile.created_at).toLocaleString() : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="最后更新">
                    {profile.updated_at ? new Date(profile.updated_at).toLocaleString() : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="个人简介" span={2}>
                    暂无简介
                  </Descriptions.Item>
                </Descriptions>
              </>
            ) : (
              <Form form={form} layout="vertical">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item 
                      name="username" 
                      label="用户名"
                      rules={[{ required: true, message: '请输入用户名' }]}
                    >
                      <Input placeholder="请输入用户名" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name="gender" label="性别">
                      <Select placeholder="请选择性别">
                        <Select.Option value="male">男</Select.Option>
                        <Select.Option value="female">女</Select.Option>
                        <Select.Option value="other">其他</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item 
                      name="age" 
                      label="年龄"
                      rules={[
                        { pattern: /^[1-9]\d*$/, message: '请输入有效的年龄' },
                        { validator: (_, value) => {
                          if (!value) return Promise.resolve();
                          const age = parseInt(value);
                          if (age < 1 || age > 120) {
                            return Promise.reject(new Error('年龄必须在1-120之间'));
                          }
                          return Promise.resolve();
                        }}
                      ]}
                    >
                      <Input type="number" placeholder="请输入年龄" min={1} max={120} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name="education" label="教育程度">
                      <Select placeholder="请选择教育程度">
                        <Select.Option value="primary">小学</Select.Option>
                        <Select.Option value="middle">初中</Select.Option>
                        <Select.Option value="high">高中</Select.Option>
                        <Select.Option value="college">大专</Select.Option>
                        <Select.Option value="bachelor">本科</Select.Option>
                        <Select.Option value="master">硕士</Select.Option>
                        <Select.Option value="doctor">博士</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item 
                  name="bio" 
                  label="个人简介"
                  rules={[
                    { max: 500, message: '个人简介不能超过500个字符' }
                  ]}
                >
                  <Input.TextArea 
                    rows={4} 
                    placeholder="请输入个人简介" 
                    maxLength={500}
                    showCount
                  />
                </Form.Item>
              </Form>
            )}
          </Card>
        </Col>

        {/* 统计信息卡片 */}
        <Col xs={24} lg={8}>
          <Card title="我的统计" style={{ marginBottom: 24 }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="等级"
                  value={profile.level || 0}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<TrophyOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="心流值"
                  value={profile.flow_value || 0}
                  valueStyle={{ color: '#ff4d4f' }}
                  prefix={<FireOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="连续天数"
                  value={profile.streak || 0}
                  suffix="天"
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CalendarOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="发帖数量"
                  value={0} // 需要从数据库获取
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<MessageOutlined />}
                />
              </Col>
            </Row>
          </Card>

          {/* 会员信息 */}
          {profile.is_premium && (
            <Card title="会员信息" style={{ marginBottom: 24 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <span>会员类型: </span>
                  {getMembershipTag(profile.membership_type || 'USER')}
                </div>
                <div>
                  <span>到期时间: </span>
                  <span>永久有效</span> {/* 需要从数据库获取实际到期时间 */}
                </div>
              </Space>
            </Card>
          )}
        </Col>
      </Row>

      {/* 详细信息标签页 */}
      <Card style={{ marginTop: 24 }}>
        <Tabs defaultActiveKey="activity">
          <TabPane tab={<Space><CalendarOutlined />活动记录</Space>} key="activity">
            <Timeline>
              <Timeline.Item color="green">
                用户注册 - {profile.created_at ? new Date(profile.created_at).toLocaleString() : '未知时间'}
              </Timeline.Item>
              <Timeline.Item color="blue">
                资料更新 - {profile.updated_at ? new Date(profile.updated_at).toLocaleString() : '未更新'}
              </Timeline.Item>
              <Timeline.Item color="gray">
                更多活动记录开发中...
              </Timeline.Item>
            </Timeline>
          </TabPane>
          
          <TabPane tab={<Space><MessageOutlined />我的内容</Space>} key="posts">
            <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
              我的发布内容列表功能开发中...
            </div>
          </TabPane>
          
          <TabPane tab={<Space><SettingOutlined />账户设置</Space>} key="settings">
            <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
              账户设置功能开发中...
            </div>
          </TabPane>

          <TabPane tab={<Space><SafetyOutlined />安全中心</Space>} key="security">
            <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
              安全中心功能开发中...
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default Profile