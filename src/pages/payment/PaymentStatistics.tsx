import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Statistic,
  Row,
  Col,
  message,
  Tabs,
  DatePicker,
  Select,
  Input
} from 'antd'
import {
  DollarOutlined,
  CreditCardOutlined,
  BankOutlined,
  WalletOutlined,
  RiseOutlined,
  ReloadOutlined,
  SearchOutlined,
  ExportOutlined
} from '@ant-design/icons'
import EChartsComponent from '../../components/EChartsComponent'
import { subscriptionPaymentService } from '../../services/subscriptionPaymentService'
import type { PaymentRecord, PaymentStats } from '../../services/subscriptionPaymentService'
import { createLineChartOption, createPieChartOption } from '../../utils/chartOptions'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { TabPane } = Tabs
const { Option } = Select

const PaymentStatistics: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [filters, setFilters] = useState<any>({})
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState<PaymentStats | null>(null)

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      if (activeTab === 'overview' || activeTab === 'statistics') {
        const statsData = await subscriptionPaymentService.getPaymentStats()
        setStats(statsData)
      } else if (activeTab === 'payments') {
        const response = await subscriptionPaymentService.getPaymentRecords({
          page: currentPage,
          pageSize,
          ...filters
        })
        setPayments(response.data)
        setTotal(response.total)
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [activeTab, currentPage, pageSize, filters])

  // 支付状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'orange', text: '待支付' },
      completed: { color: 'green', text: '已完成' },
      failed: { color: 'red', text: '失败' },
      refunded: { color: 'purple', text: '已退款' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 支付方式标签
  const getPaymentMethodTag = (method: string) => {
    const methodMap = {
      wechat: { color: 'green', text: '微信支付' },
      alipay: { color: 'blue', text: '支付宝' },
      balance: { color: 'orange', text: '余额支付' }
    }
    const config = methodMap[method as keyof typeof methodMap] || { color: 'default', text: method }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 支付记录表格列
  const paymentColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (text: string) => text.slice(0, 8) + '...'
    },
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user: any) => user?.username || '未知用户'
    },
    {
      title: '订单类型',
      dataIndex: 'order_type',
      key: 'order_type',
      render: (type: string) => {
        const typeMap = {
          subscription: '订阅',
          appreciation: '赞赏',
          partner_commission: '合伙人佣金'
        }
        return typeMap[type as keyof typeof typeMap] || type
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `¥${amount.toFixed(2)}`
    },
    {
      title: '支付方式',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: getPaymentMethodTag
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: getStatusTag
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    }
  ]

  // 统计概览
  const renderOverview = () => {
    if (!stats) return null

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总收入"
                value={stats.total_revenue}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#3f8600' }}
                suffix={<RiseOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日收入"
                value={stats.today_revenue}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="本月收入"
                value={stats.month_revenue}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="退款金额"
                value={stats.refund_amount}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title="收入趋势" extra={<Button icon={<ReloadOutlined />} onClick={loadData} />}>
              {stats.revenue_trends && stats.revenue_trends.length > 0 ? (
                 <EChartsComponent
                   option={createLineChartOption({
                     xAxisData: stats.revenue_trends.map(item => item.date),
                     series: [{
                       name: '收入',
                       data: stats.revenue_trends.map(item => item.amount)
                     }],
                     title: '收入趋势'
                   })}
                   height={300}
                 />
              ) : (
                <div style={{ textAlign: 'center', padding: '50px 0' }}>暂无数据</div>
              )}
            </Card>
          </Col>
          <Col span={12}>
            <Card title="支付方式分布">
              {stats.payment_method_stats && stats.payment_method_stats.length > 0 ? (
                 <EChartsComponent
                   option={createPieChartOption({
                     data: stats.payment_method_stats.map(item => ({
                       name: item.method,
                       value: item.amount
                     })),
                     title: '支付方式分布'
                   })}
                   height={300}
                 />
              ) : (
                <div style={{ textAlign: 'center', padding: '50px 0' }}>暂无数据</div>
              )}
            </Card>
          </Col>
        </Row>
      </div>
    )
  }

  // 支付记录
  const renderPayments = () => (
    <Card
      title="支付记录"
      extra={
        <Space>
          <Button icon={<SearchOutlined />} onClick={() => setFilters({})}>重置筛选</Button>
          <Button icon={<ExportOutlined />}>导出数据</Button>
          <Button icon={<ReloadOutlined />} onClick={loadData}>刷新</Button>
        </Space>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Select
            placeholder="支付状态"
            style={{ width: 120 }}
            allowClear
            onChange={(value) => setFilters({ ...filters, status: value })}
          >
            <Option value="pending">待支付</Option>
            <Option value="completed">已完成</Option>
            <Option value="failed">失败</Option>
            <Option value="refunded">已退款</Option>
          </Select>
          <Select
            placeholder="支付方式"
            style={{ width: 120 }}
            allowClear
            onChange={(value) => setFilters({ ...filters, payment_method: value })}
          >
            <Option value="wechat">微信支付</Option>
            <Option value="alipay">支付宝</Option>
            <Option value="balance">余额支付</Option>
          </Select>
          <RangePicker
            onChange={(dates) => {
              if (dates) {
                setFilters({
                  ...filters,
                  start_date: dates[0]?.format('YYYY-MM-DD'),
                  end_date: dates[1]?.format('YYYY-MM-DD')
                })
              } else {
                const { start_date, end_date, ...rest } = filters
                setFilters(rest)
              }
            }}
          />
        </Space>
      </div>
      <Table
        columns={paymentColumns}
        dataSource={payments}
        rowKey="id"
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize,
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page, size) => {
            setCurrentPage(page)
            setPageSize(size || 20)
          }
        }}
      />
    </Card>
  )

  return (
    <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="统计概览" key="overview">
          {renderOverview()}
        </TabPane>
        <TabPane tab="支付记录" key="payments">
          {renderPayments()}
        </TabPane>
      </Tabs>
    </div>
  )
}

export default PaymentStatistics