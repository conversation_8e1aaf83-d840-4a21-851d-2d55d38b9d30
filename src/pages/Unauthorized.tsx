import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Button } from 'antd'
import { useUserStore } from '../store/userStore'

/**
 * 无权限访问页面
 */
export const Unauthorized: React.FC = () => {
  const { user, role } = useUserStore()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-8 h-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            访问被拒绝
          </h1>
          <p className="text-gray-600 mb-4">
            抱歉，您没有权限访问此页面。
          </p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="text-sm text-gray-600">
            <p><span className="font-medium">当前用户：</span>{user?.email}</p>
            <p><span className="font-medium">当前角色：</span>{role}</p>
          </div>
        </div>

        <div className="space-y-3">
          <Link to="/">
            <Button type="primary" block className="mb-3">
              返回首页
            </Button>
          </Link>
          
          <Link to="/profile">
            <Button block>
              查看个人资料
            </Button>
          </Link>
        </div>

        <div className="mt-6 text-xs text-gray-500">
          <p>
            如果您认为这是一个错误，请联系管理员或
            <Link to="/contact" className="text-blue-600 hover:underline ml-1">
              提交反馈
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

export default Unauthorized