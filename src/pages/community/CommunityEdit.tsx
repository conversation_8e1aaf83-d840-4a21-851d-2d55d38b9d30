import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Switch,
  Upload,
  message,
  Space,
  Row,
  Col,
  Spin,
  Alert,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  UploadOutlined,
  PlusOutlined
} from '@ant-design/icons';
import RichTextEditor from '../../components/RichTextEditor';
import { communityService } from '../../services/communityService';
import type { CommunityPost, CommunityCategory } from '../../services/communityService';

const { TextArea } = Input;
const { Option } = Select;

interface CommunityPostFormData {
  title: string;
  content: string;
  category_id?: string;
  status: 'published' | 'pending' | 'rejected';
  is_featured: boolean;
  rating: number;
}

interface CommunityEditProps {
  mode?: 'edit' | 'create';
}

const CommunityEdit: React.FC<CommunityEditProps> = ({ mode = 'edit' }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [post, setPost] = useState<CommunityPost | null>(null);
  const [categories, setCategories] = useState<CommunityCategory[]>([]);
  const [loading, setLoading] = useState(mode === 'edit');
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const isEditMode = mode === 'edit';
  const pageTitle = isEditMode ? '编辑社区帖子' : '创建社区帖子';

  useEffect(() => {
    loadCategories();
    if (isEditMode && id) {
      loadPost();
    } else {
      setLoading(false);
    }
  }, [id, isEditMode]);

  const loadPost = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const result = await communityService.getPostById(id);
      if (result.success && result.data) {
        const postData = result.data;
        setPost(postData);
        form.setFieldsValue({
          title: postData.title,
          content: postData.content,
          category_id: postData.category_id,
          status: postData.status,
          is_featured: postData.is_featured,
          rating: postData.rating
        });
      } else {
        setError(new Error('帖子不存在'));
      }
    } catch (error) {
      setError(error as Error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const result = await communityService.getAllCategories();
      if (result.success && result.data) {
        setCategories(result.data);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const handleSubmit = async (values: CommunityPostFormData) => {
    setSubmitting(true);
    try {
      let result;
      if (isEditMode && id) {
        result = await communityService.updatePost(id, values);
      } else {
        result = await communityService.createPost({
          ...values,
          user_id: 'current-user-id' // 这里应该从认证系统获取当前用户ID
        });
      }
      
      if (result.success) {
        message.success(isEditMode ? '帖子更新成功' : '帖子创建成功');
        navigate('/community');
      } else {
        message.error(isEditMode ? '更新失败' : '创建失败');
      }
    } catch (error) {
      message.error(isEditMode ? '更新失败' : '创建失败');
    } finally {
      setSubmitting(false);
    }
  };

  const handleImageUpload = (info: any) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      message.success('图片上传成功');
      // 这里可以处理图片上传后的逻辑
    } else if (info.file.status === 'error') {
      message.error('图片上传失败');
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error.message}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => navigate('/community')}>
            返回列表
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 头部操作栏 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/community')}
          >
            返回列表
          </Button>
          <Button 
            type="primary" 
            icon={<SaveOutlined />}
            loading={submitting}
            onClick={() => form.submit()}
          >
            {isEditMode ? '保存修改' : '创建帖子'}
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        {/* 主要表单 */}
        <Col span={16}>
          <Card title={pageTitle}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                status: 'pending',
                is_featured: false,
                rating: 0
              }}
            >
              <Form.Item
                label="帖子标题"
                name="title"
                rules={[
                  { required: true, message: '请输入帖子标题' },
                  { min: 5, message: '标题至少5个字符' },
                  { max: 100, message: '标题不能超过100个字符' }
                ]}
              >
                <Input placeholder="请输入帖子标题" size="large" />
              </Form.Item>

              <Form.Item
                label="帖子内容"
                name="content"
                rules={[
                  { required: true, message: '请输入帖子内容' },
                  { min: 10, message: '内容至少10个字符' }
                ]}
              >
                <RichTextEditor 
                  placeholder="请输入帖子内容"
                  style={{ height: '300px' }}
                />
              </Form.Item>

              <Form.Item label="图片上传">
                <Upload
                  name="image"
                  listType="picture-card"
                  className="avatar-uploader"
                  showUploadList={false}
                  action="/api/upload" // 这里应该是实际的上传接口
                  onChange={handleImageUpload}
                >
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传图片</div>
                  </div>
                </Upload>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 侧边栏设置 */}
        <Col span={8}>
          <Card title="帖子设置" style={{ marginBottom: '24px' }}>
            <Form.Item
              label="分类"
              name="category_id"
            >
              <Select placeholder="选择分类" allowClear>
                {categories.map((category) => (
                  <Option key={category.id} value={category.id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="发布状态"
              name="status"
              rules={[{ required: true, message: '请选择发布状态' }]}
            >
              <Select>
                <Option value="published">已发布</Option>
                <Option value="pending">待审核</Option>
                <Option value="rejected">已拒绝</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="精选帖子"
              name="is_featured"
              valuePropName="checked"
            >
              <Switch 
                checkedChildren="精选" 
                unCheckedChildren="普通" 
              />
            </Form.Item>

            <Form.Item
              label="初始评分"
              name="rating"
            >
              <Select>
                <Option value={0}>0分</Option>
                <Option value={1}>1分</Option>
                <Option value={2}>2分</Option>
                <Option value={3}>3分</Option>
                <Option value={4}>4分</Option>
                <Option value={5}>5分</Option>
              </Select>
            </Form.Item>
          </Card>

          {/* 帮助信息 */}
          <Card title="帮助信息">
            <div style={{ fontSize: '12px', color: '#666' }}>
              <p><strong>状态说明：</strong></p>
              <p>• 已发布：帖子对所有用户可见</p>
              <p>• 待审核：帖子需要管理员审核</p>
              <p>• 已拒绝：帖子被拒绝发布</p>
              
              <Divider style={{ margin: '12px 0' }} />
              
              <p><strong>精选帖子：</strong></p>
              <p>精选帖子会在首页优先展示</p>
              
              <Divider style={{ margin: '12px 0' }} />
              
              <p><strong>评分系统：</strong></p>
              <p>管理员可以为帖子设置初始评分</p>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default CommunityEdit;