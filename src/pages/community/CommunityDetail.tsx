import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Space,
  Button,
  Tag,
  Divider,
  Row,
  Col,
  Statistic,
  Avatar,
  Timeline,
  Rate,
  Form,
  Select,
  Switch,
  message,
  Spin,
  Alert,
  Popconfirm,
  Badge
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  LikeOutlined,
  MessageOutlined,
  StarOutlined,
  StarFilled,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { communityService } from '../../services/communityService';
import type { CommunityPost, CommunityComment } from '../../services/communityService';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

const CommunityDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [post, setPost] = useState<CommunityPost | null>(null);
  const [comments, setComments] = useState<CommunityComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    if (id) {
      loadPost();
      loadComments();
    }
  }, [id]);

  const loadPost = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const result = await communityService.getPostById(id);
      if (result.success && result.data) {
        setPost(result.data);
      } else {
        message.error('加载帖子失败');
      }
    } catch (error) {
      message.error('加载帖子失败');
    } finally {
      setLoading(false);
    }
  };

  const loadComments = async () => {
    if (!id) return;
    
    try {
      const result = await communityService.getCommentsByPostId(id);
      if (result.success && result.data) {
        setComments(result.data);
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    }
  };

  const updateStatus = async (newStatus: 'published' | 'pending' | 'rejected') => {
    if (!post) return;
    
    try {
      const result = await communityService.updatePostStatus(post.id, newStatus);
      if (result.success) {
        message.success('状态更新成功');
        setPost({ ...post, status: newStatus });
      } else {
        message.error('状态更新失败');
      }
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  const toggleFeatured = async () => {
    if (!post) return;
    
    try {
      const result = await communityService.toggleFeatured(post.id, !post.is_featured);
      if (result.success) {
        message.success(post.is_featured ? '已取消精选' : '已设为精选');
        setPost({ ...post, is_featured: !post.is_featured });
      } else {
        message.error('操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  const updateRating = async (rating: number) => {
    if (!post) return;
    
    try {
      const result = await communityService.updateRating(post.id, rating);
      if (result.success) {
        message.success('评分更新成功');
        setPost({ ...post, rating });
      } else {
        message.error('评分更新失败');
      }
    } catch (error) {
      message.error('评分更新失败');
    }
  };

  const deletePost = async () => {
    if (!post) return;
    
    try {
      const result = await communityService.deletePost(post.id);
      if (result.success) {
        message.success('帖子删除成功');
        navigate('/community');
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'success';
      case 'pending': return 'warning';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'published': return '已发布';
      case 'pending': return '待审核';
      case 'rejected': return '已拒绝';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !post) {
    return (
      <Alert
        message="加载失败"
        description={error?.message || '帖子不存在或已被删除'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => navigate('/community')}>
            返回列表
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 头部操作栏 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/community')}
          >
            返回列表
          </Button>
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => navigate(`/community/edit/${post.id}`)}
          >
            编辑帖子
          </Button>
          <Popconfirm
            title="确定删除此帖子吗？"
            description="删除后将无法恢复，请确认操作"
            onConfirm={deletePost}
            okText="确定"
            cancelText="取消"
          >
            <Button danger icon={<DeleteOutlined />}>
              删除帖子
            </Button>
          </Popconfirm>
        </Space>
      </div>

      <Row gutter={24}>
        {/* 主要内容 */}
        <Col span={16}>
          <Card>
            {/* 帖子标题和基本信息 */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={2} style={{ marginBottom: '16px' }}>
                {post.title}
                {post.is_featured && (
                  <Badge 
                    count={<StarFilled style={{ color: '#faad14' }} />} 
                    style={{ marginLeft: '8px' }}
                  />
                )}
              </Title>
              
              <Space size="large">
                <Space>
                  <Avatar icon={<UserOutlined />} />
                  <Text>作者ID: {post.user_id}</Text>
                </Space>
                <Text type="secondary">
                  创建时间: {new Date(post.created_at).toLocaleString()}
                </Text>
                <Tag color={getStatusColor(post.status)}>
                  {getStatusText(post.status)}
                </Tag>
              </Space>
            </div>

            <Divider />

            {/* 帖子内容 */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={4}>帖子内容</Title>
              <div 
                style={{ 
                  padding: '16px', 
                  backgroundColor: '#fafafa', 
                  borderRadius: '6px',
                  minHeight: '200px',
                  whiteSpace: 'pre-wrap'
                }}
              >
                {post.content}
              </div>
            </div>

            {/* 统计信息 */}
            <Row gutter={16}>
              <Col span={6}>
                <Statistic 
                  title="浏览量" 
                  value={post.view_count} 
                  prefix={<EyeOutlined />} 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="点赞数" 
                  value={post.like_count} 
                  prefix={<LikeOutlined />} 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="评论数" 
                  value={post.comment_count} 
                  prefix={<MessageOutlined />} 
                />
              </Col>
              <Col span={6}>
                <div>
                  <Text type="secondary">评分</Text>
                  <div>
                    <Rate 
                      value={post.rating} 
                      onChange={updateRating}
                      allowHalf
                    />
                    <Text style={{ marginLeft: '8px' }}>({post.rating})</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 评论列表 */}
          <Card title="评论列表" style={{ marginTop: '24px' }}>
            {comments.length > 0 ? (
              <Timeline>
                {comments.map((comment) => (
                  <Timeline.Item
                    key={comment.id}
                    dot={
                      comment.status === 'approved' ? (
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                      ) : comment.status === 'rejected' ? (
                        <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                      ) : (
                        <ClockCircleOutlined style={{ color: '#faad14' }} />
                      )
                    }
                  >
                    <div style={{ marginBottom: '8px' }}>
                      <Space>
                        <Avatar size="small" icon={<UserOutlined />} />
                        <Text strong>用户ID: {comment.user_id}</Text>
                        <Text type="secondary">
                          {new Date(comment.created_at).toLocaleString()}
                        </Text>
                        <Tag color={getStatusColor(comment.status)}>
                          {getStatusText(comment.status)}
                        </Tag>
                      </Space>
                    </div>
                    <Paragraph style={{ marginLeft: '32px' }}>
                      {comment.content}
                    </Paragraph>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Text type="secondary">暂无评论</Text>
              </div>
            )}
          </Card>
        </Col>

        {/* 侧边栏 */}
        <Col span={8}>
          {/* 管理操作 */}
          <Card title="管理操作" style={{ marginBottom: '24px' }}>
            <Form form={form} layout="vertical">
              <Form.Item label="帖子状态">
                <Select value={post.status} onChange={updateStatus} style={{ width: '100%' }}>
                  <Option value="published">已发布</Option>
                  <Option value="pending">待审核</Option>
                  <Option value="rejected">已拒绝</Option>
                </Select>
              </Form.Item>
              
              <Form.Item label="精选设置">
                <Switch 
                  checked={post.is_featured}
                  onChange={toggleFeatured}
                  checkedChildren="精选"
                  unCheckedChildren="普通"
                />
              </Form.Item>
            </Form>
          </Card>

          {/* 帖子信息 */}
          <Card title="帖子信息">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text type="secondary">帖子ID:</Text>
                <br />
                <Text copyable>{post.id}</Text>
              </div>
              
              <div>
                <Text type="secondary">作者ID:</Text>
                <br />
                <Text>{post.user_id}</Text>
              </div>
              
              <div>
                <Text type="secondary">分类ID:</Text>
                <br />
                <Text>{post.category_id || '无分类'}</Text>
              </div>
              
              <div>
                <Text type="secondary">创建时间:</Text>
                <br />
                <Text>{new Date(post.created_at).toLocaleString()}</Text>
              </div>
              
              {post.updated_at && (
                <div>
                  <Text type="secondary">更新时间:</Text>
                  <br />
                  <Text>{new Date(post.updated_at).toLocaleString()}</Text>
                </div>
              )}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default CommunityDetail;