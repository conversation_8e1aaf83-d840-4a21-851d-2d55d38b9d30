import React, { useState, useContext, useEffect } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Switch,
  Rate,
  Dropdown,
  Menu,
  Popconfirm,
  Spin,
  Alert
} from 'antd'
import { 
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExclamationCircleOutlined,
  MoreOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import RichTextEditor from '../../components/RichTextEditor'
import { GlobalMessageContext } from '../../components/GlobalMessage'
import { communityService } from '../../services/communityService'
import type { CommunityPost } from '../../services/communityService'

const { Title } = Typography
const { Option } = Select

interface CommunityDataType {
  key: string
  id: string
  title: string
  author: string
  category: string
  status: 'published' | 'pending' | 'rejected'
  views: number
  likes: number
  comments: number
  rating: number
  featured: boolean
  createdAt: string
  updatedAt: string
}

const communityData: CommunityDataType[] = [
  { 
    id: 1, 
    title: '如何保持积极心态', 
    author: 'test09', 
    category: '经验分享',
    status: 'published',
    likes: 42,
    comments: 8,
    views: 125,
    featured: true,
    createdAt: '2023-06-01',
    rating: 4.5
  },
  { 
    id: 2, 
    title: '戒色100天心得体会', 
    author: '测试用户1', 
    category: '心得分享',
    status: 'published',
    likes: 87,
    comments: 15,
    views: 256,
    featured: false,
    createdAt: '2023-06-02',
    rating: 4.8
  },
  { 
    id: 3, 
    title: '求助：最近总是控制不住自己', 
    author: '活跃用户2', 
    category: '求助',
    status: 'pending',
    likes: 0,
    comments: 0,
    views: 0,
    isFeatured: false,
    createdAt: '2023-06-05',
    rating: 0
  },
  { 
    id: 4, 
    title: '分享一个帮助集中注意力的方法', 
    author: '高级用户3', 
    category: '方法分享',
    status: 'rejected',
    likes: 0,
    comments: 0,
    views: 0,
    isFeatured: false,
    createdAt: '2023-06-03',
    rating: 0
  },
]

const communityColumns: TableProps<CommunityDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    render: (text: string) => (
      <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {text}
      </div>
    )
  },
  {
    title: '作者',
    dataIndex: 'author',
    key: 'author',
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    render: (category: string) => {
      const categoryMap: Record<string, { text: string, color: string }> = {
        '经验分享': { text: '经验分享', color: 'green' },
        '心得分享': { text: '心得分享', color: 'blue' },
        '求助': { text: '求助', color: 'orange' },
        '方法分享': { text: '方法分享', color: 'purple' }
      }
      
      const cat = categoryMap[category] || { text: category, color: 'default' }
      return <Tag color={cat.color}>{cat.text}</Tag>
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => {
      const statusMap: Record<string, { text: string, color: 'success' | 'processing' | 'default' | 'error' }> = {
        'published': { text: '已发布', color: 'success' },
        'pending': { text: '待审核', color: 'processing' },
        'rejected': { text: '已拒绝', color: 'error' }
      }
      
      const stat = statusMap[status] || { text: status, color: 'default' }
      return <Tag color={stat.color}>{stat.text}</Tag>
    }
  },
  {
    title: '评分',
    dataIndex: 'rating',
    key: 'rating',
    render: (rating: number) => (
      rating > 0 ? <Rate disabled defaultValue={rating} allowHalf /> : '未评分'
    )
  },
  {
    title: '精选',
    dataIndex: 'featured',
    key: 'featured',
    render: (featured: boolean) => (
      <Tag color={featured ? 'success' : 'default'}>
        {featured ? '是' : '否'}
      </Tag>
    )
  },
  {
    title: '浏览',
    dataIndex: 'views',
    key: 'views',
  },
  {
    title: '点赞',
    dataIndex: 'likes',
    key: 'likes',
  },
  {
    title: '评论',
    dataIndex: 'comments',
    key: 'comments',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    render: (_, record) => (
      <Space size="middle">
        <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewContent(record)}>查看</Button>
        <Button type="link" icon={<EditOutlined />} size="small" onClick={() => handleEditContent(record)}>编辑</Button>
        <Dropdown 
          overlay={
            <Menu>
              <Menu.Item key="1" icon={<EyeOutlined />} onClick={() => handleViewContent(record)}>查看详情</Menu.Item>
              <Menu.Item key="2" icon={<EditOutlined />} onClick={() => handleEditContent(record)}>编辑内容</Menu.Item>
              {record.status === 'pending' && (
                <>
                  <Menu.Item key="3" icon={<EyeOutlined />}>审核通过</Menu.Item>
                  <Menu.Item key="4" icon={<ExclamationCircleOutlined />}>拒绝内容</Menu.Item>
                </>
              )}
              <Menu.Item key="5" icon={<EyeOutlined />}>
                {record.featured ? '取消精选' : '设为精选'}
              </Menu.Item>
              <Menu.Divider />
              <Popconfirm
                title="确定删除此内容吗？"
                description="删除后将无法恢复，请确认操作"
                onConfirm={() => {
                  // 在实际应用中，这里会调用删除API
                  message.success(`内容 "${record.title}" 删除成功`)
                }}
                okText="确定"
                cancelText="取消"
              >
                <Menu.Item key="6" danger icon={<DeleteOutlined />}>删除内容</Menu.Item>
              </Popconfirm>
            </Menu>
          }
        >
          <Button type="link" icon={<MoreOutlined />} size="small">更多</Button>
        </Dropdown>
      </Space>
    ),
  },
]

const CommunityList: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()
  const { message } = useContext(GlobalMessageContext)
  const [communityData, setCommunityData] = useState<CommunityDataType[]>([])
  
  useEffect(() => {
    loadCommunityData()
  }, [])
  
  const loadCommunityData = async () => {
     setLoading(true)
     setError(null)
     try {
       const result = await communityService.getAllPosts()
       if (result.success && result.data) {
         setCommunityData(result.data.map((item: CommunityPost) => ({
           key: item.id,
           id: item.id,
           title: item.title,
           author: item.user_id,
           category: item.category_id || '无分类',
           status: item.status,
           views: item.view_count,
           likes: item.like_count,
           comments: item.comment_count,
           rating: item.rating,
           featured: item.is_featured,
           createdAt: new Date(item.created_at).toLocaleString(),
           updatedAt: item.updated_at ? new Date(item.updated_at).toLocaleString() : ''
         })))
       }
     } catch (err) {
       setError('数据加载失败，请稍后重试')
     } finally {
       setLoading(false)
     }
   }
  
  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('Received values of form: ', values)
      message.success('内容创建成功')
      setIsModalVisible(false)
      form.resetFields()
    }).catch(info => {
      console.log('Validate Failed:', info)
      message.error('表单验证失败')
    })
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    loadCommunityData()
  }
  
  const handleAddContent = () => {
    showModal()
  }

  // 查看社区内容详情
  const handleViewContent = (record: CommunityDataType) => {
    Modal.info({
      title: '社区内容详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <strong>标题：</strong>{record.title}
            </Col>
            <Col span={12}>
              <strong>作者：</strong>{record.author}
            </Col>
            <Col span={12}>
              <strong>分类：</strong>{record.category}
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={record.status === 'published' ? 'success' : record.status === 'pending' ? 'warning' : 'error'}>
                {record.status === 'published' ? '已发布' : record.status === 'pending' ? '待审核' : '已拒绝'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>精选：</strong>
              <Tag color={record.featured ? 'success' : 'default'}>
                {record.featured ? '是' : '否'}
              </Tag>
            </Col>
            <Col span={8}>
              <strong>浏览量：</strong>{record.views}
            </Col>
            <Col span={8}>
              <strong>点赞数：</strong>{record.likes}
            </Col>
            <Col span={8}>
              <strong>评论数：</strong>{record.comments}
            </Col>
            <Col span={12}>
              <strong>评分：</strong>
              {record.rating > 0 ? <Rate disabled defaultValue={record.rating} allowHalf /> : '未评分'}
            </Col>
            <Col span={12}>
              <strong>创建时间：</strong>{record.createdAt}
            </Col>
          </Row>
        </div>
      ),
    });
  };

  // 编辑社区内容
  const handleEditContent = (record: CommunityDataType) => {
    form.setFieldsValue({
      title: record.title,
      category: record.category,
      status: record.status,
      featured: record.featured
    });
    setIsModalVisible(true);
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>社区管理</Title>
      
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <QuickActions 
              onRefresh={handleRefresh}
              onAdd={handleAddContent}
              showRefresh
              showAdd
              refreshLoading={loading}
            />
          </Col>
        </Row>
        
        <SearchFilter 
          onSearch={handleSearch}
          onRefresh={handleRefresh}
          placeholder="搜索标题/作者"
          filters={[
            {
              key: 'status',
              label: '内容状态',
              type: 'select',
              options: [
                { value: 'published', label: '已发布' },
                { value: 'pending', label: '待审核' },
                { value: 'rejected', label: '已拒绝' }
              ]
            },
            {
              key: 'category',
              label: '内容分类',
              type: 'select',
              options: [
                { value: '经验分享', label: '经验分享' },
                { value: '心得分享', label: '心得分享' },
                { value: '求助', label: '求助' },
                { value: '方法分享', label: '方法分享' }
              ]
            }
          ]}
          loading={loading}
        />
        
        {error && (
          <Alert 
            message="错误" 
            description={error} 
            type="error" 
            showIcon 
            style={{ marginTop: 16 }}
          />
        )}
        
        <Spin spinning={loading}>
          <Table 
            columns={communityColumns} 
            dataSource={communityData} 
            pagination={{ 
              showSizeChanger: true, 
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 条记录`,
              defaultPageSize: 20
            }}
            scroll={{ x: 1200 }}
            style={{ marginTop: 16 }}
          />
        </Spin>
      </Card>
      
      <Modal
        title="发布内容"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="title" 
            label="标题" 
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请输入标题" />
          </Form.Item>
          <Form.Item 
            name="author" 
            label="作者" 
            rules={[{ required: true, message: '请输入作者' }]}
          >
            <Input placeholder="请输入作者" />
          </Form.Item>
          <Form.Item 
            name="category" 
            label="分类" 
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              <Option value="经验分享">经验分享</Option>
              <Option value="心得分享">心得分享</Option>
              <Option value="求助">求助</Option>
              <Option value="方法分享">方法分享</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="content" 
            label="内容" 
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <RichTextEditor placeholder="请输入内容" />
          </Form.Item>
          <Form.Item name="featured" label="设为精选" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default CommunityList