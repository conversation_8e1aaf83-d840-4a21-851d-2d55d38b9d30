import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Statistic,
  Row,
  Col,
  Tabs,
  Form,
  Input,
  Select,
  DatePicker,
  Modal,
  message,
  Popconfirm,
  Alert,
  Spin,
  Typography,
  Tooltip,
  Badge,
  Progress,
  Descriptions,
  Drawer,
  Switch,
  InputNumber,
  Divider
} from 'antd'
import {
  SecurityScanOutlined,
  WarningOutlined,
  UserOutlined,
  SettingOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  SafetyOutlined,
  AlertOutlined,
  BarChartOutlined
} from '@ant-design/icons'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { riskService } from '../../services/riskService'
import type { 
  RiskRule, 
  RiskEvent, 
  UserRiskProfile, 
  RiskStats,
  RiskCondition,
  RiskAction
} from '../../services/riskService'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs
const { RangePicker } = DatePicker
const { Option } = Select
const { TextArea } = Input

const RiskManagement: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('rules')
  
  // 风险规则状态
  const [rules, setRules] = useState<RiskRule[]>([])
  const [rulePage, setRulePage] = useState(1)
  const [rulePageSize, setRulePageSize] = useState(20)
  const [ruleTotal, setRuleTotal] = useState(0)
  
  // 风险事件状态
  const [events, setEvents] = useState<RiskEvent[]>([])
  const [eventPage, setEventPage] = useState(1)
  const [eventPageSize, setEventPageSize] = useState(20)
  const [eventTotal, setEventTotal] = useState(0)
  
  // 用户风险档案状态
  const [profiles, setProfiles] = useState<UserRiskProfile[]>([])
  const [profilePage, setProfilePage] = useState(1)
  const [profilePageSize, setProfilePageSize] = useState(20)
  const [profileTotal, setProfileTotal] = useState(0)
  
  // 统计数据
  const [stats, setStats] = useState<RiskStats | null>(null)
  
  // 模态框状态
  const [ruleModalVisible, setRuleModalVisible] = useState(false)
  const [eventDetailVisible, setEventDetailVisible] = useState(false)
  const [profileDetailVisible, setProfileDetailVisible] = useState(false)
  const [selectedRule, setSelectedRule] = useState<RiskRule | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<RiskEvent | null>(null)
  const [selectedProfile, setSelectedProfile] = useState<UserRiskProfile | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  
  const [form] = Form.useForm()
  const [reviewForm] = Form.useForm()

  // 加载数据
  const loadRules = async () => {
    try {
      setLoading(true)
      const response = await riskService.getRiskRules({
        page: rulePage,
        pageSize: rulePageSize
      })
      setRules(response.data)
      setRuleTotal(response.total)
    } catch (error) {
      console.error('加载风险规则失败:', error)
      setError('加载风险规则失败')
    } finally {
      setLoading(false)
    }
  }

  const loadEvents = async () => {
    try {
      setLoading(true)
      const response = await riskService.getRiskEvents({
        page: eventPage,
        pageSize: eventPageSize
      })
      setEvents(response.data)
      setEventTotal(response.total)
    } catch (error) {
      console.error('加载风险事件失败:', error)
      setError('加载风险事件失败')
    } finally {
      setLoading(false)
    }
  }

  const loadProfiles = async () => {
    try {
      setLoading(true)
      const response = await riskService.getUserRiskProfiles({
        page: profilePage,
        pageSize: profilePageSize
      })
      setProfiles(response.data)
      setProfileTotal(response.total)
    } catch (error) {
      console.error('加载用户风险档案失败:', error)
      setError('加载用户风险档案失败')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const statsData = await riskService.getRiskStats()
      setStats(statsData)
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  useEffect(() => {
    loadStats()
  }, [])

  useEffect(() => {
    if (activeTab === 'rules') {
      loadRules()
    } else if (activeTab === 'events') {
      loadEvents()
    } else if (activeTab === 'profiles') {
      loadProfiles()
    }
  }, [activeTab, rulePage, eventPage, profilePage])

  // 风险规则列配置
  const ruleColumns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: RiskRule) => (
        <div>
          <Text strong>{name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => {
        const categoryMap = {
          security: { text: '安全', color: 'red' },
          financial: { text: '金融', color: 'gold' },
          behavior: { text: '行为', color: 'blue' },
          content: { text: '内容', color: 'green' }
        }
        const config = categoryMap[category as keyof typeof categoryMap] || { text: category, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => {
        const severityMap = {
          low: { text: '低', color: 'success' },
          medium: { text: '中', color: 'warning' },
          high: { text: '高', color: 'error' },
          critical: { text: '严重', color: 'error' }
        }
        const config = severityMap[severity as keyof typeof severityMap] || { text: severity, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '条件数',
      key: 'conditions',
      render: (_: any, record: RiskRule) => record.conditions.length
    },
    {
      title: '动作数',
      key: 'actions',
      render: (_: any, record: RiskRule) => record.actions.length
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 200,
      render: (_: any, record: RiskRule) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                setSelectedRule(record)
                setEventDetailVisible(true)
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => {
                setSelectedRule(record)
                setIsEditing(true)
                setRuleModalVisible(true)
                form.setFieldsValue(record)
              }}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除此规则吗？"
            onConfirm={() => handleDeleteRule(record.id)}
          >
            <Tooltip title="删除">
              <Button type="link" icon={<DeleteOutlined />} size="small" danger />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 风险事件列配置
  const eventColumns = [
    {
      title: '事件ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '用户信息',
      key: 'user',
      render: (_: any, record: RiskEvent) => (
        <Space>
          {record.user?.avatar_url && (
            <img 
              src={record.user.avatar_url} 
              alt="avatar" 
              style={{ width: 32, height: 32, borderRadius: '50%' }}
            />
          )}
          <div>
            <div>{record.user?.username || '未知用户'}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.user?.email}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '事件类型',
      dataIndex: 'event_type',
      key: 'event_type'
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => {
        const severityMap = {
          low: { text: '低', color: 'success' },
          medium: { text: '中', color: 'warning' },
          high: { text: '高', color: 'error' },
          critical: { text: '严重', color: 'error' }
        }
        const config = severityMap[severity as keyof typeof severityMap] || { text: severity, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '待处理', color: 'processing' },
          reviewed: { text: '已审核', color: 'success' },
          resolved: { text: '已解决', color: 'success' },
          ignored: { text: '已忽略', color: 'default' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: RiskEvent) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                setSelectedEvent(record)
                setEventDetailVisible(true)
              }}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <>
              <Tooltip title="标记为已解决">
                <Button 
                  type="link" 
                  icon={<CheckOutlined />} 
                  size="small"
                  style={{ color: '#52c41a' }}
                  onClick={() => handleReviewEvent(record.id, 'resolved')}
                />
              </Tooltip>
              <Tooltip title="忽略">
                <Button 
                  type="link" 
                  icon={<CloseOutlined />} 
                  size="small"
                  onClick={() => handleReviewEvent(record.id, 'ignored')}
                />
              </Tooltip>
            </>
          )}
        </Space>
      )
    }
  ]

  // 用户风险档案列配置
  const profileColumns = [
    {
      title: '用户信息',
      key: 'user',
      render: (_: any, record: UserRiskProfile) => (
        <Space>
          {record.user?.avatar_url && (
            <img 
              src={record.user.avatar_url} 
              alt="avatar" 
              style={{ width: 32, height: 32, borderRadius: '50%' }}
            />
          )}
          <div>
            <div>{record.user?.username || '未知用户'}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.user?.email}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '风险等级',
      dataIndex: 'risk_level',
      key: 'risk_level',
      render: (level: string) => {
        const levelMap = {
          low: { text: '低风险', color: 'success' },
          medium: { text: '中风险', color: 'warning' },
          high: { text: '高风险', color: 'error' },
          critical: { text: '极高风险', color: 'error' }
        }
        const config = levelMap[level as keyof typeof levelMap] || { text: level, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '风险评分',
      dataIndex: 'risk_score',
      key: 'risk_score',
      render: (score: number) => (
        <div style={{ width: 120 }}>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={score >= 80 ? '#ff4d4f' : score >= 60 ? '#faad14' : '#52c41a'}
          />
          <Text style={{ fontSize: '12px' }}>{score}/100</Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          active: { text: '正常', color: 'success' },
          monitoring: { text: '监控中', color: 'warning' },
          restricted: { text: '受限', color: 'error' }
        }
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '风险因子数',
      key: 'factors',
      render: (_: any, record: UserRiskProfile) => record.factors.length
    },
    {
      title: '最后评估',
      dataIndex: 'last_assessment',
      key: 'last_assessment',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: UserRiskProfile) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                setSelectedProfile(record)
                setProfileDetailVisible(true)
              }}
            />
          </Tooltip>
          <Tooltip title="更新状态">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleUpdateProfileStatus(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  // 处理函数
  const handleDeleteRule = async (id: string) => {
    try {
      await riskService.deleteRiskRule(id)
      message.success('删除成功')
      loadRules()
    } catch (error) {
      message.error(`删除失败: ${error}`)
    }
  }

  const handleReviewEvent = async (id: string, status: 'resolved' | 'ignored') => {
    try {
      await riskService.reviewRiskEvent(id, status)
      message.success(`事件已标记为${status === 'resolved' ? '已解决' : '已忽略'}`)
      loadEvents()
    } catch (error) {
      message.error(`操作失败: ${error}`)
    }
  }

  const handleUpdateProfileStatus = async (profile: UserRiskProfile) => {
    // 这里可以打开一个模态框来选择新状态
    Modal.confirm({
      title: '更新用户风险状态',
      content: `确定要更新用户 ${profile.user?.username} 的风险状态吗？`,
      onOk: async () => {
        try {
          await riskService.updateUserRiskStatus(profile.user_id, 'monitoring')
          message.success('状态更新成功')
          loadProfiles()
        } catch (error) {
          message.error(`更新失败: ${error}`)
        }
      }
    })
  }

  const handleSaveRule = async (values: any) => {
    try {
      if (isEditing && selectedRule) {
        await riskService.updateRiskRule(selectedRule.id, values)
        message.success('规则更新成功')
      } else {
        await riskService.createRiskRule(values)
        message.success('规则创建成功')
      }
      setRuleModalVisible(false)
      form.resetFields()
      loadRules()
    } catch (error) {
      message.error(`操作失败: ${error}`)
    }
  }

  const handleRefresh = () => {
    if (activeTab === 'rules') {
      loadRules()
    } else if (activeTab === 'events') {
      loadEvents()
    } else if (activeTab === 'profiles') {
      loadProfiles()
    }
    loadStats()
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>风险管理</Title>
      
      {/* 统计卡片 */}
      {stats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总事件数"
                value={stats.totalEvents}
                prefix={<SecurityScanOutlined />}
                suffix="个"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="待处理事件"
                value={stats.pendingEvents}
                prefix={<Badge status="warning" />}
                suffix="个"
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="高风险用户"
                value={stats.highRiskUsers}
                prefix={<WarningOutlined />}
                suffix="个"
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="规则覆盖率"
                value={stats.rulesCoverage}
                prefix={<SafetyOutlined />}
                suffix="%"
                precision={1}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* 风险规则 */}
          <TabPane tab={<Space><SettingOutlined />风险规则</Space>} key="rules">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
              <Col>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setIsEditing(false)
                    setSelectedRule(null)
                    setRuleModalVisible(true)
                    form.resetFields()
                  }}
                >
                  新建规则
                </Button>
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索规则名称/描述"
              filters={[
                {
                  key: 'category',
                  label: '规则类别',
                  type: 'select',
                  options: [
                    { value: 'security', label: '安全' },
                    { value: 'financial', label: '金融' },
                    { value: 'behavior', label: '行为' },
                    { value: 'content', label: '内容' }
                  ]
                },
                {
                  key: 'severity',
                  label: '严重程度',
                  type: 'select',
                  options: [
                    { value: 'low', label: '低' },
                    { value: 'medium', label: '中' },
                    { value: 'high', label: '高' },
                    { value: 'critical', label: '严重' }
                  ]
                },
                {
                  key: 'status',
                  label: '状态',
                  type: 'select',
                  options: [
                    { value: 'active', label: '启用' },
                    { value: 'inactive', label: '禁用' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={ruleColumns} 
                dataSource={rules} 
                rowKey="id"
                pagination={{ 
                  current: rulePage,
                  pageSize: rulePageSize,
                  total: ruleTotal,
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, size) => {
                    setRulePage(page)
                    setRulePageSize(size || 20)
                  }
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>

          {/* 风险事件 */}
          <TabPane tab={<Space><AlertOutlined />风险事件</Space>} key="events">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索用户名/邮箱/事件描述"
              filters={[
                {
                  key: 'severity',
                  label: '严重程度',
                  type: 'select',
                  options: [
                    { value: 'low', label: '低' },
                    { value: 'medium', label: '中' },
                    { value: 'high', label: '高' },
                    { value: 'critical', label: '严重' }
                  ]
                },
                {
                  key: 'status',
                  label: '处理状态',
                  type: 'select',
                  options: [
                    { value: 'pending', label: '待处理' },
                    { value: 'reviewed', label: '已审核' },
                    { value: 'resolved', label: '已解决' },
                    { value: 'ignored', label: '已忽略' }
                  ]
                },
                {
                  key: 'dateRange',
                  label: '时间范围',
                  type: 'dateRange'
                }
              ]}
              loading={loading}
            />
            
            <Spin spinning={loading}>
              <Table 
                columns={eventColumns} 
                dataSource={events} 
                rowKey="id"
                pagination={{ 
                  current: eventPage,
                  pageSize: eventPageSize,
                  total: eventTotal,
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, size) => {
                    setEventPage(page)
                    setEventPageSize(size || 20)
                  }
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>

          {/* 用户风险档案 */}
          <TabPane tab={<Space><UserOutlined />用户风险档案</Space>} key="profiles">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleRefresh}
              onRefresh={handleRefresh}
              placeholder="搜索用户名/邮箱"
              filters={[
                {
                  key: 'risk_level',
                  label: '风险等级',
                  type: 'select',
                  options: [
                    { value: 'low', label: '低风险' },
                    { value: 'medium', label: '中风险' },
                    { value: 'high', label: '高风险' },
                    { value: 'critical', label: '极高风险' }
                  ]
                },
                {
                  key: 'status',
                  label: '状态',
                  type: 'select',
                  options: [
                    { value: 'active', label: '正常' },
                    { value: 'monitoring', label: '监控中' },
                    { value: 'restricted', label: '受限' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            <Spin spinning={loading}>
              <Table 
                columns={profileColumns} 
                dataSource={profiles} 
                rowKey="id"
                pagination={{ 
                  current: profilePage,
                  pageSize: profilePageSize,
                  total: profileTotal,
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, size) => {
                    setProfilePage(page)
                    setProfilePageSize(size || 20)
                  }
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
        </Tabs>
      </Card>

      {/* 规则编辑模态框 */}
      <Modal
        title={isEditing ? '编辑风险规则' : '新建风险规则'}
        visible={ruleModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setRuleModalVisible(false)
          form.resetFields()
        }}
        okText="保存"
        cancelText="取消"
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleSaveRule}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                name="name" 
                label="规则名称" 
                rules={[{ required: true, message: '请输入规则名称' }]}
              >
                <Input placeholder="请输入规则名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item 
                name="category" 
                label="规则类别" 
                rules={[{ required: true, message: '请选择规则类别' }]}
              >
                <Select placeholder="请选择规则类别">
                  <Option value="security">安全</Option>
                  <Option value="financial">金融</Option>
                  <Option value="behavior">行为</Option>
                  <Option value="content">内容</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                name="severity" 
                label="严重程度" 
                rules={[{ required: true, message: '请选择严重程度' }]}
              >
                <Select placeholder="请选择严重程度">
                  <Option value="low">低</Option>
                  <Option value="medium">中</Option>
                  <Option value="high">高</Option>
                  <Option value="critical">严重</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item 
                name="status" 
                label="状态" 
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item 
            name="description" 
            label="规则描述" 
            rules={[{ required: true, message: '请输入规则描述' }]}
          >
            <TextArea rows={3} placeholder="请输入规则描述" />
          </Form.Item>
          <Alert
            message="提示"
            description="规则条件和动作配置需要在高级设置中进行，这里仅配置基本信息。"
            type="info"
            showIcon
          />
        </Form>
      </Modal>

      {/* 事件详情抽屉 */}
      <Drawer
        title="风险事件详情"
        placement="right"
        width={600}
        visible={eventDetailVisible}
        onClose={() => setEventDetailVisible(false)}
      >
        {selectedEvent && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Descriptions title="基本信息" bordered column={1}>
              <Descriptions.Item label="事件ID">{selectedEvent.id}</Descriptions.Item>
              <Descriptions.Item label="用户">{selectedEvent.user?.username}</Descriptions.Item>
              <Descriptions.Item label="事件类型">{selectedEvent.event_type}</Descriptions.Item>
              <Descriptions.Item label="严重程度">
                <Tag color={selectedEvent.severity === 'critical' ? 'red' : 'orange'}>
                  {selectedEvent.severity}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={selectedEvent.status === 'pending' ? 'processing' : 'success'}>
                  {selectedEvent.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="描述">{selectedEvent.description}</Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(selectedEvent.created_at).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
            
            <Divider />
            
            <Title level={5}>事件数据</Title>
            <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
              {JSON.stringify(selectedEvent.data, null, 2)}
            </pre>
            
            <Divider />
            
            <Title level={5}>已执行动作</Title>
            <Space wrap>
              {selectedEvent.actions_taken.map((action, index) => (
                <Tag key={index} color="blue">{action}</Tag>
              ))}
            </Space>
          </Space>
        )}
      </Drawer>

      {/* 用户风险档案详情抽屉 */}
      <Drawer
        title="用户风险档案"
        placement="right"
        width={600}
        visible={profileDetailVisible}
        onClose={() => setProfileDetailVisible(false)}
      >
        {selectedProfile && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Descriptions title="基本信息" bordered column={1}>
              <Descriptions.Item label="用户">{selectedProfile.user?.username}</Descriptions.Item>
              <Descriptions.Item label="风险等级">
                <Tag color={selectedProfile.risk_level === 'critical' ? 'red' : 'orange'}>
                  {selectedProfile.risk_level}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="风险评分">
                <Progress 
                  percent={selectedProfile.risk_score} 
                  strokeColor={selectedProfile.risk_score >= 80 ? '#ff4d4f' : '#faad14'}
                />
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={selectedProfile.status === 'restricted' ? 'red' : 'green'}>
                  {selectedProfile.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="最后评估">
                {new Date(selectedProfile.last_assessment).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
            
            <Divider />
            
            <Title level={5}>风险因子</Title>
            {selectedProfile.factors.map((factor, index) => (
              <Card key={index} size="small" style={{ marginBottom: 8 }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Text strong>{factor.factor}</Text>
                    <br />
                    <Text type="secondary">{factor.description}</Text>
                  </Col>
                  <Col>
                    <Text>权重: {(factor.weight * 100).toFixed(0)}%</Text>
                    <br />
                    <Text>评分: {factor.score}</Text>
                  </Col>
                </Row>
              </Card>
            ))}
          </Space>
        )}
      </Drawer>
    </Space>
  )
}

export default RiskManagement