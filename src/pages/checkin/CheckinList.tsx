import React, { useState, useContext } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  DatePicker,
  Statistic,
  Progress,
  Spin,
  Alert
} from 'antd'
import { 
  SearchOutlined,
  CalendarOutlined,
  SmileOutlined,
  FrownOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import type { RangePickerProps } from 'antd/es/date-picker'
import { useNavigate } from 'react-router-dom'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'

const { Title } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

interface CheckinDataType {
  id: number
  username: string
  date: string
  mood: number
  energy: number
  water: number
  exercise: number
  sleep: number
  notes: string
  flowValue: number
  healthScore: number
  urge: number
  stress: number
}

const checkinData: CheckinDataType[] = [
  { 
    id: 1, 
    username: 'test09', 
    date: '2023-06-10', 
    mood: 4,
    energy: 3,
    water: 6,
    exercise: 30,
    sleep: 7.5,
    notes: '今天感觉不错，坚持打卡',
    flowValue: 24,
    healthScore: 80,
    urge: 3,
    stress: 2
  },
  { 
    id: 2, 
    username: '测试用户1', 
    date: '2023-06-10', 
    mood: 5,
    energy: 4,
    water: 8,
    exercise: 45,
    sleep: 8,
    notes: '状态很好，继续保持',
    flowValue: 32,
    healthScore: 92,
    urge: 1,
    stress: 1
  },
  { 
    id: 3, 
    username: '活跃用户2', 
    date: '2023-06-10', 
    mood: 3,
    energy: 5,
    water: 7,
    exercise: 60,
    sleep: 6.5,
    notes: '有点累，但还是坚持了',
    flowValue: 28,
    healthScore: 75,
    urge: 5,
    stress: 4
  },
  { 
    id: 4, 
    username: '高级用户3', 
    date: '2023-06-10', 
    mood: 4,
    energy: 4,
    water: 8,
    exercise: 30,
    sleep: 7,
    notes: '每日打卡已成为习惯',
    flowValue: 30,
    healthScore: 85,
    urge: 2,
    stress: 3
  },
]

const CheckinList: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { message } = useContext(GlobalMessageContext)
  const navigate = useNavigate()

  // 处理函数
  const handleViewCheckin = (record: CheckinDataType) => {
    navigate(`/checkin/detail/${record.id}`)
  }

  const handleEditCheckin = (record: CheckinDataType) => {
    navigate(`/checkin/edit/${record.id}`)
  }

  const onChange: RangePickerProps['onChange'] = (dates, dateStrings) => {
    console.log(dates, dateStrings)
  }

  const checkinColumns: TableProps<CheckinDataType>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '心情',
      dataIndex: 'mood',
      key: 'mood',
      render: (mood: number) => (
        <Space>
          {[...Array(5)].map((_, i) => (
            <SmileOutlined key={i} style={{ color: i < mood ? '#52c41a' : '#f0f0f0' }} />
          ))}
        </Space>
      )
    },
    {
      title: '能量',
      dataIndex: 'energy',
      key: 'energy',
      render: (energy: number) => (
        <Space>
          {[...Array(5)].map((_, i) => (
            <FrownOutlined key={i} style={{ color: i < energy ? '#1890ff' : '#f0f0f0' }} />
          ))}
        </Space>
      )
    },
    {
      title: '喝水(杯)',
      dataIndex: 'water',
      key: 'water',
    },
    {
      title: '运动(分钟)',
      dataIndex: 'exercise',
      key: 'exercise',
    },
    {
      title: '睡眠(小时)',
      dataIndex: 'sleep',
      key: 'sleep',
    },
    {
      title: '冲动强度',
      dataIndex: 'urge',
      key: 'urge',
    },
    {
      title: '压力程度',
      dataIndex: 'stress',
      key: 'stress',
    },
    {
      title: '健康评分',
      dataIndex: 'healthScore',
      key: 'healthScore',
      render: (healthScore: number) => (
        <Progress 
          percent={healthScore} 
          size="small" 
          status={healthScore > 80 ? 'success' : 'normal'} 
          format={() => healthScore}
        />
      )
    },
    {
      title: '心流值',
      dataIndex: 'flowValue',
      key: 'flowValue',
      render: (flowValue: number) => (
        <Progress 
          percent={flowValue * 2} 
          size="small" 
          status={flowValue > 30 ? 'success' : 'normal'} 
          format={() => flowValue}
        />
      )
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => (
        <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {notes}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_: any, record: CheckinDataType) => (
        <Space size="middle">
          <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewCheckin(record)}>查看</Button>
          <Button type="link" icon={<EditOutlined />} size="small" onClick={() => handleEditCheckin(record)}>编辑</Button>
        </Space>
      ),
    },
  ]

  const handleSearch = (value: string) => {
    console.log('搜索:', value)
  }

  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      message?.success('数据已刷新')
    }, 1000)
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>签到管理</Title>
      
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日签到"
              value={156}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本周签到"
              value={892}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均心情"
              value={4.2}
              precision={1}
              prefix={<SmileOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均健康分"
              value={82.5}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Input
              placeholder="搜索用户名"
              prefix={<SearchOutlined />}
              onPressEnter={(e) => handleSearch((e.target as HTMLInputElement).value)}
            />
          </Col>
          <Col span={8}>
            <RangePicker onChange={onChange} />
          </Col>
          <Col span={8}>
            <Space>
              <Select defaultValue="all" style={{ width: 120 }}>
                <Option value="all">全部状态</Option>
                <Option value="good">状态良好</Option>
                <Option value="normal">状态一般</Option>
                <Option value="bad">状态较差</Option>
              </Select>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={checkinColumns}
          dataSource={checkinData}
          rowKey="id"
          loading={loading}
          pagination={{
            total: checkinData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  )
}

export default CheckinList