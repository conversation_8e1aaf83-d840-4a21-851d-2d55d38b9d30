import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Tag,
  Progress,
  Descriptions,
  Alert,
  Spin,
  Avatar,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  SmileOutlined,
  FrownOutlined,
  CalendarOutlined,
  UserOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface CheckinData {
  id: number;
  username: string;
  date: string;
  mood: number;
  energy: number;
  water: number;
  exercise: number;
  sleep: number;
  notes: string;
  flowValue: number;
  healthScore: number;
  urge: number;
  stress: number;
  avatar?: string;
  email?: string;
}

const CheckinDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [checkin, setCheckin] = useState<CheckinData | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCheckinDetail = async () => {
      try {
        setLoading(true);
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟数据
        const mockData: CheckinData = {
          id: parseInt(id || '1'),
          username: 'test09',
          date: '2023-06-10',
          mood: 4,
          energy: 3,
          water: 6,
          exercise: 30,
          sleep: 7.5,
          notes: '今天感觉不错，坚持打卡。早上起床后做了简单的拉伸运动，感觉身体状态很好。工作效率也比较高，完成了预定的任务。晚上看了一会儿书，准备早点休息。',
          flowValue: 24,
          healthScore: 80,
          urge: 3,
          stress: 2,
          avatar: '',
          email: '<EMAIL>'
        };
        
        setCheckin(mockData);
      } catch (err) {
        setError('加载签到详情失败');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchCheckinDetail();
    }
  }, [id]);

  const renderMoodStars = (value: number, IconComponent: React.ComponentType<any>, color: string) => (
    <Space>
      {[...Array(5)].map((_, i) => (
        <IconComponent
          key={i}
          style={{ color: i < value ? color : '#f0f0f0', fontSize: '16px' }}
        />
      ))}
      <Text strong>({value}/5)</Text>
    </Space>
  );

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !checkin) {
    return (
      <Alert
        message="加载失败"
        description={error || '签到记录不存在'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => navigate('/checkin')}>
            返回列表
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 头部操作栏 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/checkin')}
          >
            返回列表
          </Button>
        </Col>
        <Col>
          <Space>
            <Button 
              icon={<EditOutlined />} 
              type="primary"
              onClick={() => navigate(`/checkin/edit/${checkin.id}`)}
            >
              编辑
            </Button>
          </Space>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        {/* 用户信息卡片 */}
        <Col xs={24} lg={8}>
          <Card title="用户信息">
            <div style={{ textAlign: 'center', marginBottom: '16px' }}>
              <Avatar 
                size={80} 
                icon={<UserOutlined />}
                src={checkin.avatar}
                style={{ marginBottom: '8px' }}
              />
              <div>
                <Title level={4} style={{ margin: 0 }}>
                  {checkin.username}
                </Title>
                <Text type="secondary">{checkin.email}</Text>
              </div>
            </div>
            
            <Descriptions column={1} size="small">
              <Descriptions.Item label="签到日期">
                <Space>
                  <CalendarOutlined />
                  {checkin.date}
                </Space>
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 评分概览 */}
          <Card title="评分概览" style={{ marginTop: '16px' }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={checkin.healthScore}
                    size={80}
                    status={checkin.healthScore > 80 ? 'success' : 'normal'}
                  />
                  <div style={{ marginTop: '8px' }}>
                    <Text strong>健康评分</Text>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={checkin.flowValue * 2}
                    size={80}
                    strokeColor="#1890ff"
                  />
                  <div style={{ marginTop: '8px' }}>
                    <Text strong>心流值</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 详细信息 */}
        <Col xs={24} lg={16}>
          <Card title="签到详情">
            <Row gutter={[24, 24]}>
              {/* 情绪状态 */}
              <Col xs={24} sm={12}>
                <Card size="small" title="情绪状态">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>心情：</Text>
                      {renderMoodStars(checkin.mood, SmileOutlined, '#52c41a')}
                    </div>
                    <div>
                      <Text strong>能量：</Text>
                      {renderMoodStars(checkin.energy, FrownOutlined, '#1890ff')}
                    </div>
                  </Space>
                </Card>
              </Col>

              {/* 健康数据 */}
              <Col xs={24} sm={12}>
                <Card size="small" title="健康数据">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="喝水量">
                      <Tag color="blue">{checkin.water} 杯</Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="运动时长">
                      <Tag color="green">{checkin.exercise} 分钟</Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="睡眠时长">
                      <Tag color="purple">{checkin.sleep} 小时</Tag>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>

              {/* 心理状态 */}
              <Col xs={24} sm={12}>
                <Card size="small" title="心理状态">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="冲动强度">
                      <Progress 
                        percent={checkin.urge * 20} 
                        size="small" 
                        strokeColor={checkin.urge > 3 ? '#ff4d4f' : '#52c41a'}
                        format={() => `${checkin.urge}/5`}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="压力程度">
                      <Progress 
                        percent={checkin.stress * 20} 
                        size="small" 
                        strokeColor={checkin.stress > 3 ? '#ff4d4f' : '#52c41a'}
                        format={() => `${checkin.stress}/5`}
                      />
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>

              {/* 备注信息 */}
              <Col xs={24}>
                <Card size="small" title="备注信息">
                  <div style={{ 
                    padding: '12px', 
                    backgroundColor: '#fafafa', 
                    borderRadius: '6px',
                    minHeight: '100px'
                  }}>
                    <Text>{checkin.notes || '暂无备注'}</Text>
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default CheckinDetail;