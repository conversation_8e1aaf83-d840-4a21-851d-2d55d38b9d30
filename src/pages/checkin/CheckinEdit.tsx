import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Space, 
  Typography, 
  Row, 
  Col,
  Slider,
  InputNumber,
  DatePicker,
  message,
  Spin
} from 'antd'
import { 
  ArrowLeftOutlined, 
  SaveOutlined,
  SmileOutlined,
  FrownOutlined
} from '@ant-design/icons'
import { useNavigate, useParams } from 'react-router-dom'
import moment from 'moment'
import RichTextEditor from '../../components/RichTextEditor'

const { Title } = Typography
const { TextArea } = Input

interface CheckinFormData {
  username: string
  date: string
  mood: number
  energy: number
  water: number
  exercise: number
  sleep: number
  notes: string
  flowValue: number
  healthScore: number
  urge: number
  stress: number
}

const CheckinEdit: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataLoading, setDataLoading] = useState(true)
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()

  // 模拟数据获取
  useEffect(() => {
    const fetchData = async () => {
      setDataLoading(true)
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟获取的数据
        const mockData: CheckinFormData = {
          username: 'test09',
          date: '2023-06-10',
          mood: 4,
          energy: 3,
          water: 6,
          exercise: 30,
          sleep: 7.5,
          notes: '今天感觉不错，坚持打卡',
          flowValue: 24,
          healthScore: 80,
          urge: 3,
          stress: 2
        }
        
        form.setFieldsValue({
          ...mockData,
          date: moment(mockData.date)
        })
      } catch (error) {
        message.error('数据加载失败')
      } finally {
        setDataLoading(false)
      }
    }

    if (id) {
      fetchData()
    }
  }, [id, form])

  const handleSubmit = async (values: any) => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      console.log('提交的数据:', {
        ...values,
        date: values.date.format('YYYY-MM-DD')
      })
      
      message.success('签到信息更新成功')
      navigate('/checkin')
    } catch (error) {
      message.error('更新失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleBack = () => {
    navigate('/checkin')
  }

  if (dataLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载中...</div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <Space style={{ marginBottom: 24 }}>
        <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
          返回列表
        </Button>
        <Title level={2} style={{ margin: 0 }}>编辑签到信息</Title>
      </Space>

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            mood: 3,
            energy: 3,
            water: 8,
            exercise: 30,
            sleep: 8,
            flowValue: 20,
            healthScore: 80,
            urge: 3,
            stress: 3
          }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="用户名"
                name="username"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input placeholder="请输入用户名" disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="日期"
                name="date"
                rules={[{ required: true, message: '请选择日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label={
                  <Space>
                    <SmileOutlined style={{ color: '#52c41a' }} />
                    心情 (1-5星)
                  </Space>
                }
                name="mood"
              >
                <Slider
                  min={1}
                  max={5}
                  marks={{
                    1: '1星',
                    2: '2星',
                    3: '3星',
                    4: '4星',
                    5: '5星'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={
                  <Space>
                    <FrownOutlined style={{ color: '#1890ff' }} />
                    能量 (1-5星)
                  </Space>
                }
                name="energy"
              >
                <Slider
                  min={1}
                  max={5}
                  marks={{
                    1: '1星',
                    2: '2星',
                    3: '3星',
                    4: '4星',
                    5: '5星'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label="喝水量 (杯)"
                name="water"
              >
                <InputNumber min={0} max={20} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="运动时长 (分钟)"
                name="exercise"
              >
                <InputNumber min={0} max={300} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="睡眠时长 (小时)"
                name="sleep"
              >
                <InputNumber min={0} max={24} step={0.5} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label="冲动强度 (1-10)"
                name="urge"
              >
                <Slider min={1} max={10} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="压力程度 (1-10)"
                name="stress"
              >
                <Slider min={1} max={10} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="心流值"
                name="flowValue"
              >
                <InputNumber min={0} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="健康评分 (%)"
            name="healthScore"
          >
            <Slider min={0} max={100} />
          </Form.Item>

          <Form.Item
            label="备注"
            name="notes"
          >
            <RichTextEditor 
              placeholder="记录今天的感受和想法..."
              style={{ minHeight: '120px' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
                保存修改
              </Button>
              <Button onClick={handleBack}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  )
}

export default CheckinEdit