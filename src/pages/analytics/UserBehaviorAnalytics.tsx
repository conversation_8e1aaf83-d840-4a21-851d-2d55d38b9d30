import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tabs,
  Progress,
  Tag,
  Space,
  Button,
  DatePicker,
  Select,
  Tooltip,
  Alert,
  Spin,
  Typography,
  Badge
} from 'antd'
import {
  UserOutlined,
  RiseOutlined,
  FallOutlined,
  <PERSON>Outlined,
  MessageOutlined,
  HeartOutlined,
  ShareAltOutlined,
  CalendarOutlined,
  MobileOutlined,
  DesktopOutlined,
  ReloadOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON>hartOutlined,
  Pie<PERSON>hartOutlined,
  FunnelPlotOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import EChartsComponent from '../../components/EChartsComponent'
import {
  createLineChartOption,
  createBarChartOption,
  createPieChartOption
} from '../../utils/chartOptions'
import {
  userBehaviorService,
  type UserBehaviorStats,
  type UserEngagementData,
  type FeatureUsageData,
  type UserRetentionData,
  type UserSegmentData,
  type UserJourneyData
} from '../../services/userBehaviorService'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select
const { TabPane } = Tabs

interface RealTimeData {
  onlineUsers: number
  recentPosts: number
  recentComments: number
  avgResponseTime: number
  serverLoad: number
}

const UserBehaviorAnalytics: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [behaviorStats, setBehaviorStats] = useState<UserBehaviorStats | null>(null)
  const [engagementTrend, setEngagementTrend] = useState<UserEngagementData[]>([])
  const [featureUsage, setFeatureUsage] = useState<FeatureUsageData[]>([])
  const [retentionData, setRetentionData] = useState<UserRetentionData[]>([])
  const [segmentData, setSegmentData] = useState<UserSegmentData[]>([])
  const [journeyData, setJourneyData] = useState<UserJourneyData[]>([])
  const [realTimeData, setRealTimeData] = useState<RealTimeData | null>(null)
  const [selectedTimeRange, setSelectedTimeRange] = useState(30)

  // 加载所有数据
  const loadAllData = async () => {
    setLoading(true)
    try {
      // 并行加载所有数据
      const [statsResult, trendResult, featureResult, retentionResult, segmentResult, journeyResult, realTimeResult] = await Promise.all([
        userBehaviorService.getUserBehaviorStats(),
        userBehaviorService.getUserEngagementTrend(selectedTimeRange),
        userBehaviorService.getFeatureUsageStats(),
        userBehaviorService.getUserRetentionData(),
        userBehaviorService.getUserSegmentData(),
        userBehaviorService.getUserJourneyData(),
        userBehaviorService.getRealTimeUserActivity()
      ])

      if (statsResult.success && statsResult.data) {
        setBehaviorStats(statsResult.data)
      }

      if (trendResult.success && trendResult.data) {
        setEngagementTrend(trendResult.data)
      }

      if (featureResult.success && featureResult.data) {
        setFeatureUsage(featureResult.data)
      }

      if (retentionResult.success && retentionResult.data) {
        setRetentionData(retentionResult.data)
      }

      if (segmentResult.success && segmentResult.data) {
        setSegmentData(segmentResult.data)
      }

      if (journeyResult.success && journeyResult.data) {
        setJourneyData(journeyResult.data)
      }

      if (realTimeResult.success && realTimeResult.data) {
        setRealTimeData(realTimeResult.data)
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAllData()
  }, [selectedTimeRange])

  // 实时数据自动刷新
  useEffect(() => {
    const interval = setInterval(async () => {
      const result = await userBehaviorService.getRealTimeUserActivity()
      if (result.success && result.data) {
        setRealTimeData(result.data)
      }
    }, 30000) // 每30秒刷新一次

    return () => clearInterval(interval)
  }, [])

  // 功能使用表格列定义
  const featureColumns: ColumnsType<FeatureUsageData> = [
    {
      title: '功能',
      dataIndex: 'feature',
      key: 'feature'
    },
    {
      title: '使用次数',
      dataIndex: 'usageCount',
      key: 'usageCount',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: '独立用户',
      dataIndex: 'uniqueUsers',
      key: 'uniqueUsers',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: '使用率',
      dataIndex: 'usageRate',
      key: 'usageRate',
      render: (value: number) => (
        <div>
          <Progress percent={value} size="small" />
          <Text type="secondary">{value.toFixed(1)}%</Text>
        </div>
      )
    }
  ]

  // 用户留存表格列定义
  const retentionColumns: ColumnsType<UserRetentionData> = [
    {
      title: '用户群组',
      dataIndex: 'cohort',
      key: 'cohort'
    },
    {
      title: '第0天',
      dataIndex: 'day0',
      key: 'day0',
      render: (value: number) => `${value}%`
    },
    {
      title: '第1天',
      dataIndex: 'day1',
      key: 'day1',
      render: (value: number) => (
        <Tag color={value > 70 ? 'green' : value > 50 ? 'orange' : 'red'}>
          {value}%
        </Tag>
      )
    },
    {
      title: '第7天',
      dataIndex: 'day7',
      key: 'day7',
      render: (value: number) => (
        <Tag color={value > 40 ? 'green' : value > 25 ? 'orange' : 'red'}>
          {value}%
        </Tag>
      )
    },
    {
      title: '第14天',
      dataIndex: 'day14',
      key: 'day14',
      render: (value: number) => (
        <Tag color={value > 30 ? 'green' : value > 20 ? 'orange' : 'red'}>
          {value}%
        </Tag>
      )
    },
    {
      title: '第30天',
      dataIndex: 'day30',
      key: 'day30',
      render: (value: number) => (
        <Tag color={value > 25 ? 'green' : value > 15 ? 'orange' : 'red'}>
          {value}%
        </Tag>
      )
    }
  ]

  // 用户分群表格列定义
  const segmentColumns: ColumnsType<UserSegmentData> = [
    {
      title: '用户分群',
      dataIndex: 'segment',
      key: 'segment'
    },
    {
      title: '用户数量',
      dataIndex: 'userCount',
      key: 'userCount',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => (
        <div>
          <Progress percent={value} size="small" />
          <Text type="secondary">{value}%</Text>
        </div>
      )
    },
    {
      title: '平均参与度',
      dataIndex: 'avgEngagement',
      key: 'avgEngagement',
      render: (value: number) => value.toFixed(1)
    },
    {
      title: '生命周期价值',
      dataIndex: 'avgLifetimeValue',
      key: 'avgLifetimeValue',
      render: (value: number) => `$${value.toFixed(2)}`
    }
  ]

  // 生成图表数据
  const generateChartOptions = () => {
    // 用户参与度趋势图
    const engagementChartData = {
      xAxisData: engagementTrend.map(item => item.date),
      series: [
        {
          name: '活跃用户',
          data: engagementTrend.map(item => item.activeUsers),
          color: '#1890ff'
        },
        {
          name: '新用户',
          data: engagementTrend.map(item => item.newUsers),
          color: '#52c41a'
        },
        {
          name: '回访用户',
          data: engagementTrend.map(item => item.returningUsers),
          color: '#722ed1'
        }
      ]
    }

    // 功能使用分布饼图
    const featureUsageChartData = {
      data: featureUsage.map(item => ({
        name: item.feature,
        value: item.usageCount
      }))
    }

    // 用户旅程漏斗图
    const journeyChartData = {
      data: journeyData.map(item => ({
        name: item.step,
        value: item.userCount
      }))
    }

    return {
      engagementOption: createLineChartOption(engagementChartData),
      featureUsageOption: createPieChartOption(featureUsageChartData),
      journeyOption: createBarChartOption({
        xAxisData: journeyData.map(item => item.step),
        series: [{
          name: '用户数',
          data: journeyData.map(item => item.userCount),
          color: '#1890ff'
        }]
      })
    }
  }

  const chartOptions = generateChartOptions()

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>用户行为分析</Title>
        <Space>
          <Select
            value={selectedTimeRange}
            onChange={setSelectedTimeRange}
            style={{ width: 120 }}
          >
            <Option value={7}>最近7天</Option>
            <Option value={30}>最近30天</Option>
            <Option value={90}>最近90天</Option>
          </Select>
          <Button icon={<ReloadOutlined />} onClick={loadAllData} loading={loading}>
            刷新数据
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        {/* 实时数据概览 */}
        {realTimeData && (
          <Alert
            message="实时数据"
            description={
              <Row gutter={16}>
                <Col span={6}>
                  <Badge status="processing" text={`在线用户: ${realTimeData.onlineUsers}`} />
                </Col>
                <Col span={6}>
                  <Badge status="success" text={`最近发帖: ${realTimeData.recentPosts}`} />
                </Col>
                <Col span={6}>
                  <Badge status="default" text={`最近评论: ${realTimeData.recentComments}`} />
                </Col>
                <Col span={6}>
                  <Badge status="warning" text={`服务器负载: ${realTimeData.serverLoad}%`} />
                </Col>
              </Row>
            }
            type="info"
            showIcon
            style={{ marginBottom: '24px' }}
          />
        )}

        <Tabs defaultActiveKey="overview">
          {/* 概览标签页 */}
          <TabPane tab="行为概览" key="overview">
            {behaviorStats && (
              <>
                {/* 活跃度统计 */}
                <Card title="用户活跃度" style={{ marginBottom: '24px' }}>
                  <Row gutter={16}>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="日活跃用户"
                        value={behaviorStats.dailyActiveUsers}
                        prefix={<UserOutlined />}
                        suffix="人"
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="周活跃用户"
                        value={behaviorStats.weeklyActiveUsers}
                        prefix={<UserOutlined />}
                        suffix="人"
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="月活跃用户"
                        value={behaviorStats.monthlyActiveUsers}
                        prefix={<UserOutlined />}
                        suffix="人"
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="平均使用时长"
                        value={behaviorStats.avgDailyUsage}
                        suffix="分钟"
                      />
                    </Col>
                  </Row>
                </Card>

                {/* 用户行为统计 */}
                <Card title="用户行为统计" style={{ marginBottom: '24px' }}>
                  <Row gutter={16}>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="发帖率"
                        value={behaviorStats.postCreationRate}
                        precision={1}
                        suffix="%"
                        prefix={<MessageOutlined />}
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="评论率"
                        value={behaviorStats.commentRate}
                        precision={1}
                        suffix="%"
                        prefix={<MessageOutlined />}
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="点赞率"
                        value={behaviorStats.likeRate}
                        precision={1}
                        suffix="%"
                        prefix={<HeartOutlined />}
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="签到率"
                        value={behaviorStats.checkinRate}
                        precision={1}
                        suffix="%"
                        prefix={<CalendarOutlined />}
                      />
                    </Col>
                  </Row>
                </Card>

                {/* 用户留存统计 */}
                <Card title="用户留存率" style={{ marginBottom: '24px' }}>
                  <Row gutter={16}>
                    <Col xs={24} sm={8}>
                      <Statistic
                        title="次日留存"
                        value={behaviorStats.dayOneRetention}
                        suffix="%"
                        valueStyle={{ color: behaviorStats.dayOneRetention > 60 ? '#3f8600' : '#cf1322' }}
                        prefix={behaviorStats.dayOneRetention > 60 ? <RiseOutlined /> : <FallOutlined />}
                      />
                    </Col>
                    <Col xs={24} sm={8}>
                      <Statistic
                        title="7日留存"
                        value={behaviorStats.daySevenRetention}
                        suffix="%"
                        valueStyle={{ color: behaviorStats.daySevenRetention > 40 ? '#3f8600' : '#cf1322' }}
                        prefix={behaviorStats.daySevenRetention > 40 ? <RiseOutlined /> : <FallOutlined />}
                      />
                    </Col>
                    <Col xs={24} sm={8}>
                      <Statistic
                        title="30日留存"
                        value={behaviorStats.dayThirtyRetention}
                        suffix="%"
                        valueStyle={{ color: behaviorStats.dayThirtyRetention > 25 ? '#3f8600' : '#cf1322' }}
                        prefix={behaviorStats.dayThirtyRetention > 25 ? <RiseOutlined /> : <FallOutlined />}
                      />
                    </Col>
                  </Row>
                </Card>

                {/* 设备分布 */}
                <Card title="设备分布">
                  <Row gutter={16}>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="移动端用户"
                        value={behaviorStats.mobileUsers}
                        prefix={<MobileOutlined />}
                        suffix="人"
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="桌面端用户"
                        value={behaviorStats.desktopUsers}
                        prefix={<DesktopOutlined />}
                        suffix="人"
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="iOS用户"
                        value={behaviorStats.iosUsers}
                        suffix="人"
                      />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Statistic
                        title="Android用户"
                        value={behaviorStats.androidUsers}
                        suffix="人"
                      />
                    </Col>
                  </Row>
                </Card>
              </>
            )}
          </TabPane>

          {/* 参与度趋势 */}
          <TabPane tab="参与度趋势" key="engagement">
            <Card
              title={
                <Space>
                  <LineChartOutlined />
                  <span>用户参与度趋势</span>
                </Space>
              }
              style={{ height: 500 }}
            >
              <EChartsComponent
                option={chartOptions.engagementOption}
                height={400}
                loading={loading}
              />
            </Card>
          </TabPane>

          {/* 功能使用分析 */}
          <TabPane tab="功能使用" key="features">
            <Row gutter={16}>
              <Col xs={24} lg={12}>
                <Card
                  title={
                    <Space>
                      <PieChartOutlined />
                      <span>功能使用分布</span>
                    </Space>
                  }
                  style={{ height: 400, marginBottom: 16 }}
                >
                  <EChartsComponent
                    option={chartOptions.featureUsageOption}
                    height={300}
                    loading={loading}
                  />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="功能使用详情" style={{ height: 400 }}>
                  <Table
                    columns={featureColumns}
                    dataSource={featureUsage}
                    rowKey="feature"
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* 用户留存分析 */}
          <TabPane tab="留存分析" key="retention">
            <Card title="用户留存率分析">
              <Table
                columns={retentionColumns}
                dataSource={retentionData}
                rowKey="cohort"
                pagination={false}
              />
            </Card>
          </TabPane>

          {/* 用户分群 */}
          <TabPane tab="用户分群" key="segments">
            <Card title="用户分群分析">
              <Table
                columns={segmentColumns}
                dataSource={segmentData}
                rowKey="segment"
                pagination={false}
              />
            </Card>
          </TabPane>

          {/* 用户旅程 */}
          <TabPane tab="用户旅程" key="journey">
            <Card
              title={
                <Space>
                  <FunnelPlotOutlined />
                  <span>用户转化漏斗</span>
                </Space>
              }
              style={{ height: 500 }}
            >
              <EChartsComponent
                option={chartOptions.journeyOption}
                height={400}
                loading={loading}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  )
}

export default UserBehaviorAnalytics