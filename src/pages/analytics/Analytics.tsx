import React, { useState, useContext, useMemo, useEffect } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Button, 
  DatePicker,
  Table,
  Tabs,
  Progress,
  Badge,
  Spin,
  Alert
} from 'antd'
import { 
  UserOutlined, 
  VideoCameraOutlined, 
  UploadOutlined, 
  DownloadOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  RiseOutlined,
  FallOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import { GlobalMessageContext } from '../../components/GlobalMessage'
import EChartsComponent from '../../components/EChartsComponent'
import { 
  createLineChartOption, 
  createBarChartOption, 
  createPieChartOption,
  createRadarChartOption
} from '../../utils/chartOptions'
import { statisticsService } from '../../services/statisticsService'
import type { UserStats, ContentStats } from '../../services/statisticsService'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { TabPane } = Tabs

// 统计数据接口
interface StatCardData {
  id: number;
  name: string;
  value: number | string;
  icon: React.ReactNode;
  change: number;
}

interface LevelDistribution {
  level: string;
  count: number;
  percent: number;
}

const levelColumns: TableProps<LevelDistribution>['columns'] = [
  {
    title: '等级',
    dataIndex: 'level',
    key: 'level',
  },
  {
    title: '用户数',
    dataIndex: 'count',
    key: 'count',
  },
  {
    title: '占比',
    dataIndex: 'percent',
    key: 'percent',
    render: (percent: number) => (
      <Progress percent={percent} size="small" />
    )
  },
]

const Analytics: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { message } = useContext(GlobalMessageContext)
  
  // 数据状态
  const [userStats, setUserStats] = useState<UserStats | null>(null)
  const [contentStats, setContentStats] = useState<ContentStats | null>(null)
  const [userGrowthData, setUserGrowthData] = useState<any>(null)
  const [levelDistribution, setLevelDistribution] = useState<LevelDistribution[]>([])

  // 生成用户统计卡片数据
  const generateUserStatsData = (stats: UserStats): StatCardData[] => {
    return [
      { id: 1, name: '总用户数', value: stats.totalUsers, icon: <UserOutlined />, change: 12 },
      { id: 2, name: '今日新增', value: stats.newUsersToday, icon: <UserOutlined />, change: 5 },
      { id: 3, name: '活跃用户', value: stats.activeUsers, icon: <UserOutlined />, change: 8 },
      { id: 4, name: 'VIP用户', value: stats.premiumUsers, icon: <UserOutlined />, change: 3 },
    ]
  }

  // 生成内容统计卡片数据
  const generateContentStatsData = (stats: ContentStats): StatCardData[] => {
    return [
      { id: 1, name: '总帖子数', value: stats.totalPosts, icon: <UploadOutlined />, change: 25 },
      { id: 2, name: '今日发布', value: stats.publishedPosts, icon: <UploadOutlined />, change: 7 },
      { id: 3, name: '总评论数', value: stats.totalComments, icon: <VideoCameraOutlined />, change: 15 },
      { id: 4, name: '待审核评论', value: stats.pendingComments, icon: <DownloadOutlined />, change: 22 },
    ]
  }

  // 生成用户行为统计数据
  const generateEngagementStatsData = (): StatCardData[] => {
    return [
      { id: 1, name: '平均留存率', value: '68%', icon: <UserOutlined />, change: 3 },
      { id: 2, name: '平均使用时长', value: '24分钟', icon: <VideoCameraOutlined />, change: 5 },
      { id: 3, name: '互动率', value: '42%', icon: <UploadOutlined />, change: 2 },
      { id: 4, name: '内容发布率', value: '35%', icon: <DownloadOutlined />, change: 1 },
    ]
  }

  // 图表数据
  const chartData = useMemo(() => {
    // 用户增长趋势数据
    const userGrowthChartData = userGrowthData || {
      xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
      series: [
        {
          name: '新增用户',
          data: [0, 0, 0, 0, 0, 0, 0],
          color: '#1890ff'
        }
      ]
    }

    // 内容互动趋势数据（暂时使用模拟数据）
    const contentInteractionData = {
      xAxisData: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      series: [
        {
          name: '帖子发布',
          data: [45, 52, 38, 65, 72, 88, 95],
          color: '#722ed1'
        },
        {
          name: '评论数',
          data: [120, 135, 98, 156, 189, 245, 268],
          color: '#fa8c16'
        }
      ]
    }

    // 用户活跃度分布数据（暂时使用模拟数据）
    const userActivityData = {
      xAxisData: ['非常活跃', '活跃', '一般', '不活跃', '沉默'],
      series: [
        {
          name: '用户数',
          data: [156, 289, 445, 234, 121],
          color: '#13c2c2'
        }
      ]
    }

    // 功能使用率数据（暂时使用模拟数据）
    const featureUsageData = {
      data: [
        { name: '社区互动', value: 35, color: '#1890ff' },
        { name: '内容发布', value: 25, color: '#52c41a' },
        { name: '任务完成', value: 20, color: '#722ed1' },
        { name: '签到打卡', value: 15, color: '#fa8c16' },
        { name: '其他功能', value: 5, color: '#eb2f96' }
      ]
    }

    return {
      userGrowthOption: createLineChartOption(userGrowthChartData),
      contentInteractionOption: createLineChartOption(contentInteractionData),
      userActivityOption: createBarChartOption(userActivityData),
      featureUsageOption: createPieChartOption(featureUsageData)
    }
  }, [userGrowthData])
  
  // 加载分析数据
  const loadAnalyticsData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // 获取用户统计数据
       const userStatsResult = await statisticsService.getUserStats()
       if (userStatsResult.success && userStatsResult.data) {
         setUserStats(userStatsResult.data)
         
         // 模拟等级分布数据（实际应该从数据库获取）
         const mockLevelDistribution: LevelDistribution[] = [
           { level: 'L1', count: Math.floor(userStatsResult.data.totalUsers * 0.4), percent: 40 },
           { level: 'L2', count: Math.floor(userStatsResult.data.totalUsers * 0.25), percent: 25 },
           { level: 'L3', count: Math.floor(userStatsResult.data.totalUsers * 0.2), percent: 20 },
           { level: 'L4', count: Math.floor(userStatsResult.data.totalUsers * 0.1), percent: 10 },
           { level: 'L5', count: Math.floor(userStatsResult.data.totalUsers * 0.05), percent: 5 },
         ]
         setLevelDistribution(mockLevelDistribution)
       }
       
       // 获取内容统计数据
       const contentStatsResult = await statisticsService.getContentStats()
       if (contentStatsResult.success && contentStatsResult.data) {
         setContentStats(contentStatsResult.data)
       }
       
       // 获取用户增长数据
       const growthDataResult = await statisticsService.getUserGrowthData()
       if (growthDataResult.success && growthDataResult.data) {
         setUserGrowthData(growthDataResult.data)
       }
      
      message.success('数据加载成功')
    } catch (error) {
      console.error('加载分析数据失败:', error)
      setError('数据加载失败，请稍后重试')
      message.error('数据加载失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }
  
  // 组件加载时获取数据
  useEffect(() => {
    loadAnalyticsData()
  }, [])

  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    loadAnalyticsData()
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>数据统计</Title>
      
      <Row justify="space-between" align="middle">
        <Col>
          <Text type="secondary">关键指标概览</Text>
        </Col>
        <Col>
          <Space>
            <RangePicker />
            <QuickActions 
              onRefresh={handleRefresh}
              showRefresh
              refreshLoading={loading}
            />
          </Space>
        </Col>
      </Row>
      
      {error && (
        <Alert 
          message="错误" 
          description={error} 
          type="error" 
          showIcon 
          style={{ marginTop: 16 }}
        />
      )}
      
      <Spin spinning={loading}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="用户数据" key="1">
            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              {userStats && generateUserStatsData(userStats).map(stat => (
                <Col xs={24} sm={12} md={6} lg={6} xl={6} key={stat.id}>
                  <Card style={{ height: '100%' }}>
                    <Statistic 
                      title={stat.name} 
                      value={stat.value} 
                      prefix={stat.icon}
                      suffix={
                        <span style={{ fontSize: '14px', color: stat.change >= 0 ? '#52c41a' : '#ff4d4f' }}>
                          {stat.change >= 0 ? <RiseOutlined /> : <FallOutlined />} {Math.abs(stat.change)}%
                        </span>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
            
            <Card 
              title={
                <Space>
                  <BarChartOutlined />
                  <span>用户增长趋势</span>
                </Space>
              }
              extra={<a href="#">查看更多</a>}
              style={{ marginTop: 16, height: 400 }}
            >
              <EChartsComponent 
                option={chartData.userGrowthOption}
                height={320}
                loading={loading}
              />
            </Card>
          </TabPane>
          
          <TabPane tab="内容数据" key="2">
            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              {contentStats && generateContentStatsData(contentStats).map(stat => (
                <Col xs={24} sm={12} md={6} lg={6} xl={6} key={stat.id}>
                  <Card style={{ height: '100%' }}>
                    <Statistic 
                      title={stat.name} 
                      value={stat.value} 
                      prefix={stat.icon}
                      suffix={
                        <span style={{ fontSize: '14px', color: stat.change >= 0 ? '#52c41a' : '#ff4d4f' }}>
                          {stat.change >= 0 ? <RiseOutlined /> : <FallOutlined />} {Math.abs(stat.change)}%
                        </span>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
            
            <Card 
              title={
                <Space>
                  <LineChartOutlined />
                  <span>内容互动趋势</span>
                </Space>
              }
              extra={<a href="#">查看更多</a>}
              style={{ marginTop: 16, height: 400 }}
            >
              <EChartsComponent 
                option={chartData.contentInteractionOption}
                height={320}
                loading={loading}
              />
            </Card>
          </TabPane>
          
          <TabPane tab="用户行为" key="3">
            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              {generateEngagementStatsData().map(stat => (
                <Col xs={24} sm={12} md={6} lg={6} xl={6} key={stat.id}>
                  <Card style={{ height: '100%' }}>
                    <Statistic 
                      title={stat.name} 
                      value={stat.value} 
                      prefix={stat.icon}
                      suffix={
                        <span style={{ fontSize: '14px', color: stat.change >= 0 ? '#52c41a' : '#ff4d4f' }}>
                          {stat.change >= 0 ? <RiseOutlined /> : <FallOutlined />} {Math.abs(stat.change)}%
                        </span>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
            
            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card 
                  title={
                    <Space>
                      <BarChartOutlined />
                      <span>用户活跃度分布</span>
                    </Space>
                  }
                  style={{ height: 400 }}
                >
                  <EChartsComponent 
                    option={chartData.userActivityOption}
                    height={320}
                    loading={loading}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                <Card 
                  title={
                    <Space>
                      <PieChartOutlined />
                      <span>功能使用率</span>
                    </Space>
                  }
                  style={{ height: 400 }}
                >
                  <EChartsComponent 
                    option={chartData.featureUsageOption}
                    height={320}
                    loading={loading}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Spin>
    </Space>
  )
}

export default Analytics