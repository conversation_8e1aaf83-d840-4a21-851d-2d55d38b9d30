import React, { useState, useContext } from 'react'
import { 
  Space, 
  Typography, 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Switch,
  Upload,
  Tabs,
  Statistic,
  Progress,
  Dropdown,
  Menu,
  Popconfirm,
  Spin,
  Alert
} from 'antd'
import { 
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  UserOutlined,
  TrophyOutlined,
  Bar<PERSON>hartOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { TableProps } from 'antd'
import SearchFilter from '../../components/SearchFilter'
import QuickActions from '../../components/QuickActions'
import RichTextEditor from '../../components/RichTextEditor'
import { GlobalMessageContext } from '../../components/GlobalMessage'

const { Title } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface AchievementDataType {
  id: number
  name: string
  description: string
  category: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  points: number
  isActive: boolean
  unlockCount: number
}

interface UserAchievementDataType {
  id: number
  userId: number
  username: string
  achievementName: string
  unlockedAt: string
  progress: number
}

const achievementData: AchievementDataType[] = [
  { 
    id: 1, 
    name: '坚持不懈', 
    description: '连续打卡7天', 
    category: 'checkin',
    rarity: 'common',
    points: 50,
    isActive: true,
    unlockCount: 124
  },
  { 
    id: 2, 
    name: '社交达人', 
    description: '发布10条评论', 
    category: 'social',
    rarity: 'common',
    points: 30,
    isActive: true,
    unlockCount: 86
  },
  { 
    id: 3, 
    name: '创作新星', 
    description: '发布5篇原创文章', 
    category: 'creation',
    rarity: 'rare',
    points: 100,
    isActive: true,
    unlockCount: 24
  },
  { 
    id: 4, 
    name: '社区领袖', 
    description: '获得100个点赞', 
    category: 'social',
    rarity: 'epic',
    points: 200,
    isActive: true,
    unlockCount: 8
  },
  { 
    id: 5, 
    name: '传奇人物', 
    description: '心流值达到10000', 
    category: 'milestone',
    rarity: 'legendary',
    points: 500,
    isActive: true,
    unlockCount: 1
  },
]

const userAchievementData: UserAchievementDataType[] = [
  { 
    id: 1, 
    userId: 1,
    username: 'test09',
    achievementName: '坚持不懈',
    unlockedAt: '2023-06-01',
    progress: 100
  },
  { 
    id: 2, 
    userId: 2,
    username: '测试用户1',
    achievementName: '坚持不懈',
    unlockedAt: '2023-06-02',
    progress: 100
  },
  { 
    id: 3, 
    userId: 3,
    username: '活跃用户2',
    achievementName: '社交达人',
    unlockedAt: '2023-06-03',
    progress: 100
  },
  { 
    id: 4, 
    userId: 4,
    username: '高级用户3',
    achievementName: '创作新星',
    unlockedAt: '2023-06-04',
    progress: 100
  },
]

const achievementColumns: TableProps<AchievementDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '成就名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    render: (description: string) => (
      <div style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {description}
      </div>
    )
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    render: (category: string) => {
      const categoryMap: Record<string, string> = {
        'checkin': '打卡',
        'social': '社交',
        'creation': '创作',
        'milestone': '里程碑'
      }
      
      return categoryMap[category] || category
    }
  },
  {
    title: '稀有度',
    dataIndex: 'rarity',
    key: 'rarity',
    render: (rarity: string) => {
      const rarityMap: Record<string, { text: string, color: string }> = {
        'common': { text: '普通', color: 'default' },
        'rare': { text: '稀有', color: 'blue' },
        'epic': { text: '史诗', color: 'purple' },
        'legendary': { text: '传说', color: 'gold' }
      }
      
      const rar = rarityMap[rarity] || { text: rarity, color: 'default' }
      return <Tag color={rar.color}>{rar.text}</Tag>
    }
  },
  {
    title: '积分',
    dataIndex: 'points',
    key: 'points',
  },
  {
    title: '状态',
    dataIndex: 'isActive',
    key: 'isActive',
    render: (isActive: boolean, record: AchievementDataType) => (
      <Switch
        checked={isActive}
        onChange={(checked) => {
          // 在实际应用中，这里会调用更新API
          message.success(`成就 "${record.name}" 已${checked ? '启用' : '禁用'}`)
        }}
        onClick={() => {}}
        loading={switchLoading}
      />
    ),
    onFilter: (value: string | number | boolean, record: AchievementDataType) => 
      record.isActive === value,
    filters: [
      { text: '启用', value: true },
      { text: '禁用', value: false }
    ],
    render: (isActive: boolean) => (
      <Tag color={isActive ? 'success' : 'default'}>
        {isActive ? '启用' : '禁用'}
      </Tag>
    )
  },
  {
    title: '解锁人数',
    dataIndex: 'unlockCount',
    key: 'unlockCount',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    render: (_, record) => (
      <Space size="middle">
        <Button type="link" icon={<EyeOutlined />} size="small" onClick={() => handleViewAchievement(record)}>查看</Button>
        <Button type="link" icon={<EditOutlined />} size="small" onClick={() => handleEditAchievement(record)}>编辑</Button>
        <Popconfirm
          title={record.isActive ? "确定禁用此成就吗？" : "确定启用此成就吗？"}
          description={record.isActive ? "禁用后用户将无法再获得此成就" : "启用后用户可以获得此成就"}
          onConfirm={() => {
            // 在实际应用中，这里会调用更新API
            message.success(`成就 "${record.name}" 已${record.isActive ? '禁用' : '启用'}`)
          }}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" icon={record.isActive ? <TrophyOutlined /> : <TrophyOutlined />} size="small">
            {record.isActive ? '禁用' : '启用'}
          </Button>
        </Popconfirm>
        <Popconfirm
          title="确定删除此成就吗？"
          description="删除后将无法恢复，请确认操作"
          onConfirm={() => {
            // 在实际应用中，这里会调用删除API
            message.success(`成就 "${record.name}" 删除成功`)
          }}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" icon={<DeleteOutlined />} size="small" danger>
            删除
          </Button>
        </Popconfirm>
      </Space>
    ),
  },
]

const userAchievementColumns: TableProps<UserAchievementDataType>['columns'] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '用户ID',
    dataIndex: 'userId',
    key: 'userId',
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '成就名称',
    dataIndex: 'achievementName',
    key: 'achievementName',
  },
  {
    title: '解锁时间',
    dataIndex: 'unlockedAt',
    key: 'unlockedAt',
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    render: (progress: number) => (
      <Progress percent={progress} size="small" />
    )
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 150,
    render: (_, record) => (
      <Space size="middle">
        <Button 
          type="link" 
          icon={<EyeOutlined />} 
          size="small"
          onClick={() => message.info(`查看用户成就: ${record.achievementName}`)}
        >
          详情
        </Button>
        <Button 
          type="link" 
          icon={<EditOutlined />} 
          size="small"
          onClick={() => message.info(`编辑用户成就: ${record.achievementName}`)}
        >
          编辑
        </Button>
      </Space>
    ),
  },
  {
    title: '筛选',
    key: 'filter',
    width: 100,
    onFilter: (value: string | number | boolean, record: UserAchievementDataType) => 
      record.progress === value,
    filters: [
      { text: '已完成', value: 100 },
      { text: '进行中', value: 0 },
    ],
    render: (text, record: UserAchievementDataType) => (
      <Tag color={record.progress === 100 ? 'success' : 'processing'}>
        {record.progress === 100 ? '已完成' : '进行中'}
      </Tag>
    ),
  },
]

const AchievementList: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('achievements')
  const [loading, setLoading] = useState(false)
  const [switchLoading, setSwitchLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form] = Form.useForm()
  const { message } = useContext(GlobalMessageContext)
  
  const showModal = () => {
    setIsModalVisible(true)
  }
  
  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('Received values of form: ', values)
      message.success('成就创建成功')
      setIsModalVisible(false)
      form.resetFields()
    }).catch(info => {
      console.log('Validate Failed:', info)
      message.error('表单验证失败')
    })
  }
  
  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }
  
  const handleSearch = () => {
    message.success('搜索完成')
  }
  
  const handleRefresh = () => {
    setLoading(true)
    setError(null)
    // 模拟API调用
    setTimeout(() => {
      // 模拟随机错误
      if (Math.random() > 0.8) {
        setError('数据加载失败，请稍后重试')
        message.error('数据加载失败，请稍后重试')
      } else {
        message.success('数据刷新成功')
      }
      setLoading(false)
    }, 1000)
  }
  
  const handleAddAchievement = () => {
    showModal()
  }

  // 查看成就详情
  const handleViewAchievement = (record: AchievementDataType) => {
    Modal.info({
      title: '成就详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <strong>成就名称：</strong>{record.name}
            </Col>
            <Col span={24}>
              <strong>描述：</strong>{record.description}
            </Col>
            <Col span={12}>
              <strong>分类：</strong>
              <Tag color={record.category === 'checkin' ? 'blue' : record.category === 'social' ? 'green' : record.category === 'creation' ? 'purple' : 'orange'}>
                {record.category === 'checkin' ? '打卡' : record.category === 'social' ? '社交' : record.category === 'creation' ? '创作' : '里程碑'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>稀有度：</strong>
              <Tag color={record.rarity === 'common' ? 'default' : record.rarity === 'rare' ? 'blue' : record.rarity === 'epic' ? 'purple' : 'gold'}>
                {record.rarity === 'common' ? '普通' : record.rarity === 'rare' ? '稀有' : record.rarity === 'epic' ? '史诗' : '传说'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>积分：</strong>{record.points}
            </Col>
            <Col span={12}>
              <strong>状态：</strong>
              <Tag color={record.isActive ? 'success' : 'default'}>
                {record.isActive ? '启用' : '禁用'}
              </Tag>
            </Col>
            <Col span={12}>
              <strong>解锁人数：</strong>{record.unlockCount}
            </Col>
          </Row>
        </div>
      ),
    });
  };

  // 编辑成就
  const handleEditAchievement = (record: AchievementDataType) => {
    form.setFieldsValue({
      name: record.name,
      description: record.description,
      category: record.category,
      rarity: record.rarity,
      points: record.points,
      isActive: record.isActive
    });
    setIsModalVisible(true);
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Title level={3}>成就系统</Title>
      
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<Space><TrophyOutlined />成就管理</Space>} key="achievements">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  onAdd={handleAddAchievement}
                  showRefresh
                  showAdd
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索成就名称/描述"
              filters={[
                {
                  key: 'category',
                  label: '成就分类',
                  type: 'select',
                  options: [
                    { value: 'checkin', label: '打卡' },
                    { value: 'social', label: '社交' },
                    { value: 'creation', label: '创作' },
                    { value: 'milestone', label: '里程碑' }
                  ]
                },
                {
                  key: 'rarity',
                  label: '稀有度',
                  type: 'select',
                  options: [
                    { value: 'common', label: '普通' },
                    { value: 'rare', label: '稀有' },
                    { value: 'epic', label: '史诗' },
                    { value: 'legendary', label: '传说' }
                  ]
                },
                {
                  key: 'isActive',
                  label: '状态',
                  type: 'select',
                  options: [
                    { value: 'true', label: '启用' },
                    { value: 'false', label: '禁用' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={achievementColumns} 
                dataSource={achievementData} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`,
                  defaultPageSize: 20
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
          
          <TabPane tab={<Space><UserOutlined />用户成就</Space>} key="userAchievements">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <QuickActions 
                  onRefresh={handleRefresh}
                  showRefresh
                  refreshLoading={loading}
                />
              </Col>
            </Row>
            
            <SearchFilter 
              onSearch={handleSearch}
              onRefresh={handleRefresh}
              placeholder="搜索用户名/成就名称"
              filters={[
                {
                  key: 'category',
                  label: '成就分类',
                  type: 'select',
                  options: [
                    { value: 'checkin', label: '打卡' },
                    { value: 'social', label: '社交' },
                    { value: 'creation', label: '创作' },
                    { value: 'milestone', label: '里程碑' }
                  ]
                }
              ]}
              loading={loading}
            />
            
            {error && (
              <Alert 
                message="错误" 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
            
            <Spin spinning={loading}>
              <Table 
                columns={userAchievementColumns} 
                dataSource={userAchievementData} 
                pagination={{ 
                  showSizeChanger: true, 
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total) => `共 ${total} 条记录`
                }}
                scroll={{ x: 1200 }}
                style={{ marginTop: 16 }}
              />
            </Spin>
          </TabPane>
          
          <TabPane tab={<Space><BarChartOutlined />统计分析</Space>} key="statistics">
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="总成就数" value={24} prefix={<TrophyOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="已解锁次数" value={324} prefix={<UserOutlined />} />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="最热门成就" value="坚持不懈" />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                <Card>
                  <Statistic title="最高稀有度成就" value="传奇人物" />
                </Card>
              </Col>
            </Row>
            
            <Card title="成就解锁分布">
              <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <img src="https://placehold.co/600x300" alt="成就解锁分布图表" />
              </div>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
      
      <Modal
        title="新增成就"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item 
            name="name" 
            label="成就名称" 
            rules={[{ required: true, message: '请输入成就名称' }]}
          >
            <Input placeholder="请输入成就名称" />
          </Form.Item>
          <Form.Item 
            name="description" 
            label="描述" 
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <RichTextEditor placeholder="请输入描述" style={{ minHeight: '120px' }} />
          </Form.Item>
          <Form.Item 
            name="category" 
            label="分类" 
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              <Option value="checkin">打卡</Option>
              <Option value="social">社交</Option>
              <Option value="creation">创作</Option>
              <Option value="milestone">里程碑</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="rarity" 
            label="稀有度" 
            rules={[{ required: true, message: '请选择稀有度' }]}
          >
            <Select placeholder="请选择稀有度">
              <Option value="common">普通</Option>
              <Option value="rare">稀有</Option>
              <Option value="epic">史诗</Option>
              <Option value="legendary">传说</Option>
            </Select>
          </Form.Item>
          <Form.Item 
            name="points" 
            label="奖励积分" 
            rules={[{ required: true, message: '请输入奖励积分' }]}
          >
            <Input type="number" placeholder="请输入奖励积分" />
          </Form.Item>
          <Form.Item name="isActive" label="是否启用" valuePropName="checked">
            <Switch defaultChecked />
          </Form.Item>
          <Form.Item 
            name="icon" 
            label="成就图标" 
            valuePropName="fileList"
          >
            <Upload beforeUpload={() => false}>
              <Button icon={<UploadOutlined />}>点击上传</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default AchievementList