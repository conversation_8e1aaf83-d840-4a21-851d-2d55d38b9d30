import { createBrowserRouter, Navigate } from "react-router-dom";
import App from "../App";
import Layout from "../components/Layout";
import ProtectedRoute from "../components/ProtectedRoute";
import Unauthorized from "../pages/Unauthorized";

import Dashboard from "../pages/Dashboard";
import Profile from "../pages/Profile";
import UserList from "../pages/users/UserList";
import CreateUser from "../pages/users/CreateUser";
import PostList from "../pages/content/PostList";
import CheckinList from "../pages/checkin/CheckinList";
import CheckinDetail from "../pages/checkin/CheckinDetail";
import CheckinEdit from "../pages/checkin/CheckinEdit";
import AchievementList from "../pages/achievements/AchievementList";
import TaskList from "../pages/tasks/TaskList";
import CommunityList from "../pages/community/CommunityList";
import Analytics from "../pages/analytics/Analytics";
import Settings from "../pages/settings/Settings";
import ChallengeList from "../pages/challenges/ChallengeList";
import InviteList from "../pages/invites/InviteList";
import NotificationList from "../pages/notifications/NotificationList";
import NotificationDemo from "../pages/notifications/NotificationDemo";
import AdminList from "../pages/admin/AdminList";
import OperationLog from "../pages/admin/OperationLog";
import Login from "../pages/Login";

// 新增页面组件导入
import FlowManagement from "../pages/flow/FlowManagement";
import UserMilestones from "../pages/milestones/UserMilestones";
import ArticleManagement from "../pages/articles/ArticleManagement";
import DataExport from "../pages/export/DataExport";
import AppreciationManagement from "../pages/appreciation/AppreciationManagement";
import SubscriptionManagement from "../pages/subscription/SubscriptionManagement";
import PartnerManagement from "../pages/partner/PartnerManagement";
import PaymentStatistics from "../pages/payment/PaymentStatistics";
import ContentLibraryManagement from "../pages/content-library/ContentLibraryManagement";

export const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children: [
      {
        path: "/",
        element: <ProtectedRoute><Layout /></ProtectedRoute>,
        children: [
          {
            index: true,
            element: <Dashboard />,
          },
          {
            path: "dashboard",
            element: <Dashboard />,
          },
          {
            path: "profile",
            element: <Profile />,
          },
          {
            path: "users",
            element: <UserList />,
          },
          {
            path: "users/create",
            element: <CreateUser />,
          },
          {
            path: "content",
            element: <PostList />,
          },
          {
            path: "community",
            element: <CommunityList />,
          },
          {
            path: "checkin",
            element: <CheckinList />,
          },
          {
            path: "checkin/detail/:id",
            element: <CheckinDetail />,
          },
          {
            path: "checkin/edit/:id",
            element: <CheckinEdit />,
          },
          {
            path: "tasks",
            element: <TaskList />,
          },
          {
            path: "achievements",
            element: <AchievementList />,
          },
          {
            path: "challenges",
            element: <ChallengeList />,
          },
          {
            path: "invites",
            element: <InviteList />,
          },
          {
            path: "notifications",
            element: <NotificationList />,
          },
          {
            path: "notifications/demo",
            element: <NotificationDemo />,
          },
          {
            path: "admin",
            element: <AdminList />,
          },
          {
            path: "logs",
            element: <OperationLog />,
          },
          {
            path: "analytics",
            element: <Analytics />,
          },
          {
            path: "settings",
            element: <Settings />,
          },
          {
            path: "flow",
            element: <FlowManagement />,
          },
          {
            path: "milestones",
            element: <UserMilestones />,
          },
          {
            path: "articles",
            element: <ArticleManagement />,
          },
          {
            path: "export",
            element: <DataExport />,
          },
          {
            path: "appreciation",
            element: <AppreciationManagement />,
          },
          {
            path: "subscription",
            element: <SubscriptionManagement />,
          },
          {
            path: "partner",
            element: <PartnerManagement />,
          },
          {
            path: "payment",
            element: <PaymentStatistics />,
          },
          {
            path: "content-library",
            element: <ContentLibraryManagement />,
          },
        ],
      },
      {
        path: "login",
        element: <Login />,
      },
    ],
  },
]);