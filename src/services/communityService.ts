import { supabase } from '../utils/supabase';

export interface CommunityPost {
  id: string;
  title: string;
  content: string;
  user_id: string;
  category_id?: string | null;
  status: 'published' | 'pending' | 'rejected';
  view_count: number;
  like_count: number;
  comment_count: number;
  is_featured: boolean;
  rating: number;
  created_at: string;
  updated_at?: string | null;
}

export interface CommunityComment {
  id: string;
  post_id: string;
  user_id: string;
  content: string;
  status: 'approved' | 'pending' | 'rejected';
  created_at: string;
  updated_at?: string | null;
}

export interface CommunityCategory {
  id: string;
  name: string;
  color: string;
  description?: string | null;
  created_at: string;
}

export const communityService = {
  // 社区帖子相关方法
  async getAllPosts(): Promise<{ success: boolean; data?: any[] | null; error?: Error | null }> {
    try {
      // 使用profiles表模拟社区帖子数据
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;

      // 模拟帖子数据
      const mockPosts = profiles?.map((profile: any, index: number) => ({
        id: `post_${profile.id}`,
        title: `用户 ${profile.username || profile.email} 的分享`,
        content: `这是来自用户的社区分享内容...`,
        user_id: profile.id,
        category_id: null,
        status: 'published' as const,
        view_count: Math.floor(Math.random() * 100),
        like_count: Math.floor(Math.random() * 50),
        comment_count: Math.floor(Math.random() * 20),
        is_featured: index < 3,
        rating: Math.floor(Math.random() * 5) + 1,
        created_at: profile.created_at,
        updated_at: profile.updated_at,
        profiles: { username: profile.username || profile.email, avatar_url: undefined },
        categories: null
      })) || [];

      return { success: true, data: mockPosts };
    } catch (error) {
      console.error('Error fetching community posts:', error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async getPostById(id: string): Promise<{ success: boolean; data?: any | null; error?: Error | null }> {
    try {
      // 从id中提取用户ID
       const userId = id.replace('post_', '');
       const { data: profile, error } = await supabase
         .from('profiles')
         .select('*')
         .eq('id', userId)
         .single();

       if (error) throw error;

       // 模拟单个帖子数据
       const mockPost = {
         id: `post_${(profile as any).id}`,
         title: `用户 ${(profile as any).username || (profile as any).email} 的分享`,
         content: `这是来自用户的详细社区分享内容...`,
         user_id: (profile as any).id,
         category_id: null,
         status: 'published' as const,
         view_count: Math.floor(Math.random() * 100),
         like_count: Math.floor(Math.random() * 50),
         comment_count: Math.floor(Math.random() * 20),
         is_featured: false,
         rating: Math.floor(Math.random() * 5) + 1,
         created_at: (profile as any).created_at,
         updated_at: (profile as any).updated_at,
         profiles: { username: (profile as any).username || (profile as any).email, avatar_url: undefined },
         categories: null,
         comments: []
       };

      return { success: true, data: mockPost };
    } catch (error) {
      console.error(`Error fetching community post ${id}:`, error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async createPost(post: Omit<CommunityPost, 'id' | 'created_at' | 'updated_at' | 'view_count' | 'like_count' | 'comment_count' | 'rating'>): Promise<{ success: boolean; data?: CommunityPost | null; error?: Error | null }> {
    try {
      // 模拟创建帖子，实际上不存储到数据库
      const mockPost: CommunityPost = {
        id: `post_${Date.now()}`,
        ...post,
        view_count: 0,
        like_count: 0,
        comment_count: 0,
        rating: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      return { success: true, data: mockPost };
    } catch (error) {
      console.error('Error creating community post:', error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async updatePost(id: string, updates: Partial<CommunityPost>): Promise<{ success: boolean; data?: CommunityPost | null; error?: Error | null }> {
    try {
      // 模拟更新帖子
      const mockPost: CommunityPost = {
        id,
        title: updates.title || '更新的帖子标题',
        content: updates.content || '更新的帖子内容',
        user_id: updates.user_id || 'mock_user_id',
        category_id: updates.category_id || null,
        status: updates.status || 'published',
        view_count: updates.view_count || 0,
        like_count: updates.like_count || 0,
        comment_count: updates.comment_count || 0,
        is_featured: updates.is_featured || false,
        rating: updates.rating || 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      return { success: true, data: mockPost };
    } catch (error) {
      console.error(`Error updating community post ${id}:`, error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async deletePost(id: string): Promise<{ success: boolean; error?: Error | null }> {
    try {
      // 模拟删除帖子
      return { success: true };
    } catch (error) {
      console.error(`Error deleting community post ${id}:`, error);
      return { success: false, error: error as Error };
    }
  },

  async searchPosts(query: string): Promise<{ success: boolean; data?: any[] | null; error?: Error | null }> {
    try {
      // 使用profiles表模拟搜索结果
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('*')
        .or(`username.ilike.%${query}%,email.ilike.%${query}%`)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      // 模拟搜索结果
      const mockPosts = profiles?.map((profile: any, index: number) => ({
        id: `post_${profile.id}`,
        title: `搜索结果: ${profile.username || profile.email} 的分享`,
        content: `包含关键词 "${query}" 的内容...`,
        user_id: profile.id,
        category_id: null,
        status: 'published' as const,
        view_count: Math.floor(Math.random() * 100),
        like_count: Math.floor(Math.random() * 50),
        comment_count: Math.floor(Math.random() * 20),
        is_featured: false,
        rating: Math.floor(Math.random() * 5) + 1,
        created_at: profile.created_at,
        updated_at: profile.updated_at,
        profiles: { username: profile.username || profile.email, avatar_url: undefined },
        categories: null
      })) || [];

      return { success: true, data: mockPosts };
    } catch (error) {
      console.error('Error searching community posts:', error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async updatePostStatus(id: string, status: 'published' | 'pending' | 'rejected'): Promise<{ success: boolean; error?: Error | null }> {
    return this.updatePost(id, { status });
  },

  async toggleFeatured(id: string, isFeatured: boolean): Promise<{ success: boolean; error?: Error | null }> {
    return this.updatePost(id, { is_featured: isFeatured });
  },

  async updateRating(id: string, rating: number): Promise<{ success: boolean; error?: Error | null }> {
    return this.updatePost(id, { rating });
  },

  // 评论相关方法 - 安全降级
  async getCommentsByPostId(postId: string): Promise<{ success: boolean; data?: CommunityComment[] | null; error?: Error | null }> {
    try {
      console.warn('community_comments 表未启用：getCommentsByPostId 返回空数组');
      return { success: true, data: [] };
    } catch (error) {
      return { success: false, error: error as Error, data: [] };
    }
  },

  async getAllComments(): Promise<{ success: boolean; data?: CommunityComment[] | null; error?: Error | null }> {
    try {
      console.warn('community_comments 表未启用：getAllComments 返回空数组');
      return { success: true, data: [] };
    } catch (error) {
      return { success: false, error: error as Error, data: [] };
    }
  },

  async createComment(comment: Omit<CommunityComment, 'id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; data?: CommunityComment | null; error?: Error | null }> {
    try {
      console.warn('community_comments 表未启用：createComment 忽略操作');
      return { success: true, data: null };
    } catch (error) {
      return { success: false, error: error as Error, data: null };
    }
  },

  async updateComment(id: string, updates: Partial<CommunityComment>): Promise<{ success: boolean; data?: CommunityComment | null; error?: Error | null }> {
    try {
      console.warn('community_comments 表未启用：updateComment 忽略操作');
      return { success: true, data: null };
    } catch (error) {
      return { success: false, error: error as Error, data: null };
    }
  },

  async deleteComment(id: string): Promise<{ success: boolean; error?: Error | null }> {
    try {
      console.warn('community_comments 表未启用：deleteComment 忽略操作');
      return { success: true };
    } catch (error) {
      return { success: false, error: error as Error };
    }
  },

  async updateCommentStatus(id: string, status: 'approved' | 'pending' | 'rejected'): Promise<{ success: boolean; error?: Error | null }> {
    console.warn('community_comments 表未启用：updateCommentStatus 忽略操作');
    return { success: true };
  },

  // 分类相关方法 - 安全降级
  async getAllCategories(): Promise<{ success: boolean; data?: CommunityCategory[] | null; error?: Error | null }> {
    try {
      console.warn('community categories 未启用：getAllCategories 返回空数组');
      return { success: true, data: [] };
    } catch (error) {
      return { success: false, error: error as Error, data: [] };
    }
  },

  async createCategory(name: string, color: string = 'blue', description?: string): Promise<{ success: boolean; data?: CommunityCategory | null; error?: Error | null }> {
    console.warn('community categories 未启用：createCategory 忽略操作');
    return { success: false, error: new Error('community categories not available'), data: null };
  },

  async updateCategory(id: string, updates: Partial<CommunityCategory>): Promise<{ success: boolean; data?: CommunityCategory | null; error?: Error | null }> {
    console.warn('community categories 未启用：updateCategory 忽略操作');
    return { success: false, error: new Error('community categories not available'), data: null };
  },

  async deleteCategory(id: string): Promise<{ success: boolean; error?: Error | null }> {
    console.warn('community categories 未启用：deleteCategory 忽略操作');
    return { success: false, error: new Error('community categories not available') };
  },

  // 统计相关方法
  async getCommunityStats(): Promise<{ success: boolean; data?: any; error?: Error | null }> {
    try {
      // 获取帖子总数
      const { count: totalPosts, error: postsError } = await supabase
        .from('community_posts')
        .select('*', { count: 'exact', head: true });

      if (postsError) throw postsError;

      // 获取今日新增帖子数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const { count: newPosts, error: newPostsError } = await supabase
        .from('community_posts')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());

      if (newPostsError) throw newPostsError;

      // 评论相关统计返回 0
      const totalComments = 0
      const pendingComments = 0

      return {
        success: true,
        data: {
          totalPosts,
          newPosts,
          totalComments,
          pendingComments
        }
      };
    } catch (error) {
      console.error('Error fetching community stats:', error);
      return { success: false, error: error as Error };
    }
  }
};