import { supabase } from '../utils/supabase'

// 赞赏记录接口
export interface AppreciationRecord {
  id: string
  from_user_id: string
  to_user_id: string
  target_type: 'article' | 'comment' | 'user'
  target_id: string
  amount: number
  message?: string
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  payment_method: 'wechat' | 'alipay' | 'balance'
  transaction_id?: string
  created_at: string
  updated_at: string
  from_user?: {
    id: string
    username: string
    avatar?: string
  }
  to_user?: {
    id: string
    username: string
    avatar?: string
  }
}

// 赞赏统计接口
export interface AppreciationStats {
  total_amount: number
  total_count: number
  today_amount: number
  today_count: number
  this_month_amount: number
  this_month_count: number
  avg_amount: number
  top_receivers: Array<{
    user_id: string
    username: string
    avatar?: string
    total_amount: number
    count: number
  }>
  top_givers: Array<{
    user_id: string
    username: string
    avatar?: string
    total_amount: number
    count: number
  }>
  payment_method_stats: Array<{
    method: string
    amount: number
    count: number
    percentage: number
  }>
}

// 赞赏设置接口
export interface AppreciationSettings {
  id: string
  min_amount: number
  max_amount: number
  default_amounts: number[]
  commission_rate: number
  auto_withdraw_threshold: number
  enable_anonymous: boolean
  enable_message: boolean
  created_at: string
  updated_at: string
}

// 用户赞赏余额接口
export interface UserAppreciationBalance {
  user_id: string
  balance: number
  total_received: number
  total_given: number
  pending_amount: number
  last_updated: string
}

class AppreciationService {
  // 获取赞赏记录列表
  async getAppreciationRecords(params: {
    page?: number
    pageSize?: number
    status?: string
    target_type?: string
    from_user_id?: string
    to_user_id?: string
    start_date?: string
    end_date?: string
  }) {
    const { page = 1, pageSize = 20, ...filters } = params
    const offset = (page - 1) * pageSize

    let query = supabase
      .from('appreciation_records')
      .select(`
        *,
        from_user:profiles!from_user_id(
          id,
          username,
          avatar
        ),
        to_user:profiles!to_user_id(
          id,
          username,
          avatar
        )
      `)
      .range(offset, offset + pageSize - 1)
      .order('created_at', { ascending: false })

    // 应用筛选条件
    if (filters.status) {
      query = query.eq('status', filters.status)
    }
    if (filters.target_type) {
      query = query.eq('target_type', filters.target_type)
    }
    if (filters.from_user_id) {
      query = query.eq('from_user_id', filters.from_user_id)
    }
    if (filters.to_user_id) {
      query = query.eq('to_user_id', filters.to_user_id)
    }
    if (filters.start_date) {
      query = query.gte('created_at', filters.start_date)
    }
    if (filters.end_date) {
      query = query.lte('created_at', filters.end_date)
    }

    const { data, error } = await query

    if (error) throw error

    // 获取总数
    let countQuery = supabase
      .from('appreciation_records')
      .select('*', { count: 'exact', head: true })

    if (filters.status) {
      countQuery = countQuery.eq('status', filters.status)
    }
    if (filters.target_type) {
      countQuery = countQuery.eq('target_type', filters.target_type)
    }
    if (filters.from_user_id) {
      countQuery = countQuery.eq('from_user_id', filters.from_user_id)
    }
    if (filters.to_user_id) {
      countQuery = countQuery.eq('to_user_id', filters.to_user_id)
    }
    if (filters.start_date) {
      countQuery = countQuery.gte('created_at', filters.start_date)
    }
    if (filters.end_date) {
      countQuery = countQuery.lte('created_at', filters.end_date)
    }

    const { count } = await countQuery

    return {
      data: data as AppreciationRecord[],
      total: count || 0,
      page,
      pageSize
    }
  }

  // 获取赞赏统计数据
  async getAppreciationStats(): Promise<AppreciationStats> {
    const today = new Date().toISOString().split('T')[0]
    const thisMonth = new Date().toISOString().slice(0, 7)

    // 获取总体统计
    const { data: totalStats } = await supabase
      .from('appreciation_records')
      .select('amount')
      .eq('status', 'completed')

    // 获取今日统计
    const { data: todayStats } = await supabase
      .from('appreciation_records')
      .select('amount')
      .eq('status', 'completed')
      .gte('created_at', today)

    // 获取本月统计
    const { data: monthStats } = await supabase
      .from('appreciation_records')
      .select('amount')
      .eq('status', 'completed')
      .gte('created_at', thisMonth)

    // 获取收赞最多的用户
    const { data: topReceivers } = await supabase
      .from('appreciation_records')
      .select(`
        to_user_id,
        amount,
        to_user:profiles!to_user_id(
          id,
          username,
          avatar
        )
      `)
      .eq('status', 'completed')
      .limit(10)

    // 获取赞赏最多的用户
    const { data: topGivers } = await supabase
      .from('appreciation_records')
      .select(`
        from_user_id,
        amount,
        from_user:profiles!from_user_id(
          id,
          username,
          avatar
        )
      `)
      .eq('status', 'completed')
      .limit(10)

    // 获取支付方式统计
    const { data: paymentStats } = await supabase
      .from('appreciation_records')
      .select('payment_method, amount')
      .eq('status', 'completed')

    // 处理统计数据
    const totalAmount = (totalStats as any)?.reduce((sum: number, record: any) => sum + record.amount, 0) || 0
    const totalCount = (totalStats as any)?.length || 0
    const todayAmount = (todayStats as any)?.reduce((sum: number, record: any) => sum + record.amount, 0) || 0
    const todayCount = (todayStats as any)?.length || 0
    const monthAmount = (monthStats as any)?.reduce((sum: number, record: any) => sum + record.amount, 0) || 0
    const monthCount = (monthStats as any)?.length || 0

    // 处理收赞排行
    const receiverMap = new Map()
    ;(topReceivers as any)?.forEach((record: any) => {
      const userId = record.to_user_id
      if (!receiverMap.has(userId)) {
        receiverMap.set(userId, {
          user_id: userId,
          username: record.to_user?.username || '',
          avatar: record.to_user?.avatar,
          total_amount: 0,
          count: 0
        })
      }
      const user = receiverMap.get(userId)
      user.total_amount += record.amount
      user.count += 1
    })

    // 处理赞赏排行
    const giverMap = new Map()
    ;(topGivers as any)?.forEach((record: any) => {
      const userId = record.from_user_id
      if (!giverMap.has(userId)) {
        giverMap.set(userId, {
          user_id: userId,
          username: record.from_user?.username || '',
          avatar: record.from_user?.avatar,
          total_amount: 0,
          count: 0
        })
      }
      const user = giverMap.get(userId)
      user.total_amount += record.amount
      user.count += 1
    })

    // 处理支付方式统计
    const paymentMethodMap = new Map()
    ;(paymentStats as any)?.forEach((record: any) => {
      const method = record.payment_method
      if (!paymentMethodMap.has(method)) {
        paymentMethodMap.set(method, { amount: 0, count: 0 })
      }
      const stat = paymentMethodMap.get(method)
      stat.amount += record.amount
      stat.count += 1
    })

    const paymentMethodStats = Array.from(paymentMethodMap.entries()).map(([method, stat]) => ({
      method,
      amount: stat.amount,
      count: stat.count,
      percentage: totalAmount > 0 ? (stat.amount / totalAmount) * 100 : 0
    }))

    return {
      total_amount: totalAmount,
      total_count: totalCount,
      today_amount: todayAmount,
      today_count: todayCount,
      this_month_amount: monthAmount,
      this_month_count: monthCount,
      avg_amount: totalCount > 0 ? totalAmount / totalCount : 0,
      top_receivers: Array.from(receiverMap.values())
        .sort((a, b) => b.total_amount - a.total_amount)
        .slice(0, 10),
      top_givers: Array.from(giverMap.values())
        .sort((a, b) => b.total_amount - a.total_amount)
        .slice(0, 10),
      payment_method_stats: paymentMethodStats
    }
  }

  // 获取赞赏设置
  async getAppreciationSettings(): Promise<AppreciationSettings | null> {
    const { data, error } = await supabase
      .from('appreciation_settings')
      .select('*')
      .single()

    if (error && error.code !== 'PGRST116') throw error
    return data
  }

  // 更新赞赏设置
  async updateAppreciationSettings(settings: Partial<AppreciationSettings>): Promise<AppreciationSettings> {
    const { data, error } = await (supabase as any)
      .from('appreciation_settings')
      .upsert({
        ...settings,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  }

  // 获取用户赞赏余额
  async getUserAppreciationBalance(userId: string): Promise<UserAppreciationBalance | null> {
    const { data, error } = await supabase
      .from('user_appreciation_balances')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error && error.code !== 'PGRST116') throw error
    return data
  }

  // 更新赞赏记录状态
  async updateAppreciationStatus(id: string, status: string, transactionId?: string) {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    }

    if (transactionId) {
      updateData.transaction_id = transactionId
    }

    const { data, error } = await (supabase as any)
      .from('appreciation_records')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // 退款赞赏
  async refundAppreciation(id: string, reason?: string) {
    const { data, error } = await (supabase as any)
      .from('appreciation_records')
      .update({
        status: 'refunded',
        refund_reason: reason,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // 批量处理赞赏记录
  async batchUpdateAppreciations(ids: string[], updates: Partial<AppreciationRecord>) {
    const { data, error } = await (supabase as any)
      .from('appreciation_records')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .in('id', ids)
      .select()

    if (error) throw error
    return data
  }

  // 获取赞赏趋势数据
  async getAppreciationTrends(days: number = 30) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const { data, error } = await supabase
      .from('appreciation_records')
      .select('created_at, amount')
      .eq('status', 'completed')
      .gte('created_at', startDate.toISOString())
      .order('created_at')

    if (error) throw error

    // 按日期分组统计
    const trendsMap = new Map()
    ;(data as any)?.forEach((record: any) => {
      const date = record.created_at.split('T')[0]
      if (!trendsMap.has(date)) {
        trendsMap.set(date, { amount: 0, count: 0 })
      }
      const trend = trendsMap.get(date)
      trend.amount += record.amount
      trend.count += 1
    })

    return Array.from(trendsMap.entries()).map(([date, trend]) => ({
      date,
      amount: trend.amount,
      count: trend.count
    }))
  }
}

export const appreciationService = new AppreciationService()