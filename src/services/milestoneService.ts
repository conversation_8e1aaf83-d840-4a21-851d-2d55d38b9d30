import { supabase } from '../utils/supabase'

export interface UserMilestone {
  id: string
  user_id: string
  milestone_type: string
  milestone_name: string
  milestone_description?: string
  target_value: number
  current_value: number
  achieved_at?: string
  is_achieved: boolean
  reward_points?: number
  created_at: string
  updated_at: string
  // 关联用户信息
  user?: {
    username: string
    avatar_url?: string
  }
}

export const milestoneService = {
  // 获取用户里程碑列表
  async getUserMilestones(): Promise<{ success: boolean; data?: UserMilestone[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('user_milestones')
        .select(`
          *,
          user:profiles!user_milestones_user_id_fkey (
            username,
            avatar_url
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      console.error('获取用户里程碑失败:', error)
      return {
        success: false,
        error: '获取用户里程碑失败'
      }
    }
  },

  // 手动标记里程碑为已达成
  async markMilestoneAchieved(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      // 先获取目标值
      const { data: milestone } = await supabase
        .from('user_milestones')
        .select('target_value')
        .eq('id', id)
        .single()

      const updateData: Record<string, any> = {
        is_achieved: true,
        achieved_at: new Date().toISOString(),
        current_value: (milestone as any)?.target_value || 0
      }

      const { error } = await (supabase as any)
        .from('user_milestones')
        .update(updateData)
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('标记里程碑达成失败:', error)
      return {
        success: false,
        error: '标记里程碑达成失败'
      }
    }
  },

  // 重置里程碑进度
  async resetMilestoneProgress(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      const updateData: Record<string, any> = {
        is_achieved: false,
        achieved_at: null,
        current_value: 0
      }

      const { error } = await (supabase as any)
        .from('user_milestones')
        .update(updateData)
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('重置里程碑进度失败:', error)
      return {
        success: false,
        error: '重置里程碑进度失败'
      }
    }
  },

  // 删除用户里程碑
  async deleteUserMilestone(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('user_milestones')
        .delete()
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('删除用户里程碑失败:', error)
      return {
        success: false,
        error: '删除用户里程碑失败'
      }
    }
  },

  // 获取里程碑统计
  async getMilestoneStats(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 获取总里程碑数
      const { count: totalMilestones } = await supabase
        .from('user_milestones')
        .select('*', { count: 'exact', head: true })

      // 获取已达成里程碑数
      const { count: achievedMilestones } = await supabase
        .from('user_milestones')
        .select('*', { count: 'exact', head: true })
        .eq('is_achieved', true)

      // 获取今日达成的里程碑数
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const { count: todayAchieved } = await supabase
        .from('user_milestones')
        .select('*', { count: 'exact', head: true })
        .eq('is_achieved', true)
        .gte('achieved_at', today.toISOString())

      // 计算达成率
      const achievementRate = totalMilestones && totalMilestones > 0 
        ? Math.round((achievedMilestones || 0) / totalMilestones * 100)
        : 0

      // 获取里程碑类型分布
      const { data: typeDistribution } = await supabase
        .from('user_milestones')
        .select('milestone_type')

      const typeStats = typeDistribution?.reduce((acc: any, item: any) => {
        acc[item.milestone_type] = (acc[item.milestone_type] || 0) + 1
        return acc
      }, {}) || {}

      return {
        success: true,
        data: {
          totalMilestones: totalMilestones || 0,
          achievedMilestones: achievedMilestones || 0,
          todayAchieved: todayAchieved || 0,
          achievementRate,
          typeStats
        }
      }
    } catch (error) {
      console.error('获取里程碑统计失败:', error)
      return {
        success: false,
        error: '获取里程碑统计失败'
      }
    }
  },

  // 获取用户的里程碑详情
  async getUserMilestonesByUserId(userId: string): Promise<{ success: boolean; data?: UserMilestone[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('user_milestones')
        .select(`
          *,
          user:profiles!user_milestones_user_id_fkey (
            username,
            avatar_url
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      console.error('获取用户里程碑详情失败:', error)
      return {
        success: false,
        error: '获取用户里程碑详情失败'
      }
    }
  }
}