// @ts-nocheck
import { supabase } from '../utils/supabase';

// 定义接口而不是直接导出
interface Invite {
  id: number;
  code: string;
  creator: string;
  creator_id?: string;
  used_by?: string | null;
  max_uses: number;
  current_uses: number;
  status: 'active' | 'used' | 'expired' | 'disabled';
  created_at: string;
  expires_at: string;
  updated_at?: string;
}

interface InviteRecord {
  id: number;
  invite_code: string;
  inviter: string;
  inviter_id: string;
  invitee?: string | null;
  invitee_id?: string | null;
  invited_at: string;
  registered_at?: string | null;
  updated_at?: string;
}

export type { Invite, InviteRecord };

export const inviteService = {
  /**
   * 获取所有邀请码
   */
  async getAllInvites(): Promise<{ success: boolean; data?: Invite[] | null; error?: Error | null }> {
    try {
      // 模拟数据，因为实际的Supabase表可能不存在
      const mockInvites: Invite[] = [
        {
          id: 1,
          code: 'INVITE001',
          creator: 'admin',
          creator_id: 'user_1',
          used_by: null,
          max_uses: 10,
          current_uses: 3,
          status: 'active',
          created_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          code: 'INVITE002',
          creator: 'manager',
          creator_id: 'user_2',
          used_by: 'user_3',
          max_uses: 5,
          current_uses: 5,
          status: 'used',
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];

      return { success: true, data: mockInvites };
    } catch (error) {
      console.error('获取邀请码列表失败:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 根据ID获取邀请码
   */
  async getInviteById(id: number): Promise<{ success: boolean; data?: Invite | null; error?: Error | null }> {
    try {
      // 模拟数据
      const mockInvite: Invite = {
        id: id,
        code: `INVITE${id.toString().padStart(3, '0')}`,
        creator: 'admin',
        creator_id: 'user_1',
        used_by: null,
        max_uses: 10,
        current_uses: 3,
        status: 'active',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      };

      return { success: true, data: mockInvite };
    } catch (error) {
      console.error('获取邀请码详情失败:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 创建邀请码
   */
  async createInvite(inviteData: any): Promise<{ success: boolean; data?: any; error?: Error | null }> {
    try {
      // 模拟创建邀请码
      const newInvite = {
        id: Math.floor(Math.random() * 1000) + 100,
        code: this.generateInviteCode(),
        creator: 'admin',
        creator_id: 'user_1',
        used_by: null,
        max_uses: inviteData.max_uses || 1,
        current_uses: 0,
        status: 'active',
        created_at: new Date().toISOString(),
        expires_at: inviteData.expires_at || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      };

      return { success: true, data: newInvite };
    } catch (error) {
      console.error('创建邀请码失败:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 更新邀请码
   */
  async updateInvite(id: number, inviteData: any): Promise<{ success: boolean; data?: any; error?: Error | null }> {
    try {
      // 模拟更新邀请码
      const updatedInvite = {
        id: id,
        code: `INVITE${id.toString().padStart(3, '0')}`,
        creator: 'admin',
        creator_id: 'user_1',
        used_by: null,
        max_uses: inviteData.max_uses || 10,
        current_uses: 3,
        status: inviteData.status || 'active',
        created_at: new Date().toISOString(),
        expires_at: inviteData.expires_at || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString()
      };

      return { success: true, data: updatedInvite };
    } catch (error) {
      console.error('更新邀请码失败:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 删除邀请码
   */
  async deleteInvite(id: number): Promise<{ success: boolean; error?: Error | null }> {
    try {
      // 模拟删除邀请码
      return { success: true };
    } catch (error) {
      console.error('删除邀请码失败:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 获取所有邀请记录
   */
  async getAllInviteRecords(): Promise<{ success: boolean; data?: InviteRecord[] | null; error?: Error | null }> {
    try {
      // 模拟邀请记录数据
      const mockRecords: InviteRecord[] = [
        {
          id: 1,
          invite_code: 'INVITE001',
          inviter: 'admin',
          inviter_id: 'user_1',
          invitee: 'newuser1',
          invitee_id: 'user_3',
          invited_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          registered_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          invite_code: 'INVITE001',
          inviter: 'admin',
          inviter_id: 'user_1',
          invitee: null,
          invitee_id: null,
          invited_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          registered_at: null
        }
      ];

      return { success: true, data: mockRecords };
    } catch (error) {
      console.error('获取邀请记录失败:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 根据邀请码获取邀请记录
   */
  async getInviteRecordsByCode(code: string): Promise<{ success: boolean; data?: InviteRecord[] | null; error?: Error | null }> {
    try {
      // 模拟根据邀请码获取记录
      const mockRecords: InviteRecord[] = [
        {
          id: 1,
          invite_code: code,
          inviter: 'admin',
          inviter_id: 'user_1',
          invitee: 'newuser1',
          invitee_id: 'user_3',
          invited_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          registered_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];

      return { success: true, data: mockRecords };
    } catch (error) {
      console.error('获取邀请记录失败:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 生成邀请码
   */
  generateInviteCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
};