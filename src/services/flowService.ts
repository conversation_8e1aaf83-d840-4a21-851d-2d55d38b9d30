import { supabase } from '../utils/supabase'

export interface FlowBehaviorRecord {
  id: string
  user_id: string
  behavior_type: string
  behavior_value: number
  flow_impact: number
  recorded_at: string
  created_at: string
  updated_at: string
  // 关联用户信息
  user?: {
    username: string
    avatar_url?: string
  }
}

export interface FlowDailyRecord {
  id: string
  user_id: string
  record_date: string
  morning_energy: number
  afternoon_energy: number
  evening_energy: number
  daily_flow_score: number
  notes?: string
  created_at: string
  updated_at: string
  // 关联用户信息
  user?: {
    username: string
    avatar_url?: string
  }
}

export const flowService = {
  // 获取心流行为记录列表
  async getFlowBehaviorRecords(): Promise<{ success: boolean; data?: FlowBehaviorRecord[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('flow_behavior_records')
        .select(`
          *,
          user:profiles!flow_behavior_records_user_id_fkey (
            username,
            avatar_url
          )
        `)
        .order('recorded_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      console.error('获取心流行为记录失败:', error)
      return {
        success: false,
        error: '获取心流行为记录失败'
      }
    }
  },

  // 获取每日心流记录列表
  async getFlowDailyRecords(): Promise<{ success: boolean; data?: FlowDailyRecord[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('flow_daily_records')
        .select(`
          *,
          user:profiles!flow_daily_records_user_id_fkey (
            username,
            avatar_url
          )
        `)
        .order('record_date', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      console.error('获取每日心流记录失败:', error)
      return {
        success: false,
        error: '获取每日心流记录失败'
      }
    }
  },

  // 获取用户心流统计
  async getUserFlowStats(userId: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 获取用户的心流行为记录统计
      const { data: behaviorData, error: behaviorError } = await supabase
        .from('flow_behavior_records')
        .select('behavior_type, flow_impact')
        .eq('user_id', userId)

      if (behaviorError) throw behaviorError

      // 获取用户的每日心流记录统计
      const { data: dailyData, error: dailyError } = await supabase
        .from('flow_daily_records')
        .select('daily_flow_score, record_date')
        .eq('user_id', userId)
        .order('record_date', { ascending: false })
        .limit(30) // 最近30天

      if (dailyError) throw dailyError

      // 计算统计数据
      const totalBehaviors = behaviorData?.length || 0
      const avgFlowImpact = behaviorData?.length 
        ? behaviorData.reduce((sum: number, record: any) => sum + record.flow_impact, 0) / behaviorData.length
        : 0
      
      const avgDailyScore = dailyData?.length
        ? dailyData.reduce((sum: number, record: any) => sum + record.daily_flow_score, 0) / dailyData.length
        : 0

      return {
        success: true,
        data: {
          totalBehaviors,
          avgFlowImpact,
          avgDailyScore,
          recentDailyScores: dailyData || []
        }
      }
    } catch (error) {
      console.error('获取用户心流统计失败:', error)
      return {
        success: false,
        error: '获取用户心流统计失败'
      }
    }
  },

  // 删除心流行为记录
  async deleteFlowBehaviorRecord(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('flow_behavior_records')
        .delete()
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('删除心流行为记录失败:', error)
      return {
        success: false,
        error: '删除心流行为记录失败'
      }
    }
  },

  // 删除每日心流记录
  async deleteFlowDailyRecord(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('flow_daily_records')
        .delete()
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('删除每日心流记录失败:', error)
      return {
        success: false,
        error: '删除每日心流记录失败'
      }
    }
  },

  // 获取心流统计概览
  async getFlowOverview(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 获取总记录数
      const { count: totalBehaviorRecords } = await supabase
        .from('flow_behavior_records')
        .select('*', { count: 'exact', head: true })

      const { count: totalDailyRecords } = await supabase
        .from('flow_daily_records')
        .select('*', { count: 'exact', head: true })

      // 获取今日新增记录
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const { count: todayBehaviorRecords } = await supabase
        .from('flow_behavior_records')
        .select('*', { count: 'exact', head: true })
        .gte('recorded_at', today.toISOString())

      const { count: todayDailyRecords } = await supabase
        .from('flow_daily_records')
        .select('*', { count: 'exact', head: true })
        .eq('record_date', today.toISOString().split('T')[0])

      // 获取平均心流分数
      const { data: avgScoreData } = await supabase
        .from('flow_daily_records')
        .select('daily_flow_score')

      const avgFlowScore = avgScoreData?.length
        ? avgScoreData.reduce((sum: number, record: any) => sum + record.daily_flow_score, 0) / avgScoreData.length
        : 0

      return {
        success: true,
        data: {
          totalBehaviorRecords: totalBehaviorRecords || 0,
          totalDailyRecords: totalDailyRecords || 0,
          todayBehaviorRecords: todayBehaviorRecords || 0,
          todayDailyRecords: todayDailyRecords || 0,
          avgFlowScore: Math.round(avgFlowScore * 100) / 100
        }
      }
    } catch (error) {
      console.error('获取心流统计概览失败:', error)
      return {
        success: false,
        error: '获取心流统计概览失败'
      }
    }
  }
}