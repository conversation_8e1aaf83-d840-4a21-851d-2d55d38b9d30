import { supabase } from '../utils/supabase'

// 订阅计划接口
export interface SubscriptionPlan {
  id: string
  name: string
  description: string
  price: number
  duration_days: number
  features: string[]
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

// 用户订阅接口
export interface UserSubscription {
  id: string
  user_id: string
  plan_id: string
  status: 'active' | 'expired' | 'cancelled' | 'pending'
  start_date: string
  end_date: string
  auto_renew: boolean
  payment_method: 'wechat' | 'alipay' | 'balance'
  created_at: string
  updated_at: string
  user?: {
    id: string
    username: string
    avatar?: string
    email?: string
  }
  plan?: SubscriptionPlan
}

// 合伙人申请接口
export interface PartnerApplication {
  id: string
  user_id: string
  application_type: 'individual' | 'company'
  real_name: string
  id_card?: string
  company_name?: string
  business_license?: string
  contact_phone: string
  contact_email: string
  experience_description: string
  promotion_plan: string
  status: 'pending' | 'approved' | 'rejected'
  review_notes?: string
  reviewed_by?: string
  reviewed_at?: string
  created_at: string
  updated_at: string
  user?: {
    id: string
    username: string
    avatar?: string
  }
}

// 合伙人信息接口
export interface Partner {
  id: string
  user_id: string
  partner_code: string
  commission_rate: number
  total_earnings: number
  available_balance: number
  total_referrals: number
  active_referrals: number
  status: 'active' | 'suspended' | 'inactive'
  created_at: string
  updated_at: string
  user?: {
    id: string
    username: string
    avatar?: string
  }
}

// 支付记录接口
export interface PaymentRecord {
  id: string
  user_id: string
  order_type: 'subscription' | 'appreciation' | 'partner_commission'
  order_id: string
  amount: number
  payment_method: 'wechat' | 'alipay' | 'balance'
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  transaction_id?: string
  payment_time?: string
  refund_time?: string
  refund_reason?: string
  created_at: string
  updated_at: string
  user?: {
    id: string
    username: string
    avatar?: string
  }
}

// 支付统计接口
export interface PaymentStats {
  total_revenue: number
  today_revenue: number
  month_revenue: number
  subscription_revenue: number
  appreciation_revenue: number
  refund_amount: number
  payment_method_stats: Array<{
    method: string
    amount: number
    count: number
    percentage: number
  }>
  revenue_trends: Array<{
    date: string
    amount: number
    count: number
  }>
}

class SubscriptionPaymentService {
  // 获取订阅计划列表
  async getSubscriptionPlans(includeInactive = false) {
    let query = supabase
      .from('subscription_plans')
      .select('*')
      .order('sort_order')

    if (!includeInactive) {
      query = query.eq('is_active', true)
    }

    const { data, error } = await query
    if (error) throw error
    return data as SubscriptionPlan[]
  }

  // 创建订阅计划
  async createSubscriptionPlan(plan: Omit<SubscriptionPlan, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await (supabase as any)
      .from('subscription_plans')
      .insert({
        ...plan,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data as SubscriptionPlan
  }

  // 更新订阅计划
  async updateSubscriptionPlan(id: string, updates: Partial<SubscriptionPlan>) {
    const { data, error } = await (supabase as any)
      .from('subscription_plans')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as SubscriptionPlan
  }

  // 删除订阅计划
  async deleteSubscriptionPlan(id: string) {
    const { error } = await supabase
      .from('subscription_plans')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // 获取用户订阅列表
  async getUserSubscriptions(params: {
    page?: number
    pageSize?: number
    status?: string
    user_id?: string
    plan_id?: string
  }) {
    const { page = 1, pageSize = 20, ...filters } = params
    const offset = (page - 1) * pageSize

    let query = supabase
      .from('user_subscriptions')
      .select(`
        *,
        user:profiles!user_id(
          id,
          username,
          avatar,
          email
        ),
        plan:subscription_plans!plan_id(*)
      `)
      .range(offset, offset + pageSize - 1)
      .order('created_at', { ascending: false })

    if (filters.status) {
      query = query.eq('status', filters.status)
    }
    if (filters.user_id) {
      query = query.eq('user_id', filters.user_id)
    }
    if (filters.plan_id) {
      query = query.eq('plan_id', filters.plan_id)
    }

    const { data, error } = await query
    if (error) throw error

    // 获取总数
    let countQuery = supabase
      .from('user_subscriptions')
      .select('*', { count: 'exact', head: true })

    if (filters.status) {
      countQuery = countQuery.eq('status', filters.status)
    }
    if (filters.user_id) {
      countQuery = countQuery.eq('user_id', filters.user_id)
    }
    if (filters.plan_id) {
      countQuery = countQuery.eq('plan_id', filters.plan_id)
    }

    const { count } = await countQuery

    return {
      data: data as UserSubscription[],
      total: count || 0,
      page,
      pageSize
    }
  }

  // 更新订阅状态
  async updateSubscriptionStatus(id: string, status: string) {
    const { data, error } = await (supabase as any)
      .from('user_subscriptions')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as UserSubscription
  }

  // 获取合伙人申请列表
  async getPartnerApplications(params: {
    page?: number
    pageSize?: number
    status?: string
  }) {
    const { page = 1, pageSize = 20, status } = params
    const offset = (page - 1) * pageSize

    let query = supabase
      .from('partner_applications')
      .select(`
        *,
        user:profiles!user_id(
          id,
          username,
          avatar
        )
      `)
      .range(offset, offset + pageSize - 1)
      .order('created_at', { ascending: false })

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query
    if (error) throw error

    // 获取总数
    let countQuery = supabase
      .from('partner_applications')
      .select('*', { count: 'exact', head: true })

    if (status) {
      countQuery = countQuery.eq('status', status)
    }

    const { count } = await countQuery

    return {
      data: data as PartnerApplication[],
      total: count || 0,
      page,
      pageSize
    }
  }

  // 审核合伙人申请
  async reviewPartnerApplication(id: string, status: 'approved' | 'rejected', reviewNotes?: string, reviewerId?: string) {
    const { data, error } = await (supabase as any)
      .from('partner_applications')
      .update({
        status,
        review_notes: reviewNotes,
        reviewed_by: reviewerId,
        reviewed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    // 如果审核通过，创建合伙人记录
    if (status === 'approved') {
      const application = data as PartnerApplication
      await this.createPartner(application.user_id)
    }

    return data as PartnerApplication
  }

  // 创建合伙人记录
  async createPartner(userId: string) {
    // 生成合伙人代码
    const partnerCode = `P${Date.now().toString(36).toUpperCase()}`

    const { data, error } = await (supabase as any)
      .from('partners')
      .insert({
        user_id: userId,
        partner_code: partnerCode,
        commission_rate: 0.1, // 默认10%佣金
        total_earnings: 0,
        available_balance: 0,
        total_referrals: 0,
        active_referrals: 0,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data as Partner
  }

  // 获取合伙人列表
  async getPartners(params: {
    page?: number
    pageSize?: number
    status?: string
  }) {
    const { page = 1, pageSize = 20, status } = params
    const offset = (page - 1) * pageSize

    let query = supabase
      .from('partners')
      .select(`
        *,
        user:profiles!user_id(
          id,
          username,
          avatar
        )
      `)
      .range(offset, offset + pageSize - 1)
      .order('created_at', { ascending: false })

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query
    if (error) throw error

    // 获取总数
    let countQuery = supabase
      .from('partners')
      .select('*', { count: 'exact', head: true })

    if (status) {
      countQuery = countQuery.eq('status', status)
    }

    const { count } = await countQuery

    return {
      data: data as Partner[],
      total: count || 0,
      page,
      pageSize
    }
  }

  // 更新合伙人状态
  async updatePartnerStatus(id: string, status: string) {
    const { data, error } = await (supabase as any)
      .from('partners')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Partner
  }

  // 获取支付记录列表
  async getPaymentRecords(params: {
    page?: number
    pageSize?: number
    status?: string
    order_type?: string
    payment_method?: string
    user_id?: string
    start_date?: string
    end_date?: string
  }) {
    const { page = 1, pageSize = 20, ...filters } = params
    const offset = (page - 1) * pageSize

    let query = supabase
      .from('payment_records')
      .select(`
        *,
        user:profiles!user_id(
          id,
          username,
          avatar
        )
      `)
      .range(offset, offset + pageSize - 1)
      .order('created_at', { ascending: false })

    // 应用筛选条件
    if (filters.status) {
      query = query.eq('status', filters.status)
    }
    if (filters.order_type) {
      query = query.eq('order_type', filters.order_type)
    }
    if (filters.payment_method) {
      query = query.eq('payment_method', filters.payment_method)
    }
    if (filters.user_id) {
      query = query.eq('user_id', filters.user_id)
    }
    if (filters.start_date) {
      query = query.gte('created_at', filters.start_date)
    }
    if (filters.end_date) {
      query = query.lte('created_at', filters.end_date)
    }

    const { data, error } = await query
    if (error) throw error

    // 获取总数
    let countQuery = supabase
      .from('payment_records')
      .select('*', { count: 'exact', head: true })

    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        if (key === 'start_date') {
          countQuery = countQuery.gte('created_at', value)
        } else if (key === 'end_date') {
          countQuery = countQuery.lte('created_at', value)
        } else {
          countQuery = countQuery.eq(key, value)
        }
      }
    })

    const { count } = await countQuery

    return {
      data: data as PaymentRecord[],
      total: count || 0,
      page,
      pageSize
    }
  }

  // 获取支付统计数据
  async getPaymentStats(): Promise<PaymentStats> {
    const today = new Date().toISOString().split('T')[0]
    const thisMonth = new Date().toISOString().slice(0, 7)

    // 获取总收入
    const { data: totalRevenue } = await supabase
      .from('payment_records')
      .select('amount, order_type')
      .eq('status', 'completed')

    // 获取今日收入
    const { data: todayRevenue } = await supabase
      .from('payment_records')
      .select('amount')
      .eq('status', 'completed')
      .gte('created_at', today)

    // 获取本月收入
    const { data: monthRevenue } = await supabase
      .from('payment_records')
      .select('amount')
      .eq('status', 'completed')
      .gte('created_at', thisMonth)

    // 获取退款金额
    const { data: refundData } = await supabase
      .from('payment_records')
      .select('amount')
      .eq('status', 'refunded')

    // 获取支付方式统计
    const { data: paymentMethodData } = await supabase
      .from('payment_records')
      .select('payment_method, amount')
      .eq('status', 'completed')

    // 获取收入趋势（最近30天）
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)
    const { data: trendsData } = await supabase
      .from('payment_records')
      .select('created_at, amount')
      .eq('status', 'completed')
      .gte('created_at', startDate.toISOString())
      .order('created_at')

    // 处理统计数据
    const totalAmount = (totalRevenue as any)?.reduce((sum: number, record: any) => sum + record.amount, 0) || 0
    const subscriptionAmount = (totalRevenue as any)?.filter((r: any) => r.order_type === 'subscription')
      .reduce((sum: number, record: any) => sum + record.amount, 0) || 0
    const appreciationAmount = (totalRevenue as any)?.filter((r: any) => r.order_type === 'appreciation')
      .reduce((sum: number, record: any) => sum + record.amount, 0) || 0
    const todayAmount = (todayRevenue as any)?.reduce((sum: number, record: any) => sum + record.amount, 0) || 0
    const monthAmount = (monthRevenue as any)?.reduce((sum: number, record: any) => sum + record.amount, 0) || 0
    const refundAmount = (refundData as any)?.reduce((sum: number, record: any) => sum + record.amount, 0) || 0

    // 处理支付方式统计
    const paymentMethodMap = new Map()
    ;(paymentMethodData as any)?.forEach((record: any) => {
      const method = record.payment_method
      if (!paymentMethodMap.has(method)) {
        paymentMethodMap.set(method, { amount: 0, count: 0 })
      }
      const stat = paymentMethodMap.get(method)
      stat.amount += record.amount
      stat.count += 1
    })

    const paymentMethodStats = Array.from(paymentMethodMap.entries()).map(([method, stat]) => ({
      method,
      amount: stat.amount,
      count: stat.count,
      percentage: totalAmount > 0 ? (stat.amount / totalAmount) * 100 : 0
    }))

    // 处理收入趋势
    const trendsMap = new Map()
    ;(trendsData as any)?.forEach((record: any) => {
      const date = record.created_at.split('T')[0]
      if (!trendsMap.has(date)) {
        trendsMap.set(date, { amount: 0, count: 0 })
      }
      const trend = trendsMap.get(date)
      trend.amount += record.amount
      trend.count += 1
    })

    const revenueTrends = Array.from(trendsMap.entries()).map(([date, trend]) => ({
      date,
      amount: trend.amount,
      count: trend.count
    }))

    return {
      total_revenue: totalAmount,
      today_revenue: todayAmount,
      month_revenue: monthAmount,
      subscription_revenue: subscriptionAmount,
      appreciation_revenue: appreciationAmount,
      refund_amount: refundAmount,
      payment_method_stats: paymentMethodStats,
      revenue_trends: revenueTrends
    }
  }

  // 处理退款
  async processRefund(paymentId: string, reason?: string) {
    const { data, error } = await (supabase as any)
      .from('payment_records')
      .update({
        status: 'refunded',
        refund_reason: reason,
        refund_time: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', paymentId)
      .select()
      .single()

    if (error) throw error
    return data as PaymentRecord
  }

  // 获取订阅统计
  async getSubscriptionStats() {
    const { data: subscriptions } = await supabase
      .from('user_subscriptions')
      .select('status, plan_id, created_at')

    const { data: plans } = await supabase
      .from('subscription_plans')
      .select('id, name, price')

    const activeCount = (subscriptions as any)?.filter((s: any) => s.status === 'active').length || 0
    const expiredCount = (subscriptions as any)?.filter((s: any) => s.status === 'expired').length || 0
    const cancelledCount = (subscriptions as any)?.filter((s: any) => s.status === 'cancelled').length || 0
    const totalCount = (subscriptions as any)?.length || 0

    // 按计划统计
    const planStats = (plans as any)?.map((plan: any) => {
      const planSubscriptions = (subscriptions as any)?.filter((s: any) => s.plan_id === plan.id) || []
      return {
        plan_id: plan.id,
        plan_name: plan.name,
        price: plan.price,
        total_subscriptions: planSubscriptions.length,
        active_subscriptions: planSubscriptions.filter((s: any) => s.status === 'active').length
      }
    }) || []

    return {
      total_subscriptions: totalCount,
      active_subscriptions: activeCount,
      expired_subscriptions: expiredCount,
      cancelled_subscriptions: cancelledCount,
      plan_stats: planStats
    }
  }
}

export const subscriptionPaymentService = new SubscriptionPaymentService()