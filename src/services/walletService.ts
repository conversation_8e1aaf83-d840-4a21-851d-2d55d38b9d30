// 钱包管理服务
export interface Wallet {
  id: string
  user_id: string
  balance: number
  frozen_balance: number
  total_income: number
  total_expense: number
  created_at: string
  updated_at: string
  status: 'active' | 'frozen' | 'closed'
  user?: {
    username: string
    email: string
    avatar_url?: string
  }
}

export interface WalletTransaction {
  id: string
  wallet_id: string
  type: 'income' | 'expense' | 'transfer' | 'refund'
  amount: number
  balance_after: number
  description: string
  reference_id?: string
  reference_type?: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  created_at: string
  updated_at: string
  wallet?: {
    user_id: string
    user?: {
      username: string
      email: string
    }
  }
}

export interface WithdrawalRequest {
  id: string
  user_id: string
  wallet_id: string
  amount: number
  fee: number
  actual_amount: number
  payment_method: 'alipay' | 'wechat' | 'bank_card'
  payment_account: string
  status: 'pending' | 'processing' | 'completed' | 'rejected' | 'cancelled'
  reason?: string
  processed_by?: string
  processed_at?: string
  created_at: string
  updated_at: string
  user?: {
    username: string
    email: string
    phone?: string
  }
}

export interface Order {
  id: string
  user_id: string
  order_no: string
  type: 'subscription' | 'donation' | 'purchase' | 'reward'
  amount: number
  currency: string
  status: 'pending' | 'paid' | 'cancelled' | 'refunded'
  payment_method?: string
  payment_id?: string
  description: string
  metadata?: Record<string, unknown>
  created_at: string
  updated_at: string
  paid_at?: string
  user?: {
    username: string
    email: string
  }
}

export interface WalletStats {
  totalWallets: number
  activeWallets: number
  frozenWallets: number
  totalBalance: number
  totalTransactions: number
  pendingWithdrawals: number
  todayTransactions: number
  todayAmount: number
}

class WalletService {
  // 获取钱包列表
  async getWallets(params: {
    page?: number
    pageSize?: number
    status?: string
    userId?: string
    search?: string
  }): Promise<{
    data: Wallet[]
    total: number
    page: number
    pageSize: number
  }> {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockWallets: Wallet[] = [
      {
        id: '1',
        user_id: 'user1',
        balance: 1250.50,
        frozen_balance: 100.00,
        total_income: 2500.00,
        total_expense: 1149.50,
        created_at: '2024-01-15T08:30:00Z',
        updated_at: '2024-01-20T14:22:00Z',
        status: 'active',
        user: {
          username: '张三',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar1.jpg'
        }
      },
      {
        id: '2',
        user_id: 'user2',
        balance: 0.00,
        frozen_balance: 0.00,
        total_income: 500.00,
        total_expense: 500.00,
        created_at: '2024-01-10T10:15:00Z',
        updated_at: '2024-01-18T16:45:00Z',
        status: 'frozen',
        user: {
          username: '李四',
          email: '<EMAIL>'
        }
      }
    ]

    return {
      data: mockWallets,
      total: mockWallets.length,
      page: params.page || 1,
      pageSize: params.pageSize || 20
    }
  }

  // 获取交易记录
  async getTransactions(params: {
    page?: number
    pageSize?: number
    type?: string
    status?: string
    walletId?: string
    dateRange?: [string, string]
  }): Promise<{
    data: WalletTransaction[]
    total: number
    page: number
    pageSize: number
  }> {
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const mockTransactions: WalletTransaction[] = [
      {
        id: '1',
        wallet_id: '1',
        type: 'income',
        amount: 100.00,
        balance_after: 1250.50,
        description: '订阅收入',
        reference_id: 'sub_001',
        reference_type: 'subscription',
        status: 'completed',
        created_at: '2024-01-20T14:22:00Z',
        updated_at: '2024-01-20T14:22:00Z',
        wallet: {
          user_id: 'user1',
          user: {
            username: '张三',
            email: '<EMAIL>'
          }
        }
      },
      {
        id: '2',
        wallet_id: '1',
        type: 'expense',
        amount: 50.00,
        balance_after: 1150.50,
        description: '提现手续费',
        reference_id: 'withdraw_001',
        reference_type: 'withdrawal',
        status: 'completed',
        created_at: '2024-01-19T09:15:00Z',
        updated_at: '2024-01-19T09:15:00Z'
      }
    ]

    return {
      data: mockTransactions,
      total: mockTransactions.length,
      page: params.page || 1,
      pageSize: params.pageSize || 20
    }
  }

  // 获取提现申请
  async getWithdrawals(params: {
    page?: number
    pageSize?: number
    status?: string
    userId?: string
    dateRange?: [string, string]
  }): Promise<{
    data: WithdrawalRequest[]
    total: number
    page: number
    pageSize: number
  }> {
    await new Promise(resolve => setTimeout(resolve, 600))
    
    const mockWithdrawals: WithdrawalRequest[] = [
      {
        id: '1',
        user_id: 'user1',
        wallet_id: '1',
        amount: 500.00,
        fee: 5.00,
        actual_amount: 495.00,
        payment_method: 'alipay',
        payment_account: '138****8888',
        status: 'pending',
        created_at: '2024-01-20T10:30:00Z',
        updated_at: '2024-01-20T10:30:00Z',
        user: {
          username: '张三',
          email: '<EMAIL>',
          phone: '***********'
        }
      },
      {
        id: '2',
        user_id: 'user2',
        wallet_id: '2',
        amount: 200.00,
        fee: 2.00,
        actual_amount: 198.00,
        payment_method: 'wechat',
        payment_account: 'wx_lisi',
        status: 'completed',
        processed_by: 'admin1',
        processed_at: '2024-01-18T16:45:00Z',
        created_at: '2024-01-18T14:20:00Z',
        updated_at: '2024-01-18T16:45:00Z',
        user: {
          username: '李四',
          email: '<EMAIL>'
        }
      }
    ]

    return {
      data: mockWithdrawals,
      total: mockWithdrawals.length,
      page: params.page || 1,
      pageSize: params.pageSize || 20
    }
  }

  // 获取订单列表
  async getOrders(params: {
    page?: number
    pageSize?: number
    type?: string
    status?: string
    userId?: string
    dateRange?: [string, string]
  }): Promise<{
    data: Order[]
    total: number
    page: number
    pageSize: number
  }> {
    await new Promise(resolve => setTimeout(resolve, 700))
    
    const mockOrders: Order[] = [
      {
        id: '1',
        user_id: 'user1',
        order_no: 'ORD20240120001',
        type: 'subscription',
        amount: 99.00,
        currency: 'CNY',
        status: 'paid',
        payment_method: 'alipay',
        payment_id: 'pay_123456',
        description: '月度会员订阅',
        created_at: '2024-01-20T08:30:00Z',
        updated_at: '2024-01-20T08:35:00Z',
        paid_at: '2024-01-20T08:35:00Z',
        user: {
          username: '张三',
          email: '<EMAIL>'
        }
      },
      {
        id: '2',
        user_id: 'user2',
        order_no: 'ORD20240119001',
        type: 'donation',
        amount: 50.00,
        currency: 'CNY',
        status: 'pending',
        description: '爱心捐赠',
        created_at: '2024-01-19T15:20:00Z',
        updated_at: '2024-01-19T15:20:00Z',
        user: {
          username: '李四',
          email: '<EMAIL>'
        }
      }
    ]

    return {
      data: mockOrders,
      total: mockOrders.length,
      page: params.page || 1,
      pageSize: params.pageSize || 20
    }
  }

  // 获取钱包统计数据
  async getWalletStats(): Promise<WalletStats> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      totalWallets: 1250,
      activeWallets: 1180,
      frozenWallets: 70,
      totalBalance: 125000.50,
      totalTransactions: 8520,
      pendingWithdrawals: 15,
      todayTransactions: 45,
      todayAmount: 2580.00
    }
  }

  // 处理提现申请
  async processWithdrawal(_id: string, _action: 'approve' | 'reject', _reason?: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 模拟处理提现申请
  }

  // 冻结/解冻钱包
  async updateWalletStatus(_id: string, _status: 'active' | 'frozen'): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 800))
    // 模拟更新钱包状态
  }

  // 手动调整余额
  async adjustBalance(_walletId: string, _amount: number, _reason: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 模拟余额调整
  }
}

export const walletService = new WalletService()