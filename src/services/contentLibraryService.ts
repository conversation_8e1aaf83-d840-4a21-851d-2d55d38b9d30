// 内容库管理服务
export interface ContentItem {
  id: string;
  title: string;
  content: string;
  type: 'article' | 'video' | 'audio' | 'image' | 'document';
  category: string;
  tags: string[];
  author: {
    id: string;
    name: string;
    avatar?: string;
    isExpert: boolean;
  };
  status: 'draft' | 'published' | 'archived' | 'rejected';
  visibility: 'public' | 'private' | 'premium';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadTime: number;
  views: number;
  likes: number;
  comments: number;
  rating: number;
  ratingCount: number;
  thumbnail?: string;
  attachments?: string[];
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  featured: boolean;
  seoKeywords?: string[];
  summary: string;
}

export interface ContentCategory {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  icon?: string;
  color?: string;
  order: number;
  isActive: boolean;
  contentCount: number;
  children?: ContentCategory[];
}

export interface ExpertQA {
  id: string;
  question: string;
  answer: string;
  expert: {
    id: string;
    name: string;
    avatar?: string;
    title: string;
    expertise: string[];
    rating: number;
  };
  category: string;
  tags: string[];
  status: 'pending' | 'answered' | 'published' | 'archived';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  askedBy: {
    id: string;
    name: string;
    avatar?: string;
  };
  views: number;
  likes: number;
  helpful: number;
  createdAt: string;
  answeredAt?: string;
  publishedAt?: string;
  isPublic: boolean;
  relatedContent?: string[];
}

export interface ContentStats {
  totalContent: number;
  publishedContent: number;
  draftContent: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  expertQACount: number;
  pendingQACount: number;
  topCategories: { name: string; count: number }[];
  topTags: { name: string; count: number }[];
}

// 模拟数据
const mockContentItems: ContentItem[] = [
  {
    id: '1',
    title: 'React Hooks 完全指南',
    content: '这是一篇关于React Hooks的详细教程...',
    type: 'article',
    category: '前端开发',
    tags: ['React', 'Hooks', 'JavaScript'],
    author: {
      id: 'author1',
      name: '张三',
      avatar: 'https://example.com/avatar1.jpg',
      isExpert: true
    },
    status: 'published',
    visibility: 'public',
    difficulty: 'intermediate',
    estimatedReadTime: 15,
    views: 1250,
    likes: 89,
    comments: 23,
    rating: 4.5,
    ratingCount: 45,
    thumbnail: 'https://example.com/thumb1.jpg',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-16T14:30:00Z',
    publishedAt: '2024-01-16T14:30:00Z',
    featured: true,
    seoKeywords: ['React', 'Hooks', '前端'],
    summary: '深入浅出地介绍React Hooks的使用方法和最佳实践'
  },
  {
    id: '2',
    title: 'TypeScript 进阶技巧',
    content: 'TypeScript的高级特性和使用技巧...',
    type: 'article',
    category: '前端开发',
    tags: ['TypeScript', '类型系统', 'JavaScript'],
    author: {
      id: 'author2',
      name: '李四',
      avatar: 'https://example.com/avatar2.jpg',
      isExpert: true
    },
    status: 'published',
    visibility: 'premium',
    difficulty: 'advanced',
    estimatedReadTime: 25,
    views: 890,
    likes: 67,
    comments: 18,
    rating: 4.7,
    ratingCount: 32,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-11T16:20:00Z',
    publishedAt: '2024-01-11T16:20:00Z',
    featured: false,
    summary: 'TypeScript高级类型系统和实用技巧详解'
  },
  {
    id: '3',
    title: 'Node.js 性能优化实战',
    content: 'Node.js应用性能优化的实战经验分享...',
    type: 'video',
    category: '后端开发',
    tags: ['Node.js', '性能优化', '后端'],
    author: {
      id: 'author3',
      name: '王五',
      avatar: 'https://example.com/avatar3.jpg',
      isExpert: true
    },
    status: 'draft',
    visibility: 'public',
    difficulty: 'intermediate',
    estimatedReadTime: 30,
    views: 0,
    likes: 0,
    comments: 0,
    rating: 0,
    ratingCount: 0,
    createdAt: '2024-01-20T11:00:00Z',
    updatedAt: '2024-01-20T11:00:00Z',
    featured: false,
    summary: 'Node.js应用性能瓶颈分析和优化策略'
  }
];

const mockCategories: ContentCategory[] = [
  {
    id: '1',
    name: '前端开发',
    description: '前端技术相关内容',
    icon: '💻',
    color: '#1890ff',
    order: 1,
    isActive: true,
    contentCount: 45
  },
  {
    id: '2',
    name: '后端开发',
    description: '后端技术相关内容',
    icon: '⚙️',
    color: '#52c41a',
    order: 2,
    isActive: true,
    contentCount: 32
  },
  {
    id: '3',
    name: '数据库',
    description: '数据库相关技术',
    icon: '🗄️',
    color: '#722ed1',
    order: 3,
    isActive: true,
    contentCount: 28
  },
  {
    id: '4',
    name: '运维部署',
    description: '运维和部署相关内容',
    icon: '🚀',
    color: '#fa8c16',
    order: 4,
    isActive: true,
    contentCount: 19
  },
  {
    id: '5',
    name: '设计模式',
    description: '软件设计模式和架构',
    icon: '🏗️',
    color: '#eb2f96',
    order: 5,
    isActive: true,
    contentCount: 15
  }
];

const mockExpertQAs: ExpertQA[] = [
  {
    id: '1',
    question: 'React中useEffect的依赖数组应该如何正确使用？',
    answer: 'useEffect的依赖数组是用来控制effect何时重新执行的关键机制...',
    expert: {
      id: 'expert1',
      name: '张专家',
      avatar: 'https://example.com/expert1.jpg',
      title: '高级前端工程师',
      expertise: ['React', 'JavaScript', 'TypeScript'],
      rating: 4.8
    },
    category: '前端开发',
    tags: ['React', 'useEffect', 'Hooks'],
    status: 'published',
    priority: 'medium',
    askedBy: {
      id: 'user1',
      name: '小明',
      avatar: 'https://example.com/user1.jpg'
    },
    views: 567,
    likes: 34,
    helpful: 28,
    createdAt: '2024-01-18T14:00:00Z',
    answeredAt: '2024-01-18T16:30:00Z',
    publishedAt: '2024-01-18T17:00:00Z',
    isPublic: true,
    relatedContent: ['1']
  },
  {
    id: '2',
    question: 'TypeScript中的泛型约束如何使用？',
    answer: '泛型约束允许我们限制泛型参数必须符合某种条件...',
    expert: {
      id: 'expert2',
      name: '李专家',
      avatar: 'https://example.com/expert2.jpg',
      title: '资深TypeScript开发者',
      expertise: ['TypeScript', 'JavaScript', '类型系统'],
      rating: 4.9
    },
    category: '前端开发',
    tags: ['TypeScript', '泛型', '类型系统'],
    status: 'answered',
    priority: 'high',
    askedBy: {
      id: 'user2',
      name: '小红',
      avatar: 'https://example.com/user2.jpg'
    },
    views: 423,
    likes: 29,
    helpful: 25,
    createdAt: '2024-01-19T10:00:00Z',
    answeredAt: '2024-01-19T15:20:00Z',
    isPublic: true,
    relatedContent: ['2']
  },
  {
    id: '3',
    question: 'Node.js中如何处理大文件上传？',
    answer: '',
    expert: {
      id: 'expert3',
      name: '王专家',
      avatar: 'https://example.com/expert3.jpg',
      title: '后端架构师',
      expertise: ['Node.js', '后端架构', '性能优化'],
      rating: 4.7
    },
    category: '后端开发',
    tags: ['Node.js', '文件上传', '性能'],
    status: 'pending',
    priority: 'urgent',
    askedBy: {
      id: 'user3',
      name: '小刚',
      avatar: 'https://example.com/user3.jpg'
    },
    views: 89,
    likes: 5,
    helpful: 0,
    createdAt: '2024-01-20T09:00:00Z',
    isPublic: true
  }
];

const mockStats: ContentStats = {
  totalContent: 124,
  publishedContent: 89,
  draftContent: 35,
  totalViews: 45678,
  totalLikes: 2345,
  totalComments: 567,
  expertQACount: 78,
  pendingQACount: 12,
  topCategories: [
    { name: '前端开发', count: 45 },
    { name: '后端开发', count: 32 },
    { name: '数据库', count: 28 },
    { name: '运维部署', count: 19 }
  ],
  topTags: [
    { name: 'React', count: 23 },
    { name: 'JavaScript', count: 34 },
    { name: 'TypeScript', count: 18 },
    { name: 'Node.js', count: 15 },
    { name: 'Vue', count: 12 }
  ]
};

// 内容库管理服务
export const contentLibraryService = {
  // 内容管理
  async getAllContent(): Promise<ContentItem[]> {
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockContentItems), 500);
    });
  },

  async getContentById(id: string): Promise<ContentItem | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const content = mockContentItems.find(item => item.id === id);
        resolve(content || null);
      }, 300);
    });
  },

  async createContent(data: Partial<ContentItem>): Promise<ContentItem> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newContent: ContentItem = {
          id: Date.now().toString(),
          title: data.title || '',
          content: data.content || '',
          type: data.type || 'article',
          category: data.category || '',
          tags: data.tags || [],
          author: data.author || {
            id: 'current-user',
            name: '当前用户',
            isExpert: false
          },
          status: data.status || 'draft',
          visibility: data.visibility || 'public',
          difficulty: data.difficulty || 'beginner',
          estimatedReadTime: data.estimatedReadTime || 5,
          views: 0,
          likes: 0,
          comments: 0,
          rating: 0,
          ratingCount: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          featured: data.featured || false,
          summary: data.summary || ''
        };
        mockContentItems.push(newContent);
        resolve(newContent);
      }, 500);
    });
  },

  async updateContent(id: string, data: Partial<ContentItem>): Promise<ContentItem> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockContentItems.findIndex(item => item.id === id);
        if (index === -1) {
          reject(new Error('内容不存在'));
          return;
        }
        
        mockContentItems[index] = {
          ...mockContentItems[index],
          ...data,
          updatedAt: new Date().toISOString()
        };
        resolve(mockContentItems[index]);
      }, 500);
    });
  },

  async deleteContent(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockContentItems.findIndex(item => item.id === id);
        if (index === -1) {
          reject(new Error('内容不存在'));
          return;
        }
        mockContentItems.splice(index, 1);
        resolve();
      }, 300);
    });
  },

  async searchContent(query: string, filters?: {
    category?: string;
    status?: string;
    type?: string;
  }): Promise<ContentItem[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let results = mockContentItems.filter(item => 
          item.title.toLowerCase().includes(query.toLowerCase()) ||
          item.content.toLowerCase().includes(query.toLowerCase()) ||
          item.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
        );

        if (filters) {
          if (filters.category && filters.category !== 'all') {
            results = results.filter(item => item.category === filters.category);
          }
          if (filters.status && filters.status !== 'all') {
            results = results.filter(item => item.status === filters.status);
          }
          if (filters.type && filters.type !== 'all') {
            results = results.filter(item => item.type === filters.type);
          }
        }

        resolve(results);
      }, 400);
    });
  },

  // 分类管理
  async getCategories(): Promise<ContentCategory[]> {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockCategories), 300);
    });
  },

  async createCategory(data: Partial<ContentCategory>): Promise<ContentCategory> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newCategory: ContentCategory = {
          id: Date.now().toString(),
          name: data.name || '',
          description: data.description || '',
          parentId: data.parentId,
          icon: data.icon,
          color: data.color,
          order: data.order || mockCategories.length + 1,
          isActive: data.isActive !== undefined ? data.isActive : true,
          contentCount: 0
        };
        mockCategories.push(newCategory);
        resolve(newCategory);
      }, 400);
    });
  },

  async updateCategory(id: string, data: Partial<ContentCategory>): Promise<ContentCategory> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockCategories.findIndex(cat => cat.id === id);
        if (index === -1) {
          reject(new Error('分类不存在'));
          return;
        }
        
        mockCategories[index] = {
          ...mockCategories[index],
          ...data
        };
        resolve(mockCategories[index]);
      }, 400);
    });
  },

  async deleteCategory(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockCategories.findIndex(cat => cat.id === id);
        if (index === -1) {
          reject(new Error('分类不存在'));
          return;
        }
        mockCategories.splice(index, 1);
        resolve();
      }, 300);
    });
  },

  // 专家问答管理
  async getExpertQAs(): Promise<ExpertQA[]> {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockExpertQAs), 400);
    });
  },

  async getExpertQAById(id: string): Promise<ExpertQA | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const qa = mockExpertQAs.find(item => item.id === id);
        resolve(qa || null);
      }, 300);
    });
  },

  async createExpertQA(data: Partial<ExpertQA>): Promise<ExpertQA> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newQA: ExpertQA = {
          id: Date.now().toString(),
          question: data.question || '',
          answer: data.answer || '',
          expert: data.expert || {
            id: 'current-expert',
            name: '当前专家',
            title: '专家',
            expertise: [],
            rating: 0
          },
          category: data.category || '',
          tags: data.tags || [],
          status: data.status || 'pending',
          priority: data.priority || 'medium',
          askedBy: data.askedBy || {
            id: 'anonymous',
            name: '匿名用户'
          },
          views: 0,
          likes: 0,
          helpful: 0,
          createdAt: new Date().toISOString(),
          isPublic: data.isPublic !== undefined ? data.isPublic : true
        };
        mockExpertQAs.push(newQA);
        resolve(newQA);
      }, 500);
    });
  },

  async updateExpertQA(id: string, data: Partial<ExpertQA>): Promise<ExpertQA> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockExpertQAs.findIndex(qa => qa.id === id);
        if (index === -1) {
          reject(new Error('问答不存在'));
          return;
        }
        
        const updatedQA = {
          ...mockExpertQAs[index],
          ...data
        };

        // 如果状态变为已回答，设置回答时间
        if (data.status === 'answered' && mockExpertQAs[index].status !== 'answered') {
          updatedQA.answeredAt = new Date().toISOString();
        }

        // 如果状态变为已发布，设置发布时间
        if (data.status === 'published' && mockExpertQAs[index].status !== 'published') {
          updatedQA.publishedAt = new Date().toISOString();
        }

        mockExpertQAs[index] = updatedQA;
        resolve(updatedQA);
      }, 500);
    });
  },

  async deleteExpertQA(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockExpertQAs.findIndex(qa => qa.id === id);
        if (index === -1) {
          reject(new Error('问答不存在'));
          return;
        }
        mockExpertQAs.splice(index, 1);
        resolve();
      }, 300);
    });
  },

  // 统计数据
  async getStats(): Promise<ContentStats> {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockStats), 300);
    });
  },

  // 标签管理
  async getAllTags(): Promise<string[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const allTags = new Set<string>();
        mockContentItems.forEach(item => {
          item.tags.forEach(tag => allTags.add(tag));
        });
        mockExpertQAs.forEach(qa => {
          qa.tags.forEach(tag => allTags.add(tag));
        });
        resolve(Array.from(allTags));
      }, 300);
    });
  },

  async getPopularTags(limit: number = 10): Promise<{ name: string; count: number }[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const tagCounts = new Map<string, number>();
        
        mockContentItems.forEach(item => {
          item.tags.forEach(tag => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
          });
        });

        mockExpertQAs.forEach(qa => {
          qa.tags.forEach(tag => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
          });
        });

        const sortedTags = Array.from(tagCounts.entries())
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, limit);

        resolve(sortedTags);
      }, 300);
    });
  },

  // 批量操作
  async batchUpdateContentStatus(ids: string[], status: ContentItem['status']): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        ids.forEach(id => {
          const index = mockContentItems.findIndex(item => item.id === id);
          if (index !== -1) {
            mockContentItems[index].status = status;
            mockContentItems[index].updatedAt = new Date().toISOString();
            if (status === 'published') {
              mockContentItems[index].publishedAt = new Date().toISOString();
            }
          }
        });
        resolve();
      }, 600);
    });
  },

  async batchDeleteContent(ids: string[]): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        ids.forEach(id => {
          const index = mockContentItems.findIndex(item => item.id === id);
          if (index !== -1) {
            mockContentItems.splice(index, 1);
          }
        });
        resolve();
      }, 500);
    });
  },

  // 内容导入导出
  async exportContent(format: 'json' | 'csv' = 'json'): Promise<string> {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (format === 'json') {
          resolve(JSON.stringify(mockContentItems, null, 2));
        } else {
          // 简化的CSV导出
          const headers = ['ID', '标题', '类型', '分类', '状态', '创建时间'];
          const rows = mockContentItems.map(item => [
            item.id,
            item.title,
            item.type,
            item.category,
            item.status,
            item.createdAt
          ]);
          const csv = [headers, ...rows].map(row => row.join(',')).join('\n');
          resolve(csv);
        }
      }, 800);
    });
  },

  async importContent(data: string, format: 'json' | 'csv' = 'json'): Promise<number> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          if (format === 'json') {
            const importedItems = JSON.parse(data) as ContentItem[];
            importedItems.forEach(item => {
              item.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
              item.createdAt = new Date().toISOString();
              item.updatedAt = new Date().toISOString();
              mockContentItems.push(item);
            });
            resolve(importedItems.length);
          } else {
            reject(new Error('CSV导入功能暂未实现'));
          }
        } catch (error) {
          reject(new Error('导入数据格式错误'));
        }
      }, 1000);
    });
  }
};

export default contentLibraryService;