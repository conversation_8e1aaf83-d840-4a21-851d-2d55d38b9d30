// 伙伴系统服务
export interface Partner {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  type: 'individual' | 'organization' | 'brand';
  status: 'pending' | 'active' | 'suspended' | 'rejected';
  level: 'bronze' | 'silver' | 'gold' | 'platinum';
  avatar?: string;
  description: string;
  website?: string;
  socialMedia?: {
    weibo?: string;
    wechat?: string;
    douyin?: string;
  };
  joinDate: string;
  lastActiveDate: string;
  totalContributions: number;
  rating: number;
  verificationStatus: 'unverified' | 'verified' | 'rejected';
  contractInfo?: {
    contractNumber: string;
    startDate: string;
    endDate: string;
    revenue: number;
  };
}

export interface PartnerStats {
  totalPartners: number;
  activePartners: number;
  pendingApplications: number;
  monthlyRevenue: number;
  topPerformers: Partner[];
}

export interface PartnerApplication {
  id: string;
  applicantName: string;
  email: string;
  phone: string;
  company: string;
  type: 'individual' | 'organization' | 'brand';
  description: string;
  website?: string;
  socialMedia?: {
    weibo?: string;
    wechat?: string;
    douyin?: string;
  };
  applicationDate: string;
  status: 'pending' | 'approved' | 'rejected';
  reviewNotes?: string;
  documents?: string[];
}

export interface PartnerContract {
  id: string;
  partnerId: string;
  contractNumber: string;
  title: string;
  startDate: string;
  endDate: string;
  revenue: number;
  commissionRate: number;
  terms: string;
  status: 'draft' | 'active' | 'expired' | 'terminated';
  signedDate?: string;
  documents?: string[];
}

export interface PartnerPerformance {
  partnerId: string;
  period: string;
  totalRevenue: number;
  totalCommission: number;
  totalOrders: number;
  conversionRate: number;
  customerSatisfaction: number;
  contentContributions: number;
  socialMediaReach: number;
}

// 模拟数据
const mockPartners: Partner[] = [
  {
    id: '1',
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    company: '正能量科技有限公司',
    type: 'organization',
    status: 'active',
    level: 'gold',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Zhang',
    description: '专注于正能量内容创作和社区建设的科技公司',
    website: 'https://positive-tech.com',
    socialMedia: {
      weibo: '@正能量科技',
      wechat: 'positive_tech',
      douyin: 'positive_tech_official'
    },
    joinDate: '2023-01-15',
    lastActiveDate: '2024-01-10',
    totalContributions: 156,
    rating: 4.8,
    verificationStatus: 'verified',
    contractInfo: {
      contractNumber: 'CT-2023-001',
      startDate: '2023-01-15',
      endDate: '2024-01-15',
      revenue: 125000
    }
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    company: '心理健康工作室',
    type: 'individual',
    status: 'active',
    level: 'silver',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Li',
    description: '专业心理咨询师，致力于传播心理健康知识',
    website: 'https://mentalhealth-studio.com',
    socialMedia: {
      weibo: '@心理健康李老师',
      wechat: 'mental_health_li'
    },
    joinDate: '2023-03-20',
    lastActiveDate: '2024-01-08',
    totalContributions: 89,
    rating: 4.6,
    verificationStatus: 'verified'
  },
  {
    id: '3',
    name: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
    company: '阳光品牌',
    type: 'brand',
    status: 'pending',
    level: 'bronze',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Wang',
    description: '致力于传播正能量的生活方式品牌',
    website: 'https://sunshine-brand.com',
    socialMedia: {
      weibo: '@阳光品牌官方',
      wechat: 'sunshine_brand',
      douyin: 'sunshine_brand_official'
    },
    joinDate: '2023-12-01',
    lastActiveDate: '2023-12-15',
    totalContributions: 23,
    rating: 4.2,
    verificationStatus: 'unverified'
  },
  {
    id: '4',
    name: '赵六',
    email: '<EMAIL>',
    phone: '13800138004',
    company: '教育培训机构',
    type: 'organization',
    status: 'suspended',
    level: 'silver',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Zhao',
    description: '专注于青少年正能量教育的培训机构',
    website: 'https://positive-education.com',
    socialMedia: {
      weibo: '@正能量教育',
      wechat: 'positive_education'
    },
    joinDate: '2023-06-10',
    lastActiveDate: '2023-11-20',
    totalContributions: 67,
    rating: 3.9,
    verificationStatus: 'verified',
    contractInfo: {
      contractNumber: 'CT-2023-002',
      startDate: '2023-06-10',
      endDate: '2024-06-10',
      revenue: 85000
    }
  }
];

const mockStats: PartnerStats = {
  totalPartners: 4,
  activePartners: 2,
  pendingApplications: 1,
  monthlyRevenue: 18500,
  topPerformers: mockPartners.slice(0, 3)
};

const mockApplications: PartnerApplication[] = [
  {
    id: '1',
    applicantName: '新申请者',
    email: '<EMAIL>',
    phone: '13800138005',
    company: '新兴科技公司',
    type: 'organization',
    description: '专注于AI和正能量结合的创新公司',
    website: 'https://new-tech.com',
    socialMedia: {
      weibo: '@新兴科技',
      wechat: 'new_tech_ai'
    },
    applicationDate: '2024-01-05',
    status: 'pending'
  }
];

const mockContracts: PartnerContract[] = [
  {
    id: '1',
    partnerId: '1',
    contractNumber: 'CT-2023-001',
    title: '正能量科技合作协议',
    startDate: '2023-01-15',
    endDate: '2024-01-15',
    revenue: 125000,
    commissionRate: 15,
    terms: '双方合作推广正能量内容，按照约定分成比例进行收益分配...',
    status: 'active',
    signedDate: '2023-01-15',
    documents: ['contract-001.pdf', 'supplement-001.pdf']
  },
  {
    id: '2',
    partnerId: '4',
    contractNumber: 'CT-2023-002',
    title: '教育培训机构合作协议',
    startDate: '2023-06-10',
    endDate: '2024-06-10',
    revenue: 85000,
    commissionRate: 12,
    terms: '合作开展青少年正能量教育项目，共同推广相关课程...',
    status: 'active',
    signedDate: '2023-06-10',
    documents: ['contract-002.pdf']
  }
];

const mockPerformance: PartnerPerformance[] = [
  {
    partnerId: '1',
    period: '2024-01',
    totalRevenue: 15000,
    totalCommission: 2250,
    totalOrders: 45,
    conversionRate: 12.5,
    customerSatisfaction: 4.8,
    contentContributions: 23,
    socialMediaReach: 15000
  },
  {
    partnerId: '2',
    period: '2024-01',
    totalRevenue: 8500,
    totalCommission: 1020,
    totalOrders: 28,
    conversionRate: 9.8,
    customerSatisfaction: 4.6,
    contentContributions: 15,
    socialMediaReach: 8500
  }
];

export const partnerService = {
  // 获取所有伙伴
  getAllPartners: async (): Promise<Partner[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockPartners;
  },

  // 获取伙伴统计数据
  getPartnerStats: async (): Promise<PartnerStats> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockStats;
  },

  // 获取单个伙伴详情
  getPartnerById: async (id: string): Promise<Partner | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockPartners.find(partner => partner.id === id) || null;
  },

  // 创建伙伴
  createPartner: async (partnerData: Omit<Partner, 'id' | 'joinDate' | 'lastActiveDate' | 'totalContributions' | 'rating' | 'verificationStatus'>): Promise<Partner> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const newPartner: Partner = {
      ...partnerData,
      id: Date.now().toString(),
      joinDate: new Date().toISOString().split('T')[0],
      lastActiveDate: new Date().toISOString().split('T')[0],
      totalContributions: 0,
      rating: 0,
      verificationStatus: 'unverified'
    };
    mockPartners.push(newPartner);
    return newPartner;
  },

  // 更新伙伴信息
  updatePartner: async (id: string, partnerData: Partial<Partner>): Promise<Partner> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    const index = mockPartners.findIndex(partner => partner.id === id);
    if (index === -1) {
      throw new Error('伙伴不存在');
    }
    mockPartners[index] = { ...mockPartners[index], ...partnerData };
    return mockPartners[index];
  },

  // 更新伙伴状态
  updatePartnerStatus: async (id: string, status: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    const partner = mockPartners.find(p => p.id === id);
    if (partner) {
      partner.status = status as Partner['status'];
    }
  },

  // 删除伙伴
  deletePartner: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    const index = mockPartners.findIndex(partner => partner.id === id);
    if (index !== -1) {
      mockPartners.splice(index, 1);
    }
  },

  // 获取伙伴申请列表
  getPartnerApplications: async (): Promise<PartnerApplication[]> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    return mockApplications;
  },

  // 处理伙伴申请
  processPartnerApplication: async (id: string, status: 'approved' | 'rejected', reviewNotes?: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    const application = mockApplications.find(app => app.id === id);
    if (application) {
      application.status = status;
      application.reviewNotes = reviewNotes;
      
      // 如果批准，创建新伙伴
      if (status === 'approved') {
        const newPartner: Partner = {
          id: Date.now().toString(),
          name: application.applicantName,
          email: application.email,
          phone: application.phone,
          company: application.company,
          type: application.type,
          status: 'active',
          level: 'bronze',
          description: application.description,
          website: application.website,
          socialMedia: application.socialMedia,
          joinDate: new Date().toISOString().split('T')[0],
          lastActiveDate: new Date().toISOString().split('T')[0],
          totalContributions: 0,
          rating: 0,
          verificationStatus: 'unverified'
        };
        mockPartners.push(newPartner);
      }
    }
  },

  // 获取伙伴合同列表
  getPartnerContracts: async (partnerId?: string): Promise<PartnerContract[]> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    if (partnerId) {
      return mockContracts.filter(contract => contract.partnerId === partnerId);
    }
    return mockContracts;
  },

  // 创建伙伴合同
  createPartnerContract: async (contractData: Omit<PartnerContract, 'id'>): Promise<PartnerContract> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const newContract: PartnerContract = {
      ...contractData,
      id: Date.now().toString()
    };
    mockContracts.push(newContract);
    return newContract;
  },

  // 更新伙伴合同
  updatePartnerContract: async (id: string, contractData: Partial<PartnerContract>): Promise<PartnerContract> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    const index = mockContracts.findIndex(contract => contract.id === id);
    if (index === -1) {
      throw new Error('合同不存在');
    }
    mockContracts[index] = { ...mockContracts[index], ...contractData };
    return mockContracts[index];
  },

  // 获取伙伴绩效数据
  getPartnerPerformance: async (partnerId: string, period?: string): Promise<PartnerPerformance[]> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    let performance = mockPerformance.filter(perf => perf.partnerId === partnerId);
    if (period) {
      performance = performance.filter(perf => perf.period === period);
    }
    return performance;
  },

  // 获取所有伙伴绩效数据
  getAllPartnerPerformance: async (period?: string): Promise<PartnerPerformance[]> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    if (period) {
      return mockPerformance.filter(perf => perf.period === period);
    }
    return mockPerformance;
  },

  // 验证伙伴
  verifyPartner: async (id: string, status: 'verified' | 'rejected', notes?: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const partner = mockPartners.find(p => p.id === id);
    if (partner) {
      partner.verificationStatus = status;
    }
  },

  // 批量操作伙伴
  batchUpdatePartners: async (ids: string[], updates: Partial<Partner>): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    ids.forEach(id => {
      const partner = mockPartners.find(p => p.id === id);
      if (partner) {
        Object.assign(partner, updates);
      }
    });
  },

  // 导出伙伴数据
  exportPartnerData: async (format: 'csv' | 'excel' = 'csv'): Promise<string> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    // 模拟导出功能，返回下载链接
    return `https://api.example.com/export/partners.${format}`;
  },

  // 搜索伙伴
  searchPartners: async (query: string, filters?: {
    type?: string;
    status?: string;
    level?: string;
  }): Promise<Partner[]> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    let results = mockPartners.filter(partner => 
      partner.name.toLowerCase().includes(query.toLowerCase()) ||
      partner.company.toLowerCase().includes(query.toLowerCase()) ||
      partner.email.toLowerCase().includes(query.toLowerCase())
    );

    if (filters) {
      if (filters.type && filters.type !== 'all') {
        results = results.filter(partner => partner.type === filters.type);
      }
      if (filters.status && filters.status !== 'all') {
        results = results.filter(partner => partner.status === filters.status);
      }
      if (filters.level && filters.level !== 'all') {
        results = results.filter(partner => partner.level === filters.level);
      }
    }

    return results;
  }
};