import { supabase } from '../utils/supabase';

export interface Post {
  id: string;
  title: string;
  content: string;
  user_id: string;
  status: 'published' | 'draft' | 'archived';
  view_count: number;
  like_count: number;
  comment_count: number;
  created_at: string;
  updated_at?: string | null;
  category_id?: string | null;
  is_featured: boolean;
}

export interface Comment {
  id: string;
  post_id: string;
  user_id: string;
  content: string;
  status: 'approved' | 'pending' | 'rejected';
  created_at: string;
  updated_at?: string | null;
}

export interface Category {
  id: string;
  name: string;
  color: string;
  description?: string | null;
  created_at: string;
}

export const contentService = {
  // 帖子相关方法
  async getAllPosts(): Promise<{ success: boolean; data?: any[] | null; error?: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles:user_id(username)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // 将 users 映射为 profiles，categories 兼容为 null
      const mapped = (data || []).map((row: any) => {
        const { profiles, ...rest } = row
        const profilesObj = profiles ? { username: profiles.username, avatar_url: undefined } : undefined
        return { ...rest, profiles: profilesObj, categories: null }
      })

      return { success: true, data: mapped };
    } catch (error) {
      console.error('Error fetching posts:', error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async getPostById(id: string): Promise<{ success: boolean; data?: any | null; error?: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles:user_id(username)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      // 兼容字段：profiles、categories、comments
      const { profiles, ...rest } = (data as any) || {}
      const profilesObj = profiles ? { username: profiles.username, avatar_url: undefined } : undefined
      const mapped = { ...rest, profiles: profilesObj, categories: null, comments: [] as any[] }

      return { success: true, data: mapped };
    } catch (error) {
      console.error(`Error fetching post ${id}:`, error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async createPost(post: Omit<Post, 'id' | 'created_at' | 'updated_at' | 'view_count' | 'like_count' | 'comment_count'>): Promise<{ success: boolean; data?: Post | null; error?: Error | null }> {
    try {
      const { data, error } = await (supabase as any)
        .from('posts')
        .insert([{
          ...post,
          view_count: 0,
          like_count: 0,
          comment_count: 0
        }])
        .select()
        .single();

      if (error) throw error;
      return { success: true, data: data as Post };
    } catch (error) {
      console.error('Error creating post:', error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async updatePost(id: string, updates: Partial<Post>): Promise<{ success: boolean; data?: Post | null; error?: Error | null }> {
    try {
      const { data, error } = await (supabase as any)
        .from('posts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { success: true, data: data as Post };
    } catch (error) {
      console.error(`Error updating post ${id}:`, error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async deletePost(id: string): Promise<{ success: boolean; error?: Error | null }> {
    try {
      const { error } = await supabase
        .from('posts')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error(`Error deleting post ${id}:`, error);
      return { success: false, error: error as Error };
    }
  },

  async searchPosts(query: string): Promise<{ success: boolean; data?: any[] | null; error?: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles:user_id(username)
        `)
        .or(`title.ilike.%${query}%,content.ilike.%${query}%`)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const mapped = (data || []).map((row: any) => {
        const { profiles, ...rest } = row
        const profilesObj = profiles ? { username: profiles.username, avatar_url: undefined } : undefined
        return { ...rest, profiles: profilesObj, categories: null }
      })

      return { success: true, data: mapped };
    } catch (error) {
      console.error('Error searching posts:', error);
      return { success: false, error: error as Error, data: null };
    }
  },

  async updatePostStatus(id: string, status: 'published' | 'draft' | 'archived'): Promise<{ success: boolean; error?: Error | null }> {
    return this.updatePost(id, { status });
  },

  async toggleFeatured(id: string, isFeatured: boolean): Promise<{ success: boolean; error?: Error | null }> {
    return this.updatePost(id, { is_featured: isFeatured });
  },

  // 评论相关方法 - 安全降级：返回空数组，避免访问不存在的表
  async getCommentsByPostId(postId: string): Promise<{ success: boolean; data?: Comment[] | null; error?: Error | null }> {
    try {
      console.warn('comments 表未启用：getCommentsByPostId 返回空数组');
      return { success: true, data: [] };
    } catch (error) {
      return { success: false, error: error as Error, data: [] };
    }
  },

  async getAllComments(): Promise<{ success: boolean; data?: Comment[] | null; error?: Error | null }> {
    try {
      console.warn('comments 表未启用：getAllComments 返回空数组');
      return { success: true, data: [] };
    } catch (error) {
      return { success: false, error: error as Error, data: [] };
    }
  },

  async createComment(comment: Omit<Comment, 'id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; data?: Comment | null; error?: Error | null }> {
    try {
      console.warn('comments 表未启用：createComment 忽略操作');
      return { success: true, data: null };
    } catch (error) {
      return { success: false, error: error as Error, data: null };
    }
  },

  async updateComment(id: string, updates: Partial<Comment>): Promise<{ success: boolean; data?: Comment | null; error?: Error | null }> {
    try {
      console.warn('comments 表未启用：updateComment 忽略操作');
      return { success: true, data: null };
    } catch (error) {
      return { success: false, error: error as Error, data: null };
    }
  },

  async deleteComment(id: string): Promise<{ success: boolean; error?: Error | null }> {
    try {
      console.warn('comments 表未启用：deleteComment 忽略操作');
      return { success: true };
    } catch (error) {
      return { success: false, error: error as Error };
    }
  },

  async updateCommentStatus(id: string, status: 'approved' | 'pending' | 'rejected'): Promise<{ success: boolean; error?: Error | null }> {
    console.warn('comments 表未启用：updateCommentStatus 忽略操作');
    return { success: true };
  },

  // 分类相关方法 - 安全降级
  async getAllCategories(): Promise<{ success: boolean; data?: Category[] | null; error?: Error | null }> {
    try {
      console.warn('categories 表未启用：getAllCategories 返回空数组');
      return { success: true, data: [] };
    } catch (error) {
      return { success: false, error: error as Error, data: [] };
    }
  },

  async createCategory(name: string, color: string = 'blue', description?: string): Promise<{ success: boolean; data?: Category | null; error?: Error | null }> {
    console.warn('categories 表未启用：createCategory 忽略操作');
    return { success: false, error: new Error('categories not available'), data: null };
  },

  async updateCategory(id: string, updates: Partial<Category>): Promise<{ success: boolean; data?: Category | null; error?: Error | null }> {
    console.warn('categories 表未启用：updateCategory 忽略操作');
    return { success: false, error: new Error('categories not available'), data: null };
  },

  async deleteCategory(id: string): Promise<{ success: boolean; error?: Error | null }> {
    console.warn('categories 表未启用：deleteCategory 忽略操作');
    return { success: false, error: new Error('categories not available') };
  },

  // 统计相关方法
  async getContentStats(): Promise<{ success: boolean; data?: any; error?: Error | null }> {
    try {
      // 获取帖子总数
      const { count: totalPosts, error: postsError } = await supabase
        .from('posts')
        .select('*', { count: 'exact', head: true });

      if (postsError) throw postsError;

      // 获取今日新增帖子数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const { count: newPosts, error: newPostsError } = await supabase
        .from('posts')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());

      if (newPostsError) throw newPostsError;

      // comments 表未启用：相关统计返回 0
      const totalComments = 0
      const pendingComments = 0

      return {
        success: true,
        data: {
          totalPosts,
          newPosts,
          totalComments,
          pendingComments
        }
      };
    } catch (error) {
      console.error('Error fetching content stats:', error);
      return { success: false, error: error as Error };
    }
  }
};