import { supabase } from '../utils/supabase'

export interface DashboardStats {
  totalUsers: number
  totalPosts: number
  totalCheckins: number
  totalAchievements: number
  userGrowth: number
  postGrowth: number
  checkinGrowth: number
  achievementGrowth: number
}

export interface UserGrowthData {
  date: string
  count: number
}

export interface ContentStats {
  totalPosts: number
  publishedPosts: number
  draftPosts: number
  pendingPosts: number
  rejectedPosts: number
  totalComments: number
  approvedComments: number
  pendingComments: number
}

export interface UserStats {
  totalUsers: number
  activeUsers: number
  premiumUsers: number
  bannedUsers: number
  newUsersToday: number
  newUsersThisWeek: number
  newUsersThisMonth: number
}

export const statisticsService = {
  // 获取仪表盘统计数据
  async getDashboardStats(): Promise<{ success: boolean; data?: DashboardStats; error?: string }> {
    try {
      // 从Supabase获取真实数据
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })

      // 获取用户统计数据
      const { data: profilesData } = await supabase
        .from('profiles')
        .select('level, flow_value, streak, is_premium')

      // 计算统计数据
      const totalPosts = 0 // 需要实现posts表
      const totalCheckins = profilesData?.reduce((sum: number, profile: any) => sum + (profile.streak || 0), 0) || 0
      const totalAchievements = 0 // 需要实现achievements表
      
      // 计算增长率（简化版本，实际需要对比历史数据）
      const userGrowth = 5.2 // 模拟数据
      const postGrowth = 3.1
      const checkinGrowth = 8.7
      const achievementGrowth = 2.4

      return {
        success: true,
        data: {
          totalUsers: totalUsers || 0,
          totalPosts,
          totalCheckins,
          totalAchievements,
          userGrowth,
          postGrowth,
          checkinGrowth,
          achievementGrowth
        }
      }
    } catch (error) {
      console.error('获取仪表盘统计数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取统计数据失败'
      }
    }
  },


  // 获取用户增长数据（最近30天）
  async getUserGrowthData(): Promise<{ success: boolean; data?: UserGrowthData[]; error?: string }> {
    try {
      // 获取最近30天的用户注册数据
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      const { data: usersData } = await supabase
        .from('profiles')
        .select('created_at')
        .gte('created_at', thirtyDaysAgo.toISOString())
        .order('created_at', { ascending: true })

      if (!usersData) {
        return { success: true, data: [] }
      }

      // 按日期分组统计
      const growthMap = new Map<string, number>()
      
      usersData.forEach((user: any) => {
        const date = new Date(user.created_at).toISOString().split('T')[0]
        growthMap.set(date, (growthMap.get(date) || 0) + 1)
      })

      // 转换为数组格式
      const growthData: UserGrowthData[] = Array.from(growthMap.entries()).map(([date, count]) => ({
        date,
        count
      }))

      return {
        success: true,
        data: growthData
      }
    } catch (error) {
      console.error('获取用户增长数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取用户增长数据失败'
      }
    }
  },

  // 获取内容统计数据
  async getContentStats(): Promise<{ success: boolean; data?: ContentStats; error?: string }> {
    try {
      // 基于profiles表中的统计字段获取内容数据
      const { data: profilesData } = await supabase
        .from('profiles')
        .select('posts_count, comments_posted')
      
      // 计算总帖子数（基于用户的posts_count字段）
      const totalPosts = profilesData?.reduce((sum, profile: any) => sum + (profile.posts_count || 0), 0) || 0
      
      // 计算总评论数（基于用户的comments_posted字段）
      const totalComments = profilesData?.reduce((sum, profile: any) => sum + (profile.comments_posted || 0), 0) || 0
      
      // 由于没有posts和comments表，使用估算数据
      // 假设80%的帖子是已发布状态，15%是草稿，5%是待审核
      const publishedPosts = Math.round(totalPosts * 0.8)
      const draftPosts = Math.round(totalPosts * 0.15)
      const pendingPosts = Math.round(totalPosts * 0.05)
      const rejectedPosts = 0
      
      // 假设90%的评论是已批准状态，10%是待审核
      const approvedComments = Math.round(totalComments * 0.9)
      const pendingComments = Math.round(totalComments * 0.1)
      
      return {
        success: true,
        data: {
          totalPosts,
          publishedPosts,
          draftPosts,
          pendingPosts,
          rejectedPosts,
          totalComments,
          approvedComments,
          pendingComments
        }
      }
    } catch (error) {
      console.error('获取内容统计数据失败:', error)
      return {
        success: false,
        error: '获取内容统计数据失败'
      }
    }
  },

  // 获取用户统计数据
  async getUserStats(): Promise<{ success: boolean; data?: UserStats; error?: string }> {
    try {
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
      
      // 活跃用户（最近7天有登录）
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      
      let activeUsers = 0
      {
        const { count, error } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .gte('last_active_at', sevenDaysAgo.toISOString())
        if (error) {
          const { count: count2, error: error2 } = await supabase
            .from('profiles')
            .select('*', { count: 'exact', head: true })
            .gte('last_sign_in_at', sevenDaysAgo.toISOString())
          if (!error2) activeUsers = count2 || 0
        } else {
          activeUsers = count || 0
        }
      }
      
      let premiumUsers = 0
      {
        const { count, error } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('is_premium', true)
        if (!error) premiumUsers = count || 0
      }
      
      let bannedUsers = 0
      {
        const { count, error } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('is_active', false)
        if (!error) bannedUsers = count || 0
      }
      
      // 今日新用户
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const { count: newUsersToday } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString())
      
      // 本周新用户
      const thisWeek = new Date()
      thisWeek.setDate(thisWeek.getDate() - thisWeek.getDay())
      thisWeek.setHours(0, 0, 0, 0)
      
      const { count: newUsersThisWeek } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', thisWeek.toISOString())
      
      // 本月新用户
      const thisMonth = new Date()
      thisMonth.setDate(1)
      thisMonth.setHours(0, 0, 0, 0)
      
      const { count: newUsersThisMonth } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', thisMonth.toISOString())
      
      return {
        success: true,
        data: {
          totalUsers: totalUsers || 0,
          activeUsers: activeUsers || 0,
          premiumUsers: premiumUsers || 0,
          bannedUsers: bannedUsers || 0,
          newUsersToday: newUsersToday || 0,
          newUsersThisWeek: newUsersThisWeek || 0,
          newUsersThisMonth: newUsersThisMonth || 0
        }
      }
    } catch (error) {
      console.error('获取用户统计数据失败:', error)
      return {
        success: false,
        error: '获取用户统计数据失败'
      }
    }
  },

  // 获取最近用户列表
  async getRecentUsers(limit: number = 10): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      const { data: usersData } = await supabase
        .from('profiles')
        .select('id, username, created_at, level, is_premium')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (!usersData) {
        return { success: true, data: [] }
      }

      // 转换数据格式以匹配Dashboard组件的期望
      const formattedUsers = usersData.map((user: any) => ({
        id: user.id,
        username: user.username || '未设置',
        email: '', // profiles表中没有email字段，需要从auth.users获取
        created_at: user.created_at,
        level: user.level?.toString() || '0'
      }))

      return {
        success: true,
        data: formattedUsers
      }
    } catch (error) {
      console.error('获取最近用户列表失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取用户列表失败'
      }
    }
  }
}