import { supabase } from '../utils/supabase'

export interface Article {
  id: string
  title: string
  content: string
  summary?: string
  author_id: string
  category: string
  tags?: string[]
  status: 'draft' | 'published' | 'archived'
  is_featured: boolean
  is_premium: boolean
  view_count: number
  like_count: number
  comment_count: number
  reading_time?: number
  cover_image?: string
  seo_title?: string
  seo_description?: string
  published_at?: string
  created_at: string
  updated_at: string
  // 关联作者信息
  author?: {
    username: string
    avatar_url?: string
  }
}

export interface ArticleLike {
  id: string
  article_id: string
  user_id: string
  created_at: string
  // 关联用户信息
  user?: {
    username: string
    avatar_url?: string
  }
}

export interface UserArticleAccess {
  id: string
  user_id: string
  article_id: string
  access_type: 'view' | 'like' | 'share'
  created_at: string
  // 关联用户和文章信息
  user?: {
    username: string
    avatar_url?: string
  }
  article?: {
    title: string
  }
}

export const articleService = {
  // 获取文章列表
  async getArticles(): Promise<{ success: boolean; data?: Article[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('articles')
        .select(`
          *,
          author:profiles!articles_author_id_fkey (
            username,
            avatar_url
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      console.error('获取文章列表失败:', error)
      return {
        success: false,
        error: '获取文章列表失败'
      }
    }
  },

  // 根据ID获取文章详情
  async getArticleById(id: string): Promise<{ success: boolean; data?: Article; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('articles')
        .select(`
          *,
          author:profiles!articles_author_id_fkey (
            username,
            avatar_url
          )
        `)
        .eq('id', id)
        .single()

      if (error) throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('获取文章详情失败:', error)
      return {
        success: false,
        error: '获取文章详情失败'
      }
    }
  },

  // 创建文章
  async createArticle(article: Partial<Article>): Promise<{ success: boolean; data?: Article; error?: string }> {
    try {
      const { data, error } = await (supabase as any)
        .from('articles')
        .insert([article])
        .select(`
          *,
          author:profiles!articles_author_id_fkey (
            username,
            avatar_url
          )
        `)
        .single()

      if (error) throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('创建文章失败:', error)
      return {
        success: false,
        error: '创建文章失败'
      }
    }
  },

  // 更新文章
  async updateArticle(id: string, updates: Partial<Article>): Promise<{ success: boolean; data?: Article; error?: string }> {
    try {
      const { data, error } = await (supabase as any)
        .from('articles')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          author:profiles!articles_author_id_fkey (
            username,
            avatar_url
          )
        `)
        .single()

      if (error) throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('更新文章失败:', error)
      return {
        success: false,
        error: '更新文章失败'
      }
    }
  },

  // 删除文章
  async deleteArticle(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('articles')
        .delete()
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('删除文章失败:', error)
      return {
        success: false,
        error: '删除文章失败'
      }
    }
  },

  // 更新文章状态
  async updateArticleStatus(id: string, status: 'draft' | 'published' | 'archived'): Promise<{ success: boolean; error?: string }> {
    try {
      const updateData: Record<string, any> = { status }
      
      // 如果是发布状态，设置发布时间
      if (status === 'published') {
        updateData.published_at = new Date().toISOString()
      }

      const { error } = await (supabase as any)
        .from('articles')
        .update(updateData)
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('更新文章状态失败:', error)
      return {
        success: false,
        error: '更新文章状态失败'
      }
    }
  },

  // 切换精选状态
  async toggleFeatured(id: string, isFeatured: boolean): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await (supabase as any)
        .from('articles')
        .update({ is_featured: isFeatured })
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('切换精选状态失败:', error)
      return {
        success: false,
        error: '切换精选状态失败'
      }
    }
  },

  // 切换付费状态
  async togglePremium(id: string, isPremium: boolean): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await (supabase as any)
        .from('articles')
        .update({ is_premium: isPremium })
        .eq('id', id)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('切换付费状态失败:', error)
      return {
        success: false,
        error: '切换付费状态失败'
      }
    }
  },

  // 获取文章点赞记录
  async getArticleLikes(articleId?: string): Promise<{ success: boolean; data?: ArticleLike[]; error?: string }> {
    try {
      let query = supabase
        .from('article_likes')
        .select(`
          *,
          user:profiles!article_likes_user_id_fkey (
            username,
            avatar_url
          )
        `)
        .order('created_at', { ascending: false })

      if (articleId) {
        query = query.eq('article_id', articleId)
      }

      const { data, error } = await query

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      console.error('获取文章点赞记录失败:', error)
      return {
        success: false,
        error: '获取文章点赞记录失败'
      }
    }
  },

  // 获取用户文章访问记录
  async getUserArticleAccess(): Promise<{ success: boolean; data?: UserArticleAccess[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('user_article_access')
        .select(`
          *,
          user:profiles!user_article_access_user_id_fkey (
            username,
            avatar_url
          ),
          article:articles!user_article_access_article_id_fkey (
            title
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      console.error('获取用户文章访问记录失败:', error)
      return {
        success: false,
        error: '获取用户文章访问记录失败'
      }
    }
  },

  // 获取文章统计数据
  async getArticleStats(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 获取总文章数
      const { count: totalArticles } = await supabase
        .from('articles')
        .select('*', { count: 'exact', head: true })

      // 获取已发布文章数
      const { count: publishedArticles } = await supabase
        .from('articles')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'published')

      // 获取草稿文章数
      const { count: draftArticles } = await supabase
        .from('articles')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'draft')

      // 获取精选文章数
      const { count: featuredArticles } = await supabase
        .from('articles')
        .select('*', { count: 'exact', head: true })
        .eq('is_featured', true)

      // 获取付费文章数
      const { count: premiumArticles } = await supabase
        .from('articles')
        .select('*', { count: 'exact', head: true })
        .eq('is_premium', true)

      // 获取总浏览量
      const { data: viewData } = await supabase
        .from('articles')
        .select('view_count')

      const totalViews = viewData?.reduce((sum: number, article: any) => sum + (article.view_count || 0), 0) || 0

      // 获取总点赞数
      const { data: likeData } = await supabase
        .from('articles')
        .select('like_count')

      const totalLikes = likeData?.reduce((sum: number, article: any) => sum + (article.like_count || 0), 0) || 0

      // 获取分类分布
      const { data: categoryData } = await supabase
        .from('articles')
        .select('category')

      const categoryStats = categoryData?.reduce((acc: any, item: any) => {
        acc[item.category] = (acc[item.category] || 0) + 1
        return acc
      }, {}) || {}

      return {
        success: true,
        data: {
          totalArticles: totalArticles || 0,
          publishedArticles: publishedArticles || 0,
          draftArticles: draftArticles || 0,
          featuredArticles: featuredArticles || 0,
          premiumArticles: premiumArticles || 0,
          totalViews,
          totalLikes,
          categoryStats
        }
      }
    } catch (error) {
      console.error('获取文章统计数据失败:', error)
      return {
        success: false,
        error: '获取文章统计数据失败'
      }
    }
  }
}