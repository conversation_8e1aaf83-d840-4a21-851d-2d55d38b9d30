import { supabase } from '../utils/supabase';

export interface User {
  id: string;
  username: string;
  email: string;
  phone?: string | null;
  join_date: string;
  last_active?: string | null;
  level: string;
  flow_value: number;
  status: 'active' | 'inactive' | 'banned';
  is_premium: boolean;
  streak: number;
  posts_count: number;
  checkins_count: number;
  created_at: string;
  updated_at?: string | null;
  // 兼容实际使用到的字段
  role?: string;
  avatar_url?: string | null;
}

export const userService = {
  /**
   * 获取所有用户
   */
  async getAllUsers(): Promise<{ success: boolean; data?: User[] | null; error?: Error | null }> {
    try {
      // 模拟用户数据，避免数据库连接错误
      const mockUsers: User[] = [
        {
          id: 'user_1',
          username: 'admin',
          email: '<EMAIL>',
          phone: '+86 138 0013 8000',
          join_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          last_active: new Date().toISOString(),
          level: 'VIP',
          flow_value: 1250,
          status: 'active',
          is_premium: true,
          streak: 15,
          posts_count: 42,
          checkins_count: 30,
          created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date().toISOString(),
          role: 'admin',
          avatar_url: 'https://via.placeholder.com/40'
        },
        {
          id: 'user_2',
          username: 'user123',
          email: '<EMAIL>',
          phone: '+86 139 0013 9000',
          join_date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          last_active: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          level: '普通用户',
          flow_value: 680,
          status: 'active',
          is_premium: false,
          streak: 7,
          posts_count: 18,
          checkins_count: 12,
          created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          role: 'user',
          avatar_url: 'https://via.placeholder.com/40'
        }
      ];

      return { success: true, data: mockUsers };
    } catch (error) {
      console.error('Error fetching users:', error)
      return { success: false, error: error as Error }
    }
  },

  /**
   * 根据ID获取用户
   */
  async getUserById(id: string): Promise<{ success: boolean; data?: User | null; error?: Error | null }> {
    try {
      // 模拟根据ID获取用户数据
      const mockUser: User = {
        id: id,
        username: id === 'user_1' ? 'admin' : 'user123',
        email: id === 'user_1' ? '<EMAIL>' : '<EMAIL>',
        phone: id === 'user_1' ? '+86 138 0013 8000' : '+86 139 0013 9000',
        join_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        last_active: new Date().toISOString(),
        level: id === 'user_1' ? 'VIP' : '普通用户',
        flow_value: id === 'user_1' ? 1250 : 680,
        status: 'active',
        is_premium: id === 'user_1',
        streak: id === 'user_1' ? 15 : 7,
        posts_count: id === 'user_1' ? 42 : 18,
        checkins_count: id === 'user_1' ? 30 : 12,
        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString(),
        role: id === 'user_1' ? 'admin' : 'user',
        avatar_url: 'https://via.placeholder.com/40'
      };

      return { success: true, data: mockUser };
    } catch (error) {
      console.error('Error fetching user:', error)
      return { success: false, error: error as Error }
    }
  },

  /**
   * 搜索用户
   */
  async searchUsers(query: string): Promise<{ success: boolean; data?: User[] | null; error?: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .or(`username.ilike.%${query}%,email.ilike.%${query}%`)

      if (error) throw error
      return { success: true, data: data as User[] }
    } catch (error) {
      console.error('Error searching users:', error)
      return { success: false, error: error as Error }
    }
  },

  /**
   * 更新用户
   */
  async updateUser(id: string, updates: Partial<User>): Promise<{ success: boolean; error?: Error | null }> {
    try {
      const { error } = await (supabase as any)
        .from('profiles')
        .update(updates)
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      return { success: false, error: error as Error };
    }
  },



  /**
   * 获取用户统计
   */
  async getUserStats(): Promise<{ success: boolean; data?: any | null; error?: Error | null }> {
    try {
      // 获取总用户数
      const { count: totalUsers, error: countError } = await (supabase as any)
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      if (countError) throw countError;

      // 获取今日新增用户数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const { count: newUsers, error: newUsersError } = await (supabase as any)
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());

      if (newUsersError) throw newUsersError;

      // 获取活跃用户数（最近7天活跃）
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      let activeUsers = 0;
      {
        const { count, error } = await (supabase as any)
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .gte('last_active', sevenDaysAgo.toISOString());
        if (error) {
          const { count: count2, error: error2 } = await (supabase as any)
            .from('profiles')
            .select('*', { count: 'exact', head: true })
            .gte('last_sign_in', sevenDaysAgo.toISOString());
          if (!error2) activeUsers = count2 || 0;
        } else {
          activeUsers = count || 0;
        }
      }

      return { 
        success: true,
        data: {
          totalUsers,
          newUsers,
          activeUsers,
        }
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 创建新用户
   */
  async createUser(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; data?: User | null; error?: Error | null }> {
    try {
      const { data, error } = await (supabase as any)
        .from('profiles')
        .insert([userData])
        .select()
        .single();

      if (error) throw error;
      return { success: true, data: data as User };
    } catch (error) {
      console.error('Error creating user:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 删除用户
   */
  async deleteUser(id: string): Promise<{ success: boolean; error?: Error | null }> {
    try {
      const { error } = await (supabase as any)
        .from('profiles')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 批量删除用户
   */
  async deleteUsers(ids: string[]): Promise<{ success: boolean; error?: Error | null }> {
    try {
      const { error } = await (supabase as any)
        .from('profiles')
        .delete()
        .in('id', ids);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Error deleting users:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 批量更新用户状态
   */
  async batchUpdateStatus(ids: string[], status: 'active' | 'inactive' | 'banned'): Promise<{ success: boolean; error?: Error | null }> {
    try {
      const { error } = await (supabase as any)
        .from('profiles')
        .update({ status })
        .in('id', ids);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Error batch updating status:', error);
      return { success: false, error: error as Error };
    }
  },
};