import { supabase } from '../utils/supabase';

export interface User {
  id: string;
  username: string;
  email: string;
  phone?: string | null;
  join_date: string;
  last_active?: string | null;
  level: string;
  flow_value: number;
  status: 'active' | 'inactive' | 'banned';
  is_premium: boolean;
  streak: number;
  posts_count: number;
  checkins_count: number;
  created_at: string;
  updated_at?: string | null;
  // 兼容实际使用到的字段
  role?: string;
  avatar_url?: string | null;
}

export const userService = {
  /**
   * 获取所有用户
   */
  async getAllUsers(): Promise<{ success: boolean; data?: User[] | null; error?: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          avatar_url,
          role,
          level,
          flow_value,
          streak_days,
          posts_count,
          checkin_days,
          is_premium,
          is_active,
          created_at,
          updated_at,
          last_active_at,
          last_sign_in_at
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // 转换数据格式以匹配 User 接口
      const users: User[] = (data || []).map((profile: any) => ({
        id: profile.id,
        username: profile.username || '未设置',
        email: '', // 需要从 auth.users 表获取，这里先留空
        phone: null,
        join_date: profile.created_at,
        last_active: profile.last_active_at || profile.last_sign_in_at,
        level: profile.level?.toString() || '1',
        flow_value: profile.flow_value || 0,
        status: profile.is_active ? 'active' : 'inactive',
        is_premium: profile.is_premium || false,
        streak: profile.streak_days || 0,
        posts_count: profile.posts_count || 0,
        checkins_count: profile.checkin_days || 0,
        created_at: profile.created_at,
        updated_at: profile.updated_at,
        role: profile.role || 'USER',
        avatar_url: profile.avatar_url
      }));

      return { success: true, data: users };
    } catch (error) {
      console.error('Error fetching users:', error)
      return { success: false, error: error as Error }
    }
  },

  /**
   * 根据ID获取用户
   */
  async getUserById(id: string): Promise<{ success: boolean; data?: User | null; error?: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          avatar_url,
          role,
          level,
          flow_value,
          streak_days,
          posts_count,
          checkin_days,
          is_premium,
          is_active,
          created_at,
          updated_at,
          last_active_at,
          last_sign_in_at
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      if (!data) {
        return { success: false, error: new Error('User not found') };
      }

      // 转换数据格式以匹配 User 接口
      const user: User = {
        id: data.id,
        username: data.username || '未设置',
        email: '', // 需要从 auth.users 表获取，这里先留空
        phone: null,
        join_date: data.created_at,
        last_active: data.last_active_at || data.last_sign_in_at,
        level: data.level?.toString() || '1',
        flow_value: data.flow_value || 0,
        status: data.is_active ? 'active' : 'inactive',
        is_premium: data.is_premium || false,
        streak: data.streak_days || 0,
        posts_count: data.posts_count || 0,
        checkins_count: data.checkin_days || 0,
        created_at: data.created_at,
        updated_at: data.updated_at,
        role: data.role || 'USER',
        avatar_url: data.avatar_url
      };

      return { success: true, data: user };
    } catch (error) {
      console.error('Error fetching user:', error)
      return { success: false, error: error as Error }
    }
  },

  /**
   * 搜索用户
   */
  async searchUsers(query: string): Promise<{ success: boolean; data?: User[] | null; error?: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .or(`username.ilike.%${query}%,email.ilike.%${query}%`)

      if (error) throw error
      return { success: true, data: data as User[] }
    } catch (error) {
      console.error('Error searching users:', error)
      return { success: false, error: error as Error }
    }
  },

  /**
   * 更新用户
   */
  async updateUser(id: string, updates: Partial<User>): Promise<{ success: boolean; error?: Error | null }> {
    try {
      // 转换字段名以匹配数据库表结构
      const dbUpdates: any = {};
      if (updates.username !== undefined) dbUpdates.username = updates.username;
      if (updates.level !== undefined) dbUpdates.level = parseInt(updates.level);
      if (updates.flow_value !== undefined) dbUpdates.flow_value = updates.flow_value;
      if (updates.is_premium !== undefined) dbUpdates.is_premium = updates.is_premium;
      if (updates.status !== undefined) dbUpdates.is_active = updates.status === 'active';
      if (updates.streak !== undefined) dbUpdates.streak_days = updates.streak;
      if (updates.avatar_url !== undefined) dbUpdates.avatar_url = updates.avatar_url;
      if (updates.role !== undefined) dbUpdates.role = updates.role;

      const { error } = await supabase
        .from('profiles')
        .update(dbUpdates)
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      return { success: false, error: error as Error };
    }
  },



  /**
   * 获取用户统计
   */
  async getUserStats(): Promise<{ success: boolean; data?: any | null; error?: Error | null }> {
    try {
      // 获取总用户数
      const { count: totalUsers, error: countError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      if (countError) throw countError;

      // 获取今日新增用户数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const { count: newUsers, error: newUsersError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());

      if (newUsersError) throw newUsersError;

      // 获取活跃用户数（最近7天活跃）
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      let activeUsers = 0;
      {
        const { count, error } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .gte('last_active_at', sevenDaysAgo.toISOString());
        if (error) {
          const { count: count2, error: error2 } = await supabase
            .from('profiles')
            .select('*', { count: 'exact', head: true })
            .gte('last_sign_in_at', sevenDaysAgo.toISOString());
          if (!error2) activeUsers = count2 || 0;
        } else {
          activeUsers = count || 0;
        }
      }

      return {
        success: true,
        data: {
          totalUsers,
          newUsers,
          activeUsers,
        }
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 创建新用户
   */
  async createUser(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; data?: User | null; error?: Error | null }> {
    try {
      // 转换字段名以匹配数据库表结构
      const dbUserData = {
        username: userData.username,
        level: parseInt(userData.level),
        flow_value: userData.flow_value,
        is_premium: userData.is_premium,
        is_active: userData.status === 'active',
        streak_days: userData.streak,
        posts_count: userData.posts_count,
        checkin_days: userData.checkins_count,
        avatar_url: userData.avatar_url,
        role: userData.role || 'USER'
      };

      const { data, error } = await supabase
        .from('profiles')
        .insert([dbUserData])
        .select()
        .single();

      if (error) throw error;

      // 转换返回数据格式
      const user: User = {
        id: data.id,
        username: data.username || '未设置',
        email: '',
        phone: null,
        join_date: data.created_at,
        last_active: data.last_active_at,
        level: data.level?.toString() || '1',
        flow_value: data.flow_value || 0,
        status: data.is_active ? 'active' : 'inactive',
        is_premium: data.is_premium || false,
        streak: data.streak_days || 0,
        posts_count: data.posts_count || 0,
        checkins_count: data.checkin_days || 0,
        created_at: data.created_at,
        updated_at: data.updated_at,
        role: data.role || 'USER',
        avatar_url: data.avatar_url
      };

      return { success: true, data: user };
    } catch (error) {
      console.error('Error creating user:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 删除用户
   */
  async deleteUser(id: string): Promise<{ success: boolean; error?: Error | null }> {
    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 批量删除用户
   */
  async deleteUsers(ids: string[]): Promise<{ success: boolean; error?: Error | null }> {
    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .in('id', ids);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Error deleting users:', error);
      return { success: false, error: error as Error };
    }
  },

  /**
   * 批量更新用户状态
   */
  async batchUpdateStatus(ids: string[], status: 'active' | 'inactive' | 'banned'): Promise<{ success: boolean; error?: Error | null }> {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ is_active: status === 'active' })
        .in('id', ids);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Error batch updating status:', error);
      return { success: false, error: error as Error };
    }
  },
};