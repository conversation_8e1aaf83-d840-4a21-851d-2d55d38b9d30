// 风险评估相关接口和类型定义

export interface RiskRule {
  id: string
  name: string
  description: string
  category: 'security' | 'financial' | 'behavior' | 'content'
  severity: 'low' | 'medium' | 'high' | 'critical'
  conditions: RiskCondition[]
  actions: RiskAction[]
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
  created_by?: string
}

export interface RiskCondition {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'contains'
  value: any
  logic?: 'and' | 'or'
}

export interface RiskAction {
  type: 'block' | 'warn' | 'review' | 'limit' | 'notify'
  parameters?: Record<string, any>
}

export interface RiskEvent {
  id: string
  user_id: string
  rule_id: string
  event_type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  data: Record<string, any>
  status: 'pending' | 'reviewed' | 'resolved' | 'ignored'
  actions_taken: string[]
  created_at: string
  reviewed_at?: string
  reviewed_by?: string
  user?: {
    id: string
    username: string
    email: string
    avatar_url?: string
  }
  rule?: RiskRule
}

export interface UserRiskProfile {
  id: string
  user_id: string
  risk_level: 'low' | 'medium' | 'high' | 'critical'
  risk_score: number
  factors: RiskFactor[]
  last_assessment: string
  status: 'active' | 'monitoring' | 'restricted'
  created_at: string
  updated_at: string
  user?: {
    id: string
    username: string
    email: string
    avatar_url?: string
  }
}

export interface RiskFactor {
  category: string
  factor: string
  score: number
  weight: number
  description: string
}

export interface RiskStats {
  totalEvents: number
  pendingEvents: number
  highRiskUsers: number
  criticalEvents: number
  rulesCoverage: number
  avgResponseTime: number
}

export interface RiskAssessmentRequest {
  user_id: string
  event_type: string
  data: Record<string, any>
}

export interface RiskAssessmentResult {
  risk_level: 'low' | 'medium' | 'high' | 'critical'
  risk_score: number
  triggered_rules: string[]
  recommended_actions: RiskAction[]
  factors: RiskFactor[]
}

export interface ListParams {
  page?: number
  pageSize?: number
  search?: string
  category?: string
  severity?: string
  status?: string
  user_id?: string
  rule_id?: string
  dateRange?: [string, string]
}

export interface ListResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
}

class RiskService {
  private baseUrl = '/api/risk'

  // 风险规则管理
  async getRiskRules(params: ListParams = {}): Promise<ListResponse<RiskRule>> {
    // 模拟API调用
    const mockRules: RiskRule[] = [
      {
        id: '1',
        name: '异常登录检测',
        description: '检测用户异常登录行为，如异地登录、频繁登录失败等',
        category: 'security',
        severity: 'high',
        conditions: [
          { field: 'login_location', operator: 'ne', value: 'usual_location' },
          { field: 'failed_attempts', operator: 'gt', value: 5, logic: 'or' }
        ],
        actions: [
          { type: 'warn', parameters: { message: '检测到异常登录' } },
          { type: 'review', parameters: { priority: 'high' } }
        ],
        status: 'active',
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        created_by: 'admin'
      },
      {
        id: '2',
        name: '大额交易监控',
        description: '监控用户大额交易行为，防范洗钱风险',
        category: 'financial',
        severity: 'critical',
        conditions: [
          { field: 'transaction_amount', operator: 'gt', value: 10000 }
        ],
        actions: [
          { type: 'block', parameters: { reason: '大额交易需要审核' } },
          { type: 'notify', parameters: { recipients: ['<EMAIL>'] } }
        ],
        status: 'active',
        created_at: '2024-01-14T09:00:00Z',
        updated_at: '2024-01-14T09:00:00Z',
        created_by: 'admin'
      },
      {
        id: '3',
        name: '内容违规检测',
        description: '检测用户发布的违规内容',
        category: 'content',
        severity: 'medium',
        conditions: [
          { field: 'content_type', operator: 'in', value: ['spam', 'abuse', 'illegal'] }
        ],
        actions: [
          { type: 'review', parameters: { auto_hide: true } },
          { type: 'warn', parameters: { message: '内容可能违规' } }
        ],
        status: 'active',
        created_at: '2024-01-13T08:00:00Z',
        updated_at: '2024-01-13T08:00:00Z',
        created_by: 'admin'
      }
    ]

    // 模拟分页和筛选
    let filteredRules = mockRules
    if (params.search) {
      filteredRules = filteredRules.filter(rule => 
        rule.name.includes(params.search!) || 
        rule.description.includes(params.search!)
      )
    }
    if (params.category) {
      filteredRules = filteredRules.filter(rule => rule.category === params.category)
    }
    if (params.severity) {
      filteredRules = filteredRules.filter(rule => rule.severity === params.severity)
    }
    if (params.status) {
      filteredRules = filteredRules.filter(rule => rule.status === params.status)
    }

    const page = params.page || 1
    const pageSize = params.pageSize || 20
    const start = (page - 1) * pageSize
    const end = start + pageSize

    return {
      data: filteredRules.slice(start, end),
      total: filteredRules.length,
      page,
      pageSize
    }
  }

  async createRiskRule(rule: Omit<RiskRule, 'id' | 'created_at' | 'updated_at'>): Promise<RiskRule> {
    // 模拟创建
    const newRule: RiskRule = {
      ...rule,
      id: Date.now().toString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    return newRule
  }

  async updateRiskRule(id: string, updates: Partial<RiskRule>): Promise<RiskRule> {
    // 模拟更新
    const updatedRule: RiskRule = {
      id,
      name: '更新的规则',
      description: '更新的描述',
      category: 'security',
      severity: 'medium',
      conditions: [],
      actions: [],
      status: 'active',
      created_at: '2024-01-15T10:00:00Z',
      updated_at: new Date().toISOString(),
      ...updates
    }
    return updatedRule
  }

  async deleteRiskRule(id: string): Promise<void> {
    // 模拟删除
    console.log(`删除风险规则: ${id}`)
  }

  // 风险事件管理
  async getRiskEvents(params: ListParams = {}): Promise<ListResponse<RiskEvent>> {
    const mockEvents: RiskEvent[] = [
      {
        id: '1',
        user_id: 'user1',
        rule_id: '1',
        event_type: 'suspicious_login',
        severity: 'high',
        description: '用户从异常地点登录',
        data: {
          ip: '*************',
          location: '北京',
          device: 'iPhone 13',
          usual_location: '上海'
        },
        status: 'pending',
        actions_taken: ['warn'],
        created_at: '2024-01-15T14:30:00Z',
        user: {
          id: 'user1',
          username: 'testuser1',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar1.jpg'
        },
        rule: {
          id: '1',
          name: '异常登录检测',
          description: '检测用户异常登录行为',
          category: 'security',
          severity: 'high',
          conditions: [],
          actions: [],
          status: 'active',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        }
      },
      {
        id: '2',
        user_id: 'user2',
        rule_id: '2',
        event_type: 'large_transaction',
        severity: 'critical',
        description: '用户尝试进行大额交易',
        data: {
          amount: 15000,
          currency: 'CNY',
          recipient: 'unknown_account',
          transaction_type: 'transfer'
        },
        status: 'reviewed',
        actions_taken: ['block', 'notify'],
        created_at: '2024-01-15T13:15:00Z',
        reviewed_at: '2024-01-15T13:45:00Z',
        reviewed_by: 'admin',
        user: {
          id: 'user2',
          username: 'testuser2',
          email: '<EMAIL>'
        },
        rule: {
          id: '2',
          name: '大额交易监控',
          description: '监控用户大额交易行为',
          category: 'financial',
          severity: 'critical',
          conditions: [],
          actions: [],
          status: 'active',
          created_at: '2024-01-14T09:00:00Z',
          updated_at: '2024-01-14T09:00:00Z'
        }
      }
    ]

    // 模拟筛选
    let filteredEvents = mockEvents
    if (params.search) {
      filteredEvents = filteredEvents.filter(event => 
        event.description.includes(params.search!) ||
        event.user?.username.includes(params.search!) ||
        event.user?.email.includes(params.search!)
      )
    }
    if (params.severity) {
      filteredEvents = filteredEvents.filter(event => event.severity === params.severity)
    }
    if (params.status) {
      filteredEvents = filteredEvents.filter(event => event.status === params.status)
    }
    if (params.user_id) {
      filteredEvents = filteredEvents.filter(event => event.user_id === params.user_id)
    }

    const page = params.page || 1
    const pageSize = params.pageSize || 20
    const start = (page - 1) * pageSize
    const end = start + pageSize

    return {
      data: filteredEvents.slice(start, end),
      total: filteredEvents.length,
      page,
      pageSize
    }
  }

  async reviewRiskEvent(id: string, status: 'resolved' | 'ignored', notes?: string): Promise<void> {
    // 模拟审核
    console.log(`审核风险事件 ${id}: ${status}, 备注: ${notes}`)
  }

  // 用户风险档案管理
  async getUserRiskProfiles(params: ListParams = {}): Promise<ListResponse<UserRiskProfile>> {
    const mockProfiles: UserRiskProfile[] = [
      {
        id: '1',
        user_id: 'user1',
        risk_level: 'high',
        risk_score: 85,
        factors: [
          {
            category: 'behavior',
            factor: 'login_pattern',
            score: 30,
            weight: 0.3,
            description: '异常登录模式'
          },
          {
            category: 'financial',
            factor: 'transaction_volume',
            score: 25,
            weight: 0.25,
            description: '交易量异常'
          },
          {
            category: 'security',
            factor: 'device_trust',
            score: 30,
            weight: 0.45,
            description: '设备信任度低'
          }
        ],
        last_assessment: '2024-01-15T14:30:00Z',
        status: 'monitoring',
        created_at: '2024-01-10T10:00:00Z',
        updated_at: '2024-01-15T14:30:00Z',
        user: {
          id: 'user1',
          username: 'testuser1',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar1.jpg'
        }
      },
      {
        id: '2',
        user_id: 'user2',
        risk_level: 'critical',
        risk_score: 95,
        factors: [
          {
            category: 'financial',
            factor: 'large_transactions',
            score: 40,
            weight: 0.4,
            description: '频繁大额交易'
          },
          {
            category: 'behavior',
            factor: 'suspicious_activity',
            score: 35,
            weight: 0.35,
            description: '可疑行为模式'
          },
          {
            category: 'security',
            factor: 'account_compromise',
            score: 20,
            weight: 0.25,
            description: '账户可能被盗用'
          }
        ],
        last_assessment: '2024-01-15T13:15:00Z',
        status: 'restricted',
        created_at: '2024-01-08T09:00:00Z',
        updated_at: '2024-01-15T13:15:00Z',
        user: {
          id: 'user2',
          username: 'testuser2',
          email: '<EMAIL>'
        }
      }
    ]

    // 模拟筛选
    let filteredProfiles = mockProfiles
    if (params.search) {
      filteredProfiles = filteredProfiles.filter(profile => 
        profile.user?.username.includes(params.search!) ||
        profile.user?.email.includes(params.search!)
      )
    }

    const page = params.page || 1
    const pageSize = params.pageSize || 20
    const start = (page - 1) * pageSize
    const end = start + pageSize

    return {
      data: filteredProfiles.slice(start, end),
      total: filteredProfiles.length,
      page,
      pageSize
    }
  }

  async updateUserRiskStatus(userId: string, status: 'active' | 'monitoring' | 'restricted'): Promise<void> {
    // 模拟更新用户风险状态
    console.log(`更新用户 ${userId} 风险状态为: ${status}`)
  }

  // 风险评估
  async assessRisk(request: RiskAssessmentRequest): Promise<RiskAssessmentResult> {
    // 模拟风险评估
    const result: RiskAssessmentResult = {
      risk_level: 'medium',
      risk_score: 65,
      triggered_rules: ['rule1', 'rule3'],
      recommended_actions: [
        { type: 'review', parameters: { priority: 'medium' } },
        { type: 'warn', parameters: { message: '检测到中等风险' } }
      ],
      factors: [
        {
          category: 'behavior',
          factor: 'activity_pattern',
          score: 35,
          weight: 0.4,
          description: '活动模式异常'
        },
        {
          category: 'security',
          factor: 'device_trust',
          score: 30,
          weight: 0.6,
          description: '设备信任度'
        }
      ]
    }
    return result
  }

  // 统计数据
  async getRiskStats(): Promise<RiskStats> {
    return {
      totalEvents: 1247,
      pendingEvents: 23,
      highRiskUsers: 15,
      criticalEvents: 5,
      rulesCoverage: 87.5,
      avgResponseTime: 2.3
    }
  }
}

export const riskService = new RiskService()
export default riskService