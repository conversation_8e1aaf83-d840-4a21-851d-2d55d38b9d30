import React from 'react'
import { Outlet } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import GlobalMessage from './components/GlobalMessage'
import './App.css'

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <GlobalMessage>
        <Outlet />
      </GlobalMessage>
    </ConfigProvider>
  )
}

export default App