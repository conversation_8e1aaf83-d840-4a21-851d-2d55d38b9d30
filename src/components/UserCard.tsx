import React from 'react'
import { Card, Avatar, Space, Typography } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { useUserStore } from '../store/userStore'

const { Text, Title } = Typography

interface UserCardProps {
  style?: React.CSSProperties
}

const UserCard: React.FC<UserCardProps> = ({ style }) => {
  const { name, email } = useUserStore()

  return (
    <Card style={style}>
      <Space direction="vertical" align="center" style={{ width: '100%' }}>
        <Avatar size={64} icon={<UserOutlined />} />
        <Title level={4}>{name || '未登录'}</Title>
        <Text type="secondary">{email || '请先登录'}</Text>
      </Space>
    </Card>
  )
}

export default UserCard