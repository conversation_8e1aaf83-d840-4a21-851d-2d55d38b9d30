import React from 'react'
import { Space, Button, Tooltip } from 'antd'
import { 
  ReloadOutlined, 
  PlusOutlined, 
  SearchOutlined,
  ExportOutlined
} from '@ant-design/icons'

interface QuickActionsProps {
  onRefresh?: () => void
  onAdd?: () => void
  onSearch?: () => void
  onExport?: () => void
  showRefresh?: boolean
  showAdd?: boolean
  showSearch?: boolean
  showExport?: boolean
  refreshLoading?: boolean
  customActions?: React.ReactNode
}

const QuickActions: React.FC<QuickActionsProps> = ({
  onRefresh,
  onAdd,
  onSearch,
  onExport,
  showRefresh = true,
  showAdd = false,
  showSearch = false,
  showExport = false,
  refreshLoading = false,
  customActions
}) => {
  return (
    <Space wrap>
      {showRefresh && (
        <Tooltip title="刷新">
          <Button 
            icon={<ReloadOutlined />} 
            onClick={onRefresh}
            loading={refreshLoading}
          />
        </Tooltip>
      )}
      
      {showAdd && (
        <Tooltip title="新增">
          <Button 
            icon={<PlusOutlined />} 
            onClick={onAdd}
            type="primary"
          />
        </Tooltip>
      )}
      
      {showSearch && (
        <Tooltip title="搜索">
          <Button 
            icon={<SearchOutlined />} 
            onClick={onSearch}
          />
        </Tooltip>
      )}
      
      {showExport && (
        <Tooltip title="导出">
          <Button 
            icon={<ExportOutlined />} 
            onClick={onExport}
          />
        </Tooltip>
      )}
      
      {customActions}
    </Space>
  )
}

export default QuickActions