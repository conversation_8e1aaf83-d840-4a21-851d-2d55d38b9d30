import React, { useEffect } from 'react'
import { message, notification } from 'antd'
import type { MessageInstance, NotificationInstance } from 'antd/es/message/interface'
import type { NotificationPlacement } from 'antd/es/notification/interface'

export interface GlobalMessageContextProps {
  message: MessageInstance
  notification: NotificationInstance
}

export const GlobalMessageContext = React.createContext<GlobalMessageContextProps>({
  message,
  notification
})

interface GlobalMessageProps {
  children: React.ReactNode
}

const GlobalMessage: React.FC<GlobalMessageProps> = ({ children }) => {
  useEffect(() => {
    // 设置全局消息配置
    message.config({
      top: 80,
      duration: 3,
      maxCount: 3
    })
    
    notification.config({
      placement: 'topRight',
      top: 80,
      duration: 4.5
    })
  }, [])
  
  return (
    <GlobalMessageContext.Provider value={{ message, notification }}>
      {children}
    </GlobalMessageContext.Provider>
  )
}

export default GlobalMessage