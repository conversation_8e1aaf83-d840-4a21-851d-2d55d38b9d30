import React from 'react'
import { Breadcrumb } from 'antd'
import { useLocation } from 'react-router-dom'
import { useNavigation } from '../hooks/useNavigation'

/**
 * 导航面包屑组件
 * 根据当前路径自动生成面包屑导航
 */
export const NavigationBreadcrumb: React.FC = () => {
  const location = useLocation()
  const { getBreadcrumbs } = useNavigation()

  const breadcrumbs = getBreadcrumbs(location.pathname)

  if (breadcrumbs.length === 0) {
    return null
  }

  const items = breadcrumbs.map((item, index) => ({
    title: item.label,
    href: index === breadcrumbs.length - 1 ? undefined : item.path, // 最后一项不可点击
  }))

  return (
    <Breadcrumb 
      style={{ margin: '0 0 16px 0' }}
      items={items}
    />
  )
}

export default NavigationBreadcrumb