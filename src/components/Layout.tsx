import React, { useState } from 'react'
import { Layout as AntLayout, Menu, theme, Avatar, Dropdown, Space, Typography, Button } from 'antd'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  ProfileOutlined,
} from '@ant-design/icons'
import { useNavigate, useLocation, Outlet } from 'react-router-dom'
import { useAuth } from '../hooks/useAuth'
import { useUserStore } from '../store/userStore'
import { useNavigation } from '../hooks/useNavigation'
import { NavigationBreadcrumb } from './NavigationBreadcrumb'

const { Header, Content, Sider } = AntLayout
const { Text } = Typography

const Layout: React.FC = () => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken()
  
  const location = useLocation()
  const { user, logout } = useAuth()
  const { name } = useUserStore()
  const { menuItems } = useNavigation()
  const navigate = useNavigate()
  const [collapsed, setCollapsed] = useState(false)
  
  // 获取当前路径对应的菜单key，用于高亮显示
  const getSelectedKeys = () => {
    const path = location.pathname
    // 处理根路径
    if (path === '/') {
      return ['/']
    }
    return [path]
  }
  
  // 获取应该展开的子菜单keys
  const getOpenKeys = () => {
    const path = location.pathname
    const openKeys: string[] = []
    
    // 根据当前路径判断应该展开哪些父菜单
    if (path.startsWith('/articles') || path.startsWith('/content') || path.startsWith('/content-library')) {
      openKeys.push('content')
    } else if (path.startsWith('/checkin') || path.startsWith('/tasks') || path.startsWith('/challenges') || 
               path.startsWith('/achievements') || path.startsWith('/milestones')) {
      openKeys.push('engagement')
    } else if (path.startsWith('/analytics') || path.startsWith('/export')) {
      openKeys.push('analytics')
    } else if (path.startsWith('/notifications') || path.startsWith('/invites') || path.startsWith('/flow')) {
      openKeys.push('operations')
    } else if (path.startsWith('/appreciation') || path.startsWith('/subscription') || 
               path.startsWith('/partner') || path.startsWith('/payment')) {
      openKeys.push('business')
    } else if (path.startsWith('/admin') || path.startsWith('/logs') || path.startsWith('/settings')) {
      openKeys.push('admin')
    } else if (path.startsWith('/users')) {
      openKeys.push('users')
    }
    
    return openKeys
  }
  
  const goProfile = () => {
    navigate('/profile')
  }
  
  
  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人资料',
      onClick: goProfile,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ]

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        breakpoint="lg"
        collapsedWidth={80}
        onBreakpoint={(broken) => {
          console.log(broken)
        }}
        onCollapse={(collapsed, type) => {
          console.log(collapsed, type)
        }}
      >
        {/* Logo和系统名称区域 */}
        <div 
          style={{
            height: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'flex-start',
            padding: collapsed ? '0 8px' : '0 16px',
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
            marginBottom: '8px',
            transition: 'all 0.2s'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: collapsed ? '0' : '12px' }}>
            {/* Logo占位区域 */}
            <div 
              style={{
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '14px',
                flexShrink: 0
              }}
            >
              R
            </div>
            {/* 系统名称 */}
            {!collapsed && (
              <Text 
                style={{ 
                  color: 'white', 
                  fontSize: '16px', 
                  fontWeight: '600',
                  margin: 0,
                  whiteSpace: 'nowrap'
                }}
              >
                Rebase Admin
              </Text>
            )}
          </div>
        </div>
        
        <Menu 
          theme="dark" 
          mode="inline" 
          selectedKeys={getSelectedKeys()} 
          defaultOpenKeys={getOpenKeys()} // 根据当前路径智能展开父菜单
          items={menuItems}
          style={{
            borderRight: 0,
          }}
        />
      </Sider>
      <AntLayout>
        <Header 
          style={{ 
            padding: '0 24px 0 0', 
            background: colorBgContainer, 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            borderBottom: '1px solid #f0f0f0'
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
            <Space 
              style={{ 
                cursor: 'pointer',
              }}
            >
              <Avatar 
                icon={<UserOutlined />} 
              />
              <Text strong style={{ color: '#262626' }}>
                {name || user?.email || '用户'}
              </Text>
            </Space>
          </Dropdown>
        </Header>
        <Content style={{ margin: '24px 16px 0' }}>
          {/* 面包屑导航 */}
          <NavigationBreadcrumb />
          <div
            style={{
              padding: 24,
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            <Outlet />
          </div>
        </Content>
      </AntLayout>
    </AntLayout>
  )
}

export default Layout