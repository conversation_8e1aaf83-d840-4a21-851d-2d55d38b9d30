import React from 'react'
import { Space, Input, Select, But<PERSON>, DatePicker, Row, Col } from 'antd'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons'

const { RangePicker } = DatePicker
const { Option } = Select

interface SearchFilterProps {
  onSearch: () => void
  onRefresh: () => void
  placeholder?: string
  filters?: Array<{
    key: string
    label: string
    type: 'select' | 'input' | 'dateRange'
    options?: Array<{ value: string; label: string }>
  }>
  onFilterChange?: (key: string, value: any) => void
  loading?: boolean
}

const SearchFilter: React.FC<SearchFilterProps> = ({
  onSearch,
  onRefresh,
  placeholder = '搜索...',
  filters = [],
  onFilterChange,
  loading = false
}) => {
  return (
    <Row gutter={[16, 16]} justify="space-between">
      <Col xs={24} sm={24} md={18} lg={18} xl={18}>
        <Space wrap>
          <Input 
            placeholder={placeholder} 
            prefix={<SearchOutlined />} 
            style={{ width: 250 }} 
            onPressEnter={onSearch}
          />
          {filters.map(filter => {
            switch (filter.type) {
              case 'select':
                return (
                  <Select
                    key={filter.key}
                    placeholder={filter.label}
                    style={{ width: 120 }}
                    allowClear
                    onChange={(value) => onFilterChange && onFilterChange(filter.key, value)}
                  >
                    {filter.options?.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                )
              case 'dateRange':
                return (
                  <RangePicker 
                    key={filter.key}
                    placeholder={[`${filter.label}开始`, `${filter.label}结束`]}
                    onChange={(dates) => onFilterChange && onFilterChange(filter.key, dates)}
                  />
                )
              default:
                return null
            }
          })}
          <Button type="primary" onClick={onSearch}>搜索</Button>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={onRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </Col>
    </Row>
  )
}

export default SearchFilter