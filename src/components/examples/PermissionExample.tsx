import React from 'react'
import { <PERSON>ton, Card, Space, Tag, Divider } from 'antd'
import { PermissionGuard, AdminGuard, ModeratorGuard, PremiumGuard } from '../PermissionGuard'
import { usePermissions } from '../../hooks/usePermissions'

/**
 * 权限系统使用示例组件
 * 展示如何在实际应用中使用权限检查
 */
export const PermissionExample: React.FC = () => {
  const {
    userRoles,
    userPermissions,
    canRead,
    canWrite,
    canDelete,
    canManage,
    isAdmin,
    isModerator,
    isPremium,
    canAccessAdminPanel,
    canManageUsers,
    canManageArticles,
  } = usePermissions()

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">权限系统示例</h1>
      
      {/* 用户角色和权限信息 */}
      <Card title="当前用户权限信息" className="mb-6">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">用户角色：</h3>
            <Space wrap>
              {userRoles.map((role, index) => (
                <Tag key={index} color="blue">{role.role}</Tag>
              ))}
              {userRoles.length === 0 && <Tag>无特殊角色</Tag>}
            </Space>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">用户权限：</h3>
            <Space wrap>
              {userPermissions.map((permission, index) => (
                <Tag key={index} color="green">{permission.permission}</Tag>
              ))}
              {userPermissions.length === 0 && <Tag>无特殊权限</Tag>}
            </Space>
          </div>
        </div>
      </Card>

      {/* 角色检查示例 */}
      <Card title="角色检查示例" className="mb-6">
        <Space direction="vertical" className="w-full">
          <AdminGuard fallback={<Tag color="red">您不是管理员</Tag>}>
            <Tag color="green">✓ 您是管理员</Tag>
          </AdminGuard>
          
          <ModeratorGuard fallback={<Tag color="red">您不是版主</Tag>}>
            <Tag color="green">✓ 您是版主或管理员</Tag>
          </ModeratorGuard>
          
          <PremiumGuard fallback={<Tag color="red">您不是高级用户</Tag>}>
            <Tag color="green">✓ 您是高级用户</Tag>
          </PremiumGuard>
        </Space>
      </Card>

      {/* 权限检查示例 */}
      <Card title="权限检查示例" className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium mb-2">文章管理权限：</h4>
            <Space direction="vertical">
              <Tag color={canRead('articles') ? 'green' : 'red'}>
                {canRead('articles') ? '✓' : '✗'} 读取文章
              </Tag>
              <Tag color={canWrite('articles') ? 'green' : 'red'}>
                {canWrite('articles') ? '✓' : '✗'} 编写文章
              </Tag>
              <Tag color={canDelete('articles') ? 'green' : 'red'}>
                {canDelete('articles') ? '✓' : '✗'} 删除文章
              </Tag>
              <Tag color={canManage('articles') ? 'green' : 'red'}>
                {canManage('articles') ? '✓' : '✗'} 管理文章
              </Tag>
            </Space>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">用户管理权限：</h4>
            <Space direction="vertical">
              <Tag color={canRead('users') ? 'green' : 'red'}>
                {canRead('users') ? '✓' : '✗'} 查看用户
              </Tag>
              <Tag color={canWrite('users') ? 'green' : 'red'}>
                {canWrite('users') ? '✓' : '✗'} 编辑用户
              </Tag>
              <Tag color={canDelete('users') ? 'green' : 'red'}>
                {canDelete('users') ? '✓' : '✗'} 删除用户
              </Tag>
              <Tag color={canManage('users') ? 'green' : 'red'}>
                {canManage('users') ? '✓' : '✗'} 管理用户
              </Tag>
            </Space>
          </div>
        </div>
      </Card>

      {/* 条件渲染示例 */}
      <Card title="条件渲染示例" className="mb-6">
        <Space direction="vertical" className="w-full">
          <PermissionGuard
            requiredPermissions={['articles.write']}
            fallback={<div className="text-gray-500">您没有权限创建文章</div>}
          >
            <Button type="primary">创建新文章</Button>
          </PermissionGuard>
          
          <PermissionGuard
            requiredPermissions={['users.manage']}
            fallback={<div className="text-gray-500">您没有权限管理用户</div>}
          >
            <Button type="primary">用户管理</Button>
          </PermissionGuard>
          
          <PermissionGuard
            requiredRoles={['ADMIN']}
            fallback={<div className="text-gray-500">仅管理员可见</div>}
          >
            <Button danger>系统设置</Button>
          </PermissionGuard>
        </Space>
      </Card>

      {/* 组合权限检查示例 */}
      <Card title="组合权限检查示例">
        <Space direction="vertical" className="w-full">
          <div>
            <h4 className="font-medium mb-2">管理面板访问：</h4>
            <Tag color={canAccessAdminPanel() ? 'green' : 'red'}>
              {canAccessAdminPanel() ? '✓ 可以访问' : '✗ 无法访问'}
            </Tag>
          </div>
          
          <Divider />
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h5 className="font-medium mb-2">用户管理：</h5>
              <Tag color={canManageUsers() ? 'green' : 'red'}>
                {canManageUsers() ? '✓ 可以管理' : '✗ 无法管理'}
              </Tag>
            </div>
            
            <div>
              <h5 className="font-medium mb-2">文章管理：</h5>
              <Tag color={canManageArticles() ? 'green' : 'red'}>
                {canManageArticles() ? '✓ 可以管理' : '✗ 无法管理'}
              </Tag>
            </div>
            
            <div>
              <h5 className="font-medium mb-2">角色检查：</h5>
              <Space direction="vertical">
                <Tag color={isAdmin() ? 'green' : 'red'}>
                  {isAdmin() ? '✓' : '✗'} 管理员
                </Tag>
                <Tag color={isModerator() ? 'green' : 'red'}>
                  {isModerator() ? '✓' : '✗'} 版主
                </Tag>
                <Tag color={isPremium() ? 'green' : 'red'}>
                  {isPremium() ? '✓' : '✗'} 高级用户
                </Tag>
              </Space>
            </div>
          </div>
        </Space>
      </Card>
    </div>
  )
}

export default PermissionExample