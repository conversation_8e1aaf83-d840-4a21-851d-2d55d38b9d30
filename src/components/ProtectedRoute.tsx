import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePermissions } from '../hooks/usePermissions';
import { Spin } from 'antd';

interface ProtectedRouteProps {
  children: React.ReactNode;
  // 需要的角色（任一匹配即可）
  requiredRoles?: string[];
  // 需要的权限（任一匹配即可）
  requiredPermissions?: string[];
  // 是否需要所有角色都匹配
  requireAllRoles?: boolean;
  // 是否需要所有权限都匹配
  requireAllPermissions?: boolean;
  // 无权限时重定向的路径
  unauthorizedRedirect?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requireAllRoles = false,
  requireAllPermissions = false,
  unauthorizedRedirect = '/unauthorized'
}) => {
  const { isLoading, isLoggedIn } = useAuth();
  const { hasRole, hasPermission } = usePermissions();
  const location = useLocation();

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (!isLoggedIn) {
    // 重定向到登录页面，并保存当前位置以便登录后返回
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 检查角色权限
  if (requiredRoles.length > 0) {
    const hasRequiredRoles = requireAllRoles
      ? requiredRoles.every(role => hasRole(role))
      : requiredRoles.some(role => hasRole(role));
    
    if (!hasRequiredRoles) {
      return <Navigate to={unauthorizedRedirect} replace />;
    }
  }

  // 检查操作权限
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAllPermissions
      ? requiredPermissions.every(permission => hasPermission(permission))
      : requiredPermissions.some(permission => hasPermission(permission));
    
    if (!hasRequiredPermissions) {
      return <Navigate to={unauthorizedRedirect} replace />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;