import React from 'react'
import { usePermissions } from '../hooks/usePermissions'
import { useUserStore } from '../store/userStore'

interface PermissionGuardProps {
  children: React.ReactNode
  // 需要的角色（任一匹配即可）
  requiredRoles?: string[]
  // 需要的权限（任一匹配即可）
  requiredPermissions?: string[]
  // 是否需要所有角色都匹配
  requireAllRoles?: boolean
  // 是否需要所有权限都匹配
  requireAllPermissions?: boolean
  // 无权限时显示的内容
  fallback?: React.ReactNode
  // 无权限时的回调
  onUnauthorized?: () => void
}

/**
 * 权限守卫组件
 * 根据用户角色和权限控制内容显示
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requireAllRoles = false,
  requireAllPermissions = false,
  fallback = null,
  onUnauthorized,
}) => {
  const { hasRole, hasPermission } = usePermissions()
  const { isLoggedIn } = useUserStore()

  // 如果用户未登录，直接返回 fallback
  if (!isLoggedIn) {
    onUnauthorized?.()
    return <>{fallback}</>
  }

  // 检查角色权限
  let hasRequiredRoles = true
  if (requiredRoles.length > 0) {
    if (requireAllRoles) {
      hasRequiredRoles = requiredRoles.every(role => hasRole(role))
    } else {
      hasRequiredRoles = requiredRoles.some(role => hasRole(role))
    }
  }

  // 检查操作权限
  let hasRequiredPermissions = true
  if (requiredPermissions.length > 0) {
    if (requireAllPermissions) {
      hasRequiredPermissions = requiredPermissions.every(permission => hasPermission(permission))
    } else {
      hasRequiredPermissions = requiredPermissions.some(permission => hasPermission(permission))
    }
  }

  // 如果没有足够的权限
  if (!hasRequiredRoles || !hasRequiredPermissions) {
    onUnauthorized?.()
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * 管理员权限守卫
 */
export const AdminGuard: React.FC<{
  children: React.ReactNode
  fallback?: React.ReactNode
}> = ({ children, fallback }) => (
  <PermissionGuard
    requiredRoles={['ADMIN']}
    fallback={fallback}
  >
    {children}
  </PermissionGuard>
)

/**
 * 版主权限守卫
 */
export const ModeratorGuard: React.FC<{
  children: React.ReactNode
  fallback?: React.ReactNode
}> = ({ children, fallback }) => (
  <PermissionGuard
    requiredRoles={['ADMIN', 'MODERATOR']}
    fallback={fallback}
  >
    {children}
  </PermissionGuard>
)

/**
 * 高级用户权限守卫
 */
export const PremiumGuard: React.FC<{
  children: React.ReactNode
  fallback?: React.ReactNode
}> = ({ children, fallback }) => (
  <PermissionGuard
    requiredRoles={['ADMIN', 'MODERATOR', 'PREMIUM_USER', 'VIP_USER']}
    fallback={fallback}
  >
    {children}
  </PermissionGuard>
)

export default PermissionGuard