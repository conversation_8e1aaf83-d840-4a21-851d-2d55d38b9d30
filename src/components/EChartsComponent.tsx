import React, { useEffect, useRef, useState } from 'react'
import { Spin } from 'antd'
import type { ECharts, EChartsOption } from 'echarts'

interface EChartsComponentProps {
  option: EChartsOption
  height?: number | string
  width?: number | string
  loading?: boolean
  theme?: string
  onChartReady?: (chart: ECharts) => void
}

const EChartsComponent: React.FC<EChartsComponentProps> = ({
  option,
  height = 300,
  width = '100%',
  loading = false,
  theme = 'default',
  onChartReady
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<ECharts | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    let mounted = true
    let resizeHandler: (() => void) | null = null
    let currentChart: ECharts | null = null

    const initChart = async () => {
      try {
        // 动态导入 echarts，实现按需加载
        const echarts = await import('echarts')
        
        if (!mounted || !chartRef.current) return

        // 如果已存在图表实例，先销毁
        if (chartInstance.current && !chartInstance.current.isDisposed()) {
          chartInstance.current.dispose()
        }
        chartInstance.current = null

        // 创建新的图表实例
        currentChart = echarts.init(chartRef.current, theme)
        chartInstance.current = currentChart
        
        // 设置图表配置
        currentChart.setOption(option)
        
        // 监听窗口大小变化
        resizeHandler = () => {
          if (currentChart && !currentChart.isDisposed()) {
            currentChart.resize()
          }
        }
        window.addEventListener('resize', resizeHandler)

        // 回调函数
        if (onChartReady && currentChart) {
          onChartReady(currentChart)
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Failed to load ECharts:', error)
        setIsLoading(false)
      }
    }

    initChart()

    return () => {
      mounted = false
      if (resizeHandler) {
        window.removeEventListener('resize', resizeHandler)
        resizeHandler = null
      }
      if (currentChart && !currentChart.isDisposed()) {
        currentChart.dispose()
      }
      if (chartInstance.current && !chartInstance.current.isDisposed()) {
        chartInstance.current.dispose()
      }
      chartInstance.current = null
    }
  }, [theme, onChartReady])

  // 更新图表配置
  useEffect(() => {
    if (chartInstance.current && option) {
      chartInstance.current.setOption(option, true)
    }
  }, [option])

  return (
    <div 
      style={{ 
        width, 
        height,
        position: 'relative'
      }}
    >
      <div 
        ref={chartRef}
        style={{
          width: '100%',
          height: '100%'
        }}
      />
      {(isLoading || loading) && (
        <div 
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 1000
          }}
        >
          <Spin size="large" />
        </div>
      )}
    </div>
  )
}

export default EChartsComponent