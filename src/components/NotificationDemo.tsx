import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Card, List, Badge, Space, message, Input, Select } from 'antd'
import { BellOutlined, SendOutlined } from '@ant-design/icons'
import { notificationService } from '../services/notificationService'

const { Option } = Select

interface DemoNotification {
  id: string
  title: string
  content: string
  type: 'system' | 'achievement' | 'task' | 'user'
  is_read: boolean
  created_at: string
}

const NotificationDemo: React.FC = () => {
  const [notifications, setNotifications] = useState<DemoNotification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [newNotification, setNewNotification] = useState({
    title: '',
    content: '',
    type: 'system' as 'system' | 'achievement' | 'task' | 'user',
    user_id: ''
  })

  // 加载通知
  const loadNotifications = async () => {
    try {
      setLoading(true)
      const result = await notificationService.getAllNotifications()
      if (result.success && result.data) {
        const formattedNotifications = result.data.map(item => ({
          id: item.id,
          title: item.title,
          content: item.content,
          type: item.type,
          is_read: item.is_read,
          created_at: item.created_at
        }))
        setNotifications(formattedNotifications)
        
        // 计算未读数量
        const unread = formattedNotifications.filter(n => !n.is_read).length
        setUnreadCount(unread)
      }
    } catch (error) {
      console.error('加载通知失败:', error)
      message.error('加载通知失败')
    } finally {
      setLoading(false)
    }
  }

  // 创建新通知
  const createNotification = async () => {
    if (!newNotification.title || !newNotification.content || !newNotification.user_id) {
      message.error('请填写完整的通知信息')
      return
    }

    try {
      const result = await notificationService.createNotification({
        title: newNotification.title,
        content: newNotification.content,
        type: newNotification.type,
        user_id: newNotification.user_id,
        is_read: false
      })

      if (result.success) {
        message.success('通知创建成功')
        setNewNotification({ title: '', content: '', type: 'system', user_id: '' })
        await loadNotifications()
      } else {
        message.error(result.error || '创建通知失败')
      }
    } catch (error) {
      console.error('创建通知失败:', error)
      message.error('创建通知失败')
    }
  }

  // 标记为已读
  const markAsRead = async (id: string) => {
    try {
      const result = await notificationService.markAsRead(id)
      if (result.success) {
        message.success('已标记为已读')
        await loadNotifications()
      } else {
        message.error('操作失败')
      }
    } catch (error) {
      console.error('标记已读失败:', error)
      message.error('操作失败')
    }
  }

  // 设置实时订阅
  useEffect(() => {
    loadNotifications()

    // 订阅所有通知的实时更新
    const channel = notificationService.subscribeToAllNotifications((payload) => {
      console.log('收到实时通知更新:', payload)
      // 重新加载通知列表
      loadNotifications()
    })

    return () => {
      if (channel) {
        notificationService.unsubscribe(channel)
      }
    }
  }, [])

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'system': return '#1890ff'
      case 'achievement': return '#52c41a'
      case 'task': return '#faad14'
      case 'user': return '#722ed1'
      default: return '#1890ff'
    }
  }

  const getTypeName = (type: string) => {
    switch (type) {
      case 'system': return '系统通知'
      case 'achievement': return '成就通知'
      case 'task': return '任务通知'
      case 'user': return '用户通知'
      default: return '未知类型'
    }
  }

  return (
    <div style={{ padding: '20px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 通知统计 */}
        <Card title={<><BellOutlined /> 实时通知演示</>} extra={<Badge count={unreadCount} />}>
          <Space>
            <Button onClick={loadNotifications} loading={loading}>刷新通知</Button>
            <span>总通知数: {notifications.length}</span>
            <span>未读: {unreadCount}</span>
          </Space>
        </Card>

        {/* 创建通知 */}
        <Card title="创建新通知">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Input
              placeholder="通知标题"
              value={newNotification.title}
              onChange={(e) => setNewNotification({ ...newNotification, title: e.target.value })}
            />
            <Input.TextArea
              placeholder="通知内容"
              value={newNotification.content}
              onChange={(e) => setNewNotification({ ...newNotification, content: e.target.value })}
              rows={3}
            />
            <Space>
              <Select
                value={newNotification.type}
                onChange={(value) => setNewNotification({ ...newNotification, type: value })}
                style={{ width: 120 }}
              >
                <Option value="system">系统通知</Option>
                <Option value="achievement">成就通知</Option>
                <Option value="task">任务通知</Option>
                <Option value="user">用户通知</Option>
              </Select>
              <Input
                placeholder="接收用户ID"
                value={newNotification.user_id}
                onChange={(e) => setNewNotification({ ...newNotification, user_id: e.target.value })}
                style={{ width: 200 }}
              />
              <Button type="primary" icon={<SendOutlined />} onClick={createNotification}>
                发送通知
              </Button>
            </Space>
          </Space>
        </Card>

        {/* 通知列表 */}
        <Card title="通知列表">
          <List
            loading={loading}
            dataSource={notifications}
            renderItem={(item) => (
              <List.Item
                actions={[
                  !item.is_read && (
                    <Button size="small" onClick={() => markAsRead(item.id)}>
                      标记已读
                    </Button>
                  )
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  title={
                    <Space>
                      <Badge 
                        color={getTypeColor(item.type)} 
                        text={getTypeName(item.type)}
                      />
                      <span style={{ opacity: item.is_read ? 0.6 : 1 }}>
                        {item.title}
                      </span>
                      {!item.is_read && <Badge status="processing" />}
                    </Space>
                  }
                  description={
                    <div style={{ opacity: item.is_read ? 0.6 : 1 }}>
                      <div>{item.content}</div>
                      <small>{new Date(item.created_at).toLocaleString()}</small>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      </Space>
    </div>
  )
}

export default NotificationDemo