# Rebase 管理后台

为 [Rebase](../rebase) 项目开发的管理后台，专注于传播正能量的移动优先应用的管理功能。

## 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **组件库**: antd
- **状态管理**: Zustand
- **图标库**: @ant-design/icons
- **路由**: React Router
- **数据库**: Supabase

## 功能模块

- 📊 仪表盘 - 显示关键指标和统计数据
- 🧑‍💼 用户管理 - 管理平台用户、权限和角色
- 📝 内容管理 - 管理文章、帖子、评论等内容
- 👥 社区管理 - 管理社区圈子、话题和互动
- ✅ 打卡系统 - 查看用户打卡记录和统计
- 🎯 任务系统 - 管理用户任务和完成情况
- 🏆 成就系统 - 管理用户等级和成就
- 🚩 挑战管理 - 管理用户参与的挑战活动
- 👥 邀请管理 - 管理用户邀请码和邀请记录
- 🔔 通知管理 - 管理系统通知和消息推送
- 🔐 权限管理 - 管理后台管理员和权限
- 📜 操作日志 - 记录管理员操作日志
- 📈 数据统计 - 用户数据、内容数据和互动数据统计
- ⚙️ 系统配置 - 系统参数设置和配置管理

## 项目结构

```
src/
├── components/     # 通用组件
├── pages/          # 页面组件
│   ├── dashboard/  # 仪表盘
│   ├── users/      # 用户管理
│   ├── content/    # 内容管理
│   ├── community/  # 社区管理
│   ├── checkin/    # 打卡系统
│   ├── tasks/      # 任务系统
│   ├── achievements/ # 成就系统
│   ├── challenges/ # 挑战管理
│   ├── invites/    # 邀请管理
│   ├── notifications/ # 通知管理
│   ├── admin/      # 权限管理和操作日志
│   ├── analytics/  # 数据统计
│   └── settings/   # 系统配置
├── hooks/          # 自定义Hooks
├── store/          # Zustand状态管理
├── routes/         # 路由配置
├── services/       # API服务
├── types/          # TypeScript类型定义
├── utils/          # 工具函数
├── App.tsx         # 根组件
└── main.tsx        # 入口文件
```

## 已完成功能

### 仪表盘
- 显示关键指标（用户数、内容数等）
- 用户增长趋势图表
- 最新用户列表

### 用户管理
- 用户列表展示（包含状态、等级、心流值等信息）
- 用户搜索和筛选功能
- 新增用户模态框
- 用户封禁/解封、VIP状态管理
- 用户详细信息查看

### 内容管理
- 帖子列表展示（包含状态、分类等信息）
- 内容搜索和筛选功能
- 发布内容模态框
- 内容审核（通过/拒绝）
- 评论管理
- 精选内容设置

### 社区管理
- 社区内容列表展示
- 内容分类和状态管理
- 精选内容设置
- 内容审核功能

### 打卡系统
- 打卡记录展示（心情、能量、喝水、运动、睡眠等）
- 健康评分和心流值展示
- 统计信息展示

### 任务系统
- 任务列表展示（包含优先级、状态等信息）
- 任务搜索和筛选功能
- 新增任务模态框

### 成就系统
- 成就列表展示（包含稀有度、积分等信息）
- 成就搜索和筛选功能
- 新增成就模态框
- 用户成就详情查看
- 成就统计分析

### 挑战管理
- 挑战列表展示（包含状态、分类等信息）
- 挑战搜索和筛选功能
- 创建挑战模态框
- 挑战参与者管理
- 挑战进度跟踪
- 挑战统计分析

### 邀请管理
- 邀请码列表展示（包含使用状态、过期时间等）
- 邀请码搜索和筛选功能
- 生成邀请码模态框
- 邀请记录管理
- 邀请统计分析

### 通知管理
- 通知列表展示（包含类型、状态等信息）
- 通知搜索和筛选功能
- 发送通知模态框
- 通知模板管理
- 通知统计分析

### 权限管理
- 管理员账户管理
- 角色和权限分配
- 账户状态管理
- 管理员操作审计

### 操作日志
- 记录所有管理员操作
- 操作类型和状态筛选
- IP地址追踪
- 日志清空功能

### 数据统计
- 关键指标统计和趋势展示
- 用户等级分布
- 心流值分布
- 用户行为分析
- 留存率和活跃度统计
- 图表可视化展示

### 系统配置
- 基本站点设置
- 用户注册和内容管理设置
- 用户等级详细设置
- 成就解锁条件配置
- 通知设置
- 系统状态监控

## 启动项目

```bash
npm install
npm run dev
```

访问 http://localhost:5273 查看应用

## 管理员登录

管理员账号进行测试：

- 邮箱： <EMAIL>
- 密码： admin123

## 数据库配置

项目已连接到 Supabase 数据库，包含完整的表结构和权限系统：

- **数据库**: Supabase PostgreSQL
- **认证**: Supabase Auth
- **权限控制**: 基于角色的权限系统 (RBAC)
- **安全策略**: 行级安全策略 (RLS) 已配置

### 主要数据表

- `profiles` - 用户资料信息
- `posts` - 帖子内容
- `articles` - 文章内容
- `notifications` - 通知消息
- `user_roles` - 用户角色分配
- `role_permissions` - 角色权限配置
- `checkin_records` - 打卡记录
- `achievements` - 成就系统
- `tasks` - 任务系统
- `challenges` - 挑战活动
- `invite_codes` - 邀请码管理
- `orders` - 订单管理
- `wallets` - 钱包系统

### 权限系统

系统支持以下角色：
- `USER` - 普通用户
- `MEMBER` - 会员用户
- `PARTNER` - 合作伙伴
- `ADMIN` - 管理员
- `SUPER_ADMIN` - 超级管理员

每个角色都有对应的权限配置，支持细粒度的资源访问控制。