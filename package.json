{"name": "rebase-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@supabase/supabase-js": "^2.57.0", "@types/react-router-dom": "^5.3.3", "antd": "^5.27.2", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "moment": "^2.30.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-quill": "^2.0.0", "react-router-dom": "^7.8.2", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}